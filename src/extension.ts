import * as vscode from 'vscode';
import { AICodingAssistant } from './core/AICodingAssistant';
import { ConfigurationManager } from './services/configurationManager';
import { AIProviderFactory } from './services/aiProviderFactory';
import { AugmentWebviewProvider } from './webviews/AugmentWebviewProvider';
import { AgentSystem } from './core/AgentSystem';
import { CompletionProvider } from './providers/completionProvider';

// Global instances
let aiCodingAssistant: AICodingAssistant;
let configManager: ConfigurationManager;
let webviewProvider: AugmentWebviewProvider;
let agentSystem: AgentSystem;
let completionProvider: CompletionProvider;
let outputChannel: vscode.OutputChannel;
let statusBarItem: vscode.StatusBarItem;

export async function activate(context: vscode.ExtensionContext) {
    console.log('AI Coding Assistant is now active!');

    // Initialize output channel
    outputChannel = vscode.window.createOutputChannel('AI Coding Assistant');
    context.subscriptions.push(outputChannel);

    // Initialize configuration manager
    configManager = new ConfigurationManager();

    // Initialize main AI coding assistant
    aiCodingAssistant = new AICodingAssistant(configManager, outputChannel);
    await aiCodingAssistant.initialize();

    // Initialize agent system with SWEBench tools
    agentSystem = new AgentSystem(aiCodingAssistant, outputChannel);

    // Initialize webview provider with Augment UI
    webviewProvider = new AugmentWebviewProvider(context, aiCodingAssistant, agentSystem);

    // Initialize completion provider (temporarily disabled due to interface mismatch)
    // completionProvider = new CompletionProvider(aiCodingAssistant, configManager);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            AugmentWebviewProvider.viewType,
            webviewProvider
        )
    );

    // Register completion provider (temporarily disabled)
    // context.subscriptions.push(
    //     vscode.languages.registerInlineCompletionItemProvider(
    //         { pattern: '**' },
    //         completionProvider
    //     )
    // );

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'ai-assistant.selectProvider';
    statusBarItem.tooltip = 'AI Coding Assistant - Click to select provider';
    updateStatusBar();
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);

    // Register commands
    const commands = [
        // Main commands
        vscode.commands.registerCommand('ai-assistant.openChat', () => {
            webviewProvider.show();
        }),

        vscode.commands.registerCommand('ai-assistant.selectProvider', async () => {
            await selectProvider();
        }),

        vscode.commands.registerCommand('ai-assistant.selectModel', async () => {
            await selectModel();
        }),

        vscode.commands.registerCommand('ai-assistant.configureGemini', async () => {
            await configureGeminiApiKey();
        }),

        vscode.commands.registerCommand('ai-assistant.validateProvider', async () => {
            await validateProvider();
        }),

        vscode.commands.registerCommand('ai-assistant.clearHistory', () => {
            webviewProvider.clearHistory();
            vscode.window.showInformationMessage('Chat history cleared!');
        }),

        // Mode switching commands
        vscode.commands.registerCommand('ai-assistant.switchToChat', () => {
            webviewProvider.switchMode('chat');
        }),

        vscode.commands.registerCommand('ai-assistant.switchToAgent', () => {
            webviewProvider.switchMode('agent');
        }),

        vscode.commands.registerCommand('ai-assistant.switchToAuto', () => {
            webviewProvider.switchMode('auto');
        })
    ];

    // Register all commands
    context.subscriptions.push(...commands);

    // Initialize and show welcome message
    try {
        const providerType = configManager.getAiProvider();
        const providerName = providerType === 'ollama' ? 'Ollama' : 'Google Gemini';
        outputChannel.appendLine(`AI Coding Assistant activated with ${providerName} provider`);
        vscode.window.showInformationMessage(`🤖 AI Coding Assistant is ready with ${providerName}!`);
    } catch (error) {
        outputChannel.appendLine(`Error during initialization: ${error}`);
        vscode.window.showErrorMessage(`Failed to initialize AI provider: ${error}`);
    }
}

// Helper functions
async function selectProvider(): Promise<void> {
    const providers = [
        { label: '🦙 Ollama (Local)', value: 'ollama' },
        { label: '✨ Google Gemini (Cloud)', value: 'gemini' }
    ];

    const selected = await vscode.window.showQuickPick(providers, {
        placeHolder: 'Select AI Provider'
    });

    if (selected) {
        if (selected.value === 'gemini' && !configManager.getGeminiApiKey()) {
            const configure = await vscode.window.showInformationMessage(
                'Gemini API key is required. Would you like to configure it now?',
                'Configure',
                'Cancel'
            );

            if (configure === 'Configure') {
                await configureGeminiApiKey();
            } else {
                return;
            }
        }

        await configManager.setAiProvider(selected.value as 'ollama' | 'gemini');
        await aiCodingAssistant.switchProvider(selected.value as 'ollama' | 'gemini');

        updateStatusBar();
        vscode.window.showInformationMessage(`🔄 Switched to ${selected.label}`);
    }
}

async function selectModel(): Promise<void> {
    const providerType = configManager.getAiProvider();

    if (providerType !== 'ollama') {
        vscode.window.showInformationMessage('Model selection is only available for Ollama provider');
        return;
    }

    try {
        const models = await aiCodingAssistant.getAvailableModels();

        if (models.length === 0) {
            vscode.window.showWarningMessage('No models available. Please ensure Ollama is running and models are installed.');
            return;
        }

        const modelItems = models.map(model => ({ label: model }));
        const selected = await vscode.window.showQuickPick(modelItems, {
            placeHolder: 'Select an Ollama model'
        });

        if (selected) {
            await configManager.setDefaultModel(selected.label);
            await configManager.setChatModel(selected.label);
            await aiCodingAssistant.setModel(selected.label);
            updateStatusBar();
            vscode.window.showInformationMessage(`🤖 Selected model: ${selected.label}`);
        }
    } catch (error) {
        outputChannel.appendLine(`Error selecting model: ${error}`);
        vscode.window.showErrorMessage(`Failed to load models: ${error}`);
    }
}

async function configureGeminiApiKey(): Promise<void> {
    const apiKey = await vscode.window.showInputBox({
        prompt: 'Enter your Google Gemini API key',
        password: true,
        placeHolder: 'AIza...'
    });

    if (apiKey) {
        await configManager.setGeminiApiKey(apiKey);
        vscode.window.showInformationMessage('✅ Gemini API key configured successfully!');
    }
}

async function validateProvider(): Promise<void> {
    try {
        const isValid = await aiCodingAssistant.validateProvider();
        const providerType = configManager.getAiProvider();

        if (isValid) {
            vscode.window.showInformationMessage(`✅ ${providerType} provider is working correctly`);
        } else {
            vscode.window.showErrorMessage(`❌ ${providerType} provider validation failed`);
        }
    } catch (error) {
        outputChannel.appendLine(`Provider validation error: ${error}`);
        vscode.window.showErrorMessage(`Validation error: ${error}`);
    }
}

function updateStatusBar(): void {
    const providerType = configManager.getAiProvider();
    const currentModel = configManager.getChatModel() || configManager.getDefaultModel();

    if (providerType === 'ollama') {
        if (currentModel) {
            statusBarItem.text = `🦙 ${currentModel}`;
        } else {
            statusBarItem.text = `🦙 No Model`;
        }
    } else {
        statusBarItem.text = `✨ Gemini`;
    }
}

export function deactivate() {
    // Cleanup resources
    if (aiCodingAssistant) {
        aiCodingAssistant.dispose();
    }

    if (agentSystem) {
        agentSystem.dispose();
    }

    if (webviewProvider) {
        webviewProvider.dispose();
    }

    // if (completionProvider) {
    //     completionProvider.dispose();
    // }

    outputChannel?.appendLine('AI Coding Assistant deactivated');
    outputChannel?.dispose();
}

