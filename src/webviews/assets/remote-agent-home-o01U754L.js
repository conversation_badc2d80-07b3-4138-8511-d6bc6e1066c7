import{t as nt,f as rt,a as it,u as m,b as i,N as Re,x as U,ag as Xe,ah as ot,ai as Ve,y as u,T as ae,a7 as ne,C as V,a1 as ce,B as l,H as J,z as y,P as e,X as be,ac as T,R as P,E as Je,a3 as ye,m as he,ad as H,a4 as lt,a5 as Ze,A as L,a8 as $e,D as se,a0 as dt,a9 as we,ae as ct,G as gt,F as et,ak as We,a6 as tt,aa as st,ar as vt,aO as ut,al as Ke,ax as mt,ay as pt,aP as ft}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";/* empty css                                */import{h as ht,I as Me,c as _t}from"./IconButtonAugment-CR0fVrwD.js";import{M as $t}from"./message-broker-BygIEqPd.js";import{S as wt,T as St,a as Qe,R as kt,B as yt,b as At,c as je,d as xt,v as Rt,e as bt}from"./RepoBadge-CZPdcqql.js";import{R as ge,a as Ne,s as Pt}from"./index-B528snJk.js";import{T as Ce,a as _e,C as zt}from"./CardAugment-DVbbQqkH.js";import{C as Ft}from"./CalloutAugment-C9yL-4XM.js";import{E as Ht}from"./exclamation-triangle-uRBrTmWU.js";import{d as It,s as Ot,R as Le}from"./remote-agents-client-IyYiaWrE.js";import{g as Ye}from"./branch-BXbTQdeh.js";import{A as qt}from"./augment-logo-CNPb11gr.js";import"./async-messaging-Bp70swAv.js";import"./github-ZfT2gl61.js";import"./index-BuQDLh4_.js";import"./chat-types-BDRYChZT.js";import"./types-CGlLNakm.js";var Tt=rt("<svg><!></svg>"),Et=u(" <!>",1),Dt=u('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),Ct=u('<div class="task-text-container svelte-1tatwxk"><!></div>'),Lt=u('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Wt=u('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),Ut=u(' <button class="error-dismiss svelte-96mn5f" aria-label="Dismiss error">×</button>',1),Bt=u('<div class="deletion-error svelte-96mn5f"><!></div>'),Gt=u('<span class="setup-script-title svelte-96mn5f">Generate a setup script</span>'),Mt=u('<div class="setup-script-title-container svelte-96mn5f"><div class="setup-script-badge svelte-96mn5f"><!></div> <!></div>'),Nt=u('<div class="tasks-list svelte-96mn5f"></div>'),Xt=u('<div class="card-commit-info svelte-96mn5f"><!> <!></div>'),jt=u('<div class="card-header svelte-96mn5f"><div class="session-summary-container svelte-96mn5f"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-96mn5f"><!> <!></div> <div class="card-actions svelte-96mn5f"><!> <!> <!></div> <!>',1),Vt=u("<div><!> <!></div>");function Jt(ve,c){Re(c,!1);const I=he(),E=he(),O=he(),re=he(),f=he(),B=he(),K=he();let t=U(c,"agent",8),z=U(c,"selected",8,!1),ue=U(c,"isPinned",8,!1),Se=U(c,"onSelect",8),A=U(c,"onDelete",8),Z=U(c,"deletionError",12,null),me=U(c,"isDeleting",8,!1),G=U(c,"onTogglePinned",24,()=>{}),g=U(c,"sshConfig",24,()=>{});function Q(){Z(null)}Ve(()=>{Q()}),ye(()=>(e(I),e(E),H(g())),()=>{var s;s=g()||{onSSH:()=>Promise.resolve(!1),canSSH:!1},T(I,s.onSSH),T(E,s.canSSH)}),ye(()=>H(t()),()=>{T(O,t().turn_summaries||[])}),ye(()=>{},()=>{T(re,!0)}),ye(()=>H(t()),()=>{var s,r,S;T(f,(S=(r=(s=t().workspace_setup)==null?void 0:s.starting_files)==null?void 0:r.github_commit_ref)==null?void 0:S.repository_url)}),ye(()=>(e(f),Ye),()=>{T(B,e(f)?Ye(e(f)):void 0)}),ye(()=>H(t()),()=>{var s,r,S;T(K,(S=(r=(s=t().workspace_setup)==null?void 0:s.starting_files)==null?void 0:r.github_commit_ref)==null?void 0:S.git_ref)}),lt(),Ze();var p=Vt();let ie;var x=m(p),M=s=>{var r=Bt(),S=m(r);Ft(S,{variant:"soft",color:"error",size:1,icon:R=>{$e(R,{name:"circle-alert",children:(F,N)=>{Ht(F,{})},$$slots:{default:!0}})},children:(R,F)=>{var N=Ut(),Y=J(N),pe=l(Y);V(()=>ce(Y,`${Z()??""} `)),et("click",pe,Q),i(R,N)},$$slots:{icon:!0,default:!0}}),i(s,r)};y(x,s=>{Z()&&s(M)});var Ue=l(x,2);zt(Ue,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>Se()(t().remote_agent_id),keydown:s=>s.key==="Enter"&&Se()(t().remote_agent_id)},children:(s,r)=>{var S=jt(),oe=J(S),R=m(oe),F=m(R),N=n=>{var a=Mt(),b=m(a),w=m(b);$e(w,{name:"terminal",children:($,k)=>{Qe($)},$$slots:{default:!0}});var ee=l(b,2);ae(ee,{size:2,weight:"medium",children:($,k)=>{var te=Gt();i($,te)},$$slots:{default:!0}}),i(n,a)},Y=n=>{ae(n,{size:2,weight:"medium",class:"session-text",children:(a,b)=>{var w=ne();V(()=>ce(w,(H(t()),L(()=>t().session_summary)))),i(a,w)},$$slots:{default:!0}})};y(F,n=>{H(t()),L(()=>t().is_setup_script_agent)?n(N):n(Y,!1)});var pe=l(R,2),Pe=m(pe);wt(Pe,{get status(){return H(t()),L(()=>t().status)},get workspaceStatus(){return H(t()),L(()=>t().workspace_status)},isExpanded:!0,get hasUpdates(){return H(t()),L(()=>t().has_updates)}});var ze=l(oe,2),Oe=m(ze),qe=n=>{var a=Nt();we(a,5,()=>(e(O),L(()=>e(O).slice(0,3))),ct,(b,w)=>{(function(ee,$){Re($,!0);let k=U($,"status",3,"info"),te=P(()=>function(j){switch(j){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(k()));var le=Wt(),X=m(le),ke=l(X,2);const q=P(()=>[_e.Hover]);Ce(ke,{get content(){return $.text},get triggerOn(){return e(q)},maxWidth:"400px",children:(j,fe)=>{var C=Ct(),W=m(C);ae(W,{size:1,color:"secondary",children:(He,at)=>{var Ie=ne();V(()=>ce(Ie,$.text)),i(He,Ie)},$$slots:{default:!0}}),i(j,C)},$$slots:{default:!0}});var de=l(ke,2),Fe=j=>{var fe=Lt(),C=m(fe);const W=P(()=>k()==="error"?"error":"neutral");ae(C,{size:1,get color(){return e(W)},children:(He,at)=>{var Ie=ne();V(()=>ce(Ie,k()==="error"?"!":k()==="warning"?"⚠":"")),i(He,Ie)},$$slots:{default:!0}}),i(j,fe)};y(de,j=>{k()!=="error"&&k()!=="warning"||j(Fe)}),V(()=>Je(X,1,`bullet-point ${e(te)??""}`,"svelte-1tatwxk")),i(ee,le),be()})(b,{get text(){return e(w)},status:"success"})}),i(n,a)};y(Oe,n=>{e(O),L(()=>e(O).length>0)&&n(qe)});var Be=l(Oe,2),Te=n=>{var a=Xt(),b=m(a);kt(b,{get repoUrl(){return e(f)},get repoName(){return e(B)}});var w=l(b,2);yt(w,{get repoUrl(){return e(f)},get branchName(){return e(K)}}),i(n,a)};y(Be,n=>{e(f)&&e(B)&&e(K)&&n(Te)});var Ee=l(ze,2),De=m(Ee),Ge=n=>{const a=se(()=>ue()?"Unpin agent":"Pin agent"),b=se(()=>(H(_e),L(()=>[_e.Hover])));Ce(n,{get content(){return e(a)},get triggerOn(){return e(b)},side:"top",children:(w,ee)=>{Me(w,{variant:"ghost",color:"neutral",size:1,$$events:{click:$=>{$.stopPropagation(),G()()}},children:($,k)=>{var te=gt(),le=J(te),X=q=>{$e(q,{name:"pin-off",children:(de,Fe)=>{(function(j,fe){const C=nt(fe,["children","$$slots","$$events","$$legacy"]);var W=Tt();it(W,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...C}));var He=m(W);ht(He,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),i(j,W)})(de,{})},$$slots:{default:!0}})},ke=q=>{$e(q,{name:"pin",children:(de,Fe)=>{At(de,{})},$$slots:{default:!0}})};y(le,q=>{ue()?q(X):q(ke,!1)}),i($,te)},$$slots:{default:!0}})},$$slots:{default:!0}})};y(De,n=>{G()&&n(Ge)});var d=l(De,2),h=n=>{const a=se(()=>(H(_e),L(()=>[_e.Hover])));Ce(n,{content:"SSH to agent",get triggerOn(){return e(a)},side:"top",children:(b,w)=>{const ee=se(()=>!e(E)),$=se(()=>e(E)?"SSH to agent":"SSH to agent (agent must be running or idle)");Me(b,{get disabled(){return e(ee)},variant:"ghost",color:"neutral",size:1,get title(){return e($)},$$events:{click:k=>{k.stopPropagation(),e(I)()}},children:(k,te)=>{$e(k,{name:"terminal",children:(le,X)=>{Qe(le)},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}})};y(d,n=>{g()&&n(h)});var _=l(d,2);const D=se(()=>(H(_e),L(()=>[_e.Hover])));Ce(_,{content:"Delete agent",get triggerOn(){return e(D)},side:"top",children:(n,a)=>{const b=se(()=>me()?"Deleting agent...":"Delete agent");Me(n,{variant:"ghost",color:"neutral",size:1,get disabled(){return me()},get title(){return e(b)},$$events:{click:w=>{w.stopPropagation(),A()(t().remote_agent_id)}},children:(w,ee)=>{$e(w,{name:"trash-2",children:($,k)=>{St($)},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}});var v=l(Ee,2);const o=se(()=>(H(t()),L(()=>t().updated_at||t().started_at)));(function(n,a){Re(a,!0);let b=U(a,"isRemote",3,!1),w=Xe(ot(It(a.timestamp)));const ee=Ot(a.timestamp,X=>{T(w,X,!0)});Ve(()=>{ee()});var $=Dt(),k=m($);ae(k,{size:1,color:"secondary",class:"location-text",children:(X,ke)=>{var q=ne();V(()=>ce(q,b()?"Running in the cloud":"Running locally")),i(X,q)},$$slots:{default:!0}});var te=l(k,2),le=m(te);ae(le,{size:1,color:"secondary",class:"time-text",children:(X,ke)=>{var q=Et(),de=J(q),Fe=l(de),j=C=>{var W=ne();V(()=>ce(W,e(w))),i(C,W)},fe=C=>{var W=ne("Unknown time");i(C,W)};y(Fe,C=>{a.timestamp?C(j):C(fe,!1)}),V(()=>ce(de,(a.status===ge.agentRunning?"Last updated":"Started")+" ")),i(X,q)},$$slots:{default:!0}}),i(n,$),be()})(v,{get isRemote(){return e(re)},get status(){return H(t()),L(()=>t().status)},get timestamp(){return e(o)}}),V(()=>dt(R,"title",(H(t()),L(()=>t().is_setup_script_agent?"Generate a setup script":t().session_summary)))),i(s,S)},$$slots:{default:!0}}),V(s=>ie=Je(p,1,"card-wrapper svelte-96mn5f",null,ie,s),[()=>({"selected-card":z(),"setup-script-card":t().is_setup_script_agent,deleting:me()})],se),i(ve,p),be()}function Ae(ve,c){Re(c,!0);const[I,E]=tt(),O=()=>st(B,"$sharedWebviewStore",I);let re=U(c,"selected",3,!1);const f=We(Le.key),B=We(je);let K=Xe(!1),t=Xe(null),z=null;function ue(){T(t,null),z&&(clearTimeout(z),z=null)}let Se=P(()=>{var g;return((g=O().state)==null?void 0:g.pinnedAgents)||{}}),A=P(()=>{var g;return((g=e(Se))==null?void 0:g[c.agent.remote_agent_id])===!0}),Z=P(()=>c.agent.status===ge.agentRunning||c.agent.status===ge.agentIdle);async function me(){return!!e(Z)&&await(async g=>await f.sshToRemoteAgent(g.remote_agent_id))(c.agent)}const G=P(()=>({onSSH:me,canSSH:e(Z)}));Jt(ve,{get agent(){return c.agent},get selected(){return re()},get isPinned(){return e(A)},get onSelect(){return c.onSelect},onDelete:()=>async function(g){var p,ie;ue(),T(K,!0);const Q=((p=O().state)==null?void 0:p.agentOverviews)||[];try{if(!await f.deleteRemoteAgent(g))throw new Error("Failed to delete agent");if(B.update(x=>{if(x)return{...x,agentOverviews:x.agentOverviews.filter(M=>M.remote_agent_id!==g)}}),(((ie=O().state)==null?void 0:ie.pinnedAgents)||{})[g])try{await f.deletePinnedAgentFromStore(g);const x=await f.getPinnedAgentsFromStore();B.update(M=>{if(M)return{...M,pinnedAgents:x}})}catch(x){console.error("Failed to remove pinned status:",x)}}catch(x){console.error("Failed to delete agent:",x),B.update(M=>{if(M)return{...M,agentOverviews:Q}}),T(t,x instanceof Error?x.message:"Failed to delete agent",!0),z=setTimeout(()=>{ue()},5e3)}finally{T(K,!1)}}(c.agent.remote_agent_id),onTogglePinned:()=>async function(g){try{e(A)?await f.deletePinnedAgentFromStore(g):await f.savePinnedAgentToStore(g,!0);const Q=await f.getPinnedAgentsFromStore();B.update(p=>{if(p)return{...p,pinnedAgents:Q}})}catch(Q){console.error("Failed to toggle pinned status:",Q)}}(c.agent.remote_agent_id),get sshConfig(){return e(G)},get deletionError(){return e(t)},set deletionError(g){T(t,g,!0)},get isDeleting(){return e(K)},set isDeleting(g){T(K,g,!0)}}),be(),E()}var Kt=u('<div class="section-header svelte-1tegnqi"><!></div>');function xe(ve,c){var I=Kt(),E=m(I);ae(E,{size:2,color:"secondary",children:(O,re)=>{var f=ne();V(()=>ce(f,c.title)),i(O,f)},$$slots:{default:!0}}),i(ve,I)}var Qt=u('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Yt=u('<div class="empty-state svelte-aiqmvp"><!></div>'),Zt=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),es=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ts=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ss=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),as=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ns=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),rs=u("<!> <!> <!> <!> <!> <!>",1),is=u('<div class="agent-list svelte-aiqmvp"><!></div>'),os=u('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');ft(function(ve,c){Re(c,!1);const I=new $t(_t),E=new xt(I,void 0,Rt,bt);I.registerConsumer(E),Ke(je,E);const O=new Le(I);Ke(Le.key,O),mt(()=>(E.fetchStateFromExtension().then(()=>{E.update(t=>{if(!t)return;const z=[...t.activeWebviews,"home"];return t.pinnedAgents?{...t,activeWebviews:z}:{...t,activeWebviews:z,pinnedAgents:{}}})}),()=>{I.dispose(),O.dispose()})),Ze();var re=os();et("message",pt,function(...t){var z;(z=I.onMessageFromExtension)==null||z.apply(this,t)});var f=m(re),B=m(f),K=m(B);$e(K,{name:"sparkles",children:(t,z)=>{qt(t)},$$slots:{default:!0}}),function(t,z){Re(z,!0);const[ue,Se]=tt(),A=()=>st(Z,"$sharedWebviewStore",ue),Z=We(je),me=We(Le.key);function G(s){Z.update(r=>{if(r)return{...r,selectedAgentId:s}})}let g=P(()=>{var s;return Pt(((s=A().state)==null?void 0:s.agentOverviews)||[])}),Q=P(()=>{var s;return((s=A().state)==null?void 0:s.pinnedAgents)||{}}),p=P(()=>e(g).reduce((s,r)=>{var S;return((S=e(Q))==null?void 0:S[r.remote_agent_id])===!0?s.pinned.push(r):r.status===ge.agentIdle&&r.has_updates?s.readyToReview.push(r):r.status===ge.agentRunning||r.status===ge.agentStarting||r.workspace_status===Ne.workspaceResuming?s.running.push(r):r.status===ge.agentFailed?s.failed.push(r):r.status===ge.agentIdle||r.workspace_status===Ne.workspacePaused||r.workspace_status===Ne.workspacePausing?s.idle.push(r):s.additional.push(r),s},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}));vt(()=>{var s;(s=A().state)!=null&&s.agentOverviews||me.focusAugmentPanel()});var ie=is(),x=m(ie),M=s=>{var r=Qt(),S=m(r),oe=m(S);ut(oe,{});var R=l(oe,2);ae(R,{size:3,color:"secondary",children:(F,N)=>{var Y=ne("Loading the Augment panel...");i(F,Y)},$$slots:{default:!0}}),i(s,r)},Ue=(s,r)=>{var S=R=>{var F=Yt(),N=m(F);ae(N,{size:3,color:"secondary",children:(Y,pe)=>{var Pe=ne("No agents available");i(Y,Pe)},$$slots:{default:!0}}),i(R,F)},oe=R=>{var F=rs(),N=J(F),Y=d=>{var h=Zt(),_=J(h);xe(_,{title:"Pinned"});var D=l(_,2);we(D,23,()=>e(p).pinned,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(N,d=>{e(p).pinned.length>0&&d(Y)});var pe=l(N,2),Pe=d=>{var h=es(),_=J(h);xe(_,{title:"Ready to review"});var D=l(_,2);we(D,23,()=>e(p).readyToReview,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(pe,d=>{e(p).readyToReview.length>0&&d(Pe)});var ze=l(pe,2),Oe=d=>{var h=ts(),_=J(h);xe(_,{title:"Running agents"});var D=l(_,2);we(D,23,()=>e(p).running,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(ze,d=>{e(p).running.length>0&&d(Oe)});var qe=l(ze,2),Be=d=>{var h=ss(),_=J(h);xe(_,{title:"Idle agents"});var D=l(_,2);we(D,23,()=>e(p).idle,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(qe,d=>{e(p).idle.length>0&&d(Be)});var Te=l(qe,2),Ee=d=>{var h=as(),_=J(h);xe(_,{title:"Failed agents"});var D=l(_,2);we(D,23,()=>e(p).failed,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(Te,d=>{e(p).failed.length>0&&d(Ee)});var De=l(Te,2),Ge=d=>{var h=ns(),_=J(h);xe(_,{title:"Other agents"});var D=l(_,2);we(D,23,()=>e(p).additional,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=P(()=>{var a;return e(o).remote_agent_id===((a=A().state)==null?void 0:a.selectedAgentId)});Ae(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:G})}),i(d,h)};y(De,d=>{e(p).additional.length>0&&d(Ge)}),i(R,F)};y(s,R=>{var F;((F=A().state)==null?void 0:F.agentOverviews.length)===0?R(S):R(oe,!1)},r)};y(x,s=>{var r;(r=A().state)!=null&&r.agentOverviews?s(Ue,!1):s(M)}),i(t,ie),be(),Se()}(l(f,2),{}),i(ve,re),be()},{target:document.getElementById("app")});
