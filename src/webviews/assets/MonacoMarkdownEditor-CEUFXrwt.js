import{N as U,x as g,ag as L,ah as Y,Z as P,P as v,ac as l,ai as C,y as ee,H as te,S as b,V as S,u as m,B as f,aj as ne,z as A,b as k,X as oe,T,a7 as B,C as ae,a1 as ie}from"./GuardedIcon-BFT2yJIo.js";import"./IconButtonAugment-CR0fVrwD.js";import{S as se,M as re}from"./index-CWns8XM2.js";var de=ee('<div class="l-markdown-editor svelte-1r1jr5o"><div class="c-markdown-editor__header svelte-1r1jr5o"><!></div> <!> <div class="c-markdown-editor__content svelte-1r1jr5o"><!> <div class="c-monaco-editor svelte-1r1jr5o"><!></div></div></div> <div class="c-markdown-editor__status svelte-1r1jr5o"><!> <!></div>',1);function ge(D,i){U(i,!0);let s=g(i,"value",15,""),y=g(i,"selectedText",15,""),M=g(i,"selectionStart",15,0),j=g(i,"selectionEnd",15,0),H=g(i,"monacoOptions",19,()=>({})),n=g(i,"editorInstance",15),r=L(Y(s())),h=!1;const R={readOnly:!1,wordWrap:"on",lineNumbers:"on",minimap:{enabled:!1},scrollBeyondLastLine:!1,automaticLayout:!0,theme:"vs-dark",language:"markdown",fontSize:14,lineHeight:20,padding:{top:16,bottom:16},...H()};let x,c,d,w=L(!1),p=L(void 0);const N=async()=>{n()&&await(async()=>{try{if(n()){const e=n().getValue();l(r,e,!0),s(e)}i.saveFunction(),l(w,!0),clearTimeout(x),x=setTimeout(()=>{l(w,!1)},1500)}catch(e){l(p,e instanceof Error?e.message:String(e),!0)}})()},F=()=>{if(!n()||h)return;const e=n().getValue();e!==v(r)&&(l(r,e,!0),s(e))},G=()=>{if(!n())return;let e,t;e=n().onDidChangeModelContent(F),t=n().addAction({id:"save-markdown-content",label:"Save Markdown Content",keybindings:[2097],contextMenuGroupId:"navigation",contextMenuOrder:1.5,run:()=>{N()}}),(()=>{if(typeof window>"u")return;const o=()=>{N()};window.addEventListener("blur",o),d=()=>{window.removeEventListener("blur",o)}})(),c=()=>{e==null||e.dispose(),t==null||t.dispose(),d==null||d()}};P(()=>{if(s()!==v(r))if(n()){if(!h){h=!0;const e=n().getPosition(),t=n().getSelection(),o=n().getModel();if(o&&o.getValue()!==s()){if(o.setValue(s()),l(r,s(),!0),e&&o.getLineCount()>=e.lineNumber){const a=o.getLineLength(e.lineNumber);e.column<=a+1&&n().setPosition(e)}if(t&&o.getLineCount()>=t.endLineNumber){const a=o.getLineLength(t.endLineNumber);t.endColumn<=a+1&&n().setSelection(t)}}setTimeout(()=>{h=!1},10)}}else l(r,s(),!0)}),P(()=>{if(n()){G();const e=n(),t=e.onDidChangeCursorSelection(()=>{const a=e.getSelection();if(a){const u=e.getModel();u&&(M(u.getOffsetAt(a.getStartPosition())),j(u.getOffsetAt(a.getEndPosition())),M()!==j()?y(u.getValueInRange(a)):y(""))}}),o=c;c=()=>{o==null||o(),t==null||t.dispose()}}}),C(()=>{c==null||c(),d==null||d()});var V=de(),_=te(V),$=m(_),W=m($);b(W,()=>i.header??S);var E=f($,2);b(E,()=>i.children??S);var X=f(E,2),I=m(X);b(I,()=>i.title??S);var Z=f(I,2),q=m(Z);ne(q,()=>re.Root,(e,t)=>{t(e,{children:(o,a)=>{se(o,{get text(){return v(r)},lang:"markdown",get options(){return R},height:void 0,get editorInstance(){return n()},set editorInstance(u){n(u)}})},$$slots:{default:!0}})});var z=f(_,2),O=m(z),J=e=>{T(e,{size:1,weight:"light",color:"error",children:(t,o)=>{var a=B();ae(()=>ie(a,v(p))),k(t,a)},$$slots:{default:!0}})};A(O,e=>{v(p)&&e(J)});var K=f(O,2),Q=e=>{T(e,{size:1,weight:"light",color:"success",children:(t,o)=>{var a=B("Saved");k(t,a)},$$slots:{default:!0}})};A(K,e=>{v(w)&&e(Q)}),k(D,V),oe()}export{ge as M};
