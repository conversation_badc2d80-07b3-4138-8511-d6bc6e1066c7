var q=Object.defineProperty;var J=(s,e,t)=>e in s?q(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var u=(s,e,t)=>J(s,typeof e!="symbol"?e+"":e,t);import{t as V,f as E,a as N,u as v,b as f,aU as y,w as O,at as K,N as ee,x as p,y as P,z as te,P as z,R as se,B as U,C as B,E as w,a0 as ne,F as D,X as oe,H as ae,a1 as G,al as ie,ak as ce}from"./GuardedIcon-BFT2yJIo.js";import{h as R}from"./IconButtonAugment-CR0fVrwD.js";import{e as S}from"./index-DON3DCoY.js";import{a as le}from"./BaseTextInput-BaUpeUef.js";import{R as x}from"./chat-types-BDRYChZT.js";var re=E("<svg><!></svg>");function _e(s,e){const t=V(e,["children","$$slots","$$events","$$legacy"]);var n=re();N(n,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var l=v(n);R(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),f(s,n)}var de=E('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function Le(s){var e=de();f(s,e)}const b={enabled:!1,volume:.5,playOnlyWhenWindowUnfocused:!1},Ee={enabled:!0},ue=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href,W=O(!0);function Ae(s){W.set(s)}var H=(s=>(s.AGENT_COMPLETE="agent-complete",s))(H||{});const he={"agent-complete":ue},h=class h{constructor(){u(this,"audioCache",new Map)}static getInstance(){return h._instance||(h._instance=new h),h._instance}retrieveAudioElement(e,t){let n=this.audioCache.get(e);return n?n.volume=t.volume:(n=new Audio,n.src=he[e],n.volume=t.volume,n.preload="auto",n._isUnlocked=!1,this.audioCache.set(e,n)),n}async playSound(e,t){if(t.enabled&&(!t.playOnlyWhenWindowUnfocused||!y(W)))try{const n=this.retrieveAudioElement(e,t);n.currentTime=0,await n.play()}catch(n){if(n instanceof DOMException&&n.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",n)}}async unlockSoundForConfig(e){if(!e.enabled)return;const t=this.retrieveAudioElement("agent-complete",e);if(!t._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),t._isUnlocked=!0}catch(n){console.warn("Failed to unlock sound:",n)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};u(h,"_instance");let L=h;const _=L.getInstance();class ge{constructor(e){u(this,"_soundSettings",O(b));u(this,"_isLoaded",!1);u(this,"dispose",()=>{_.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:S.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){y(this._soundSettings).enabled&&_.unlockSoundForConfig(y(this._soundSettings))}async playAgentComplete(e){const t={...y(this._soundSettings),...e};await _.playSound(H.AGENT_COMPLETE,t)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:S.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(b),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:S.updateSoundSettings,data:e}),this._soundSettings.update(t=>({...t,...e}))}catch(t){throw console.error("Failed to update sound settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const t=Math.max(0,Math.min(1,e));await this.updateSettings({volume:t})}async resetToDefaults(){await this.updateSettings(b)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}updateVolume(e){this.setVolume(e).catch(t=>{console.error("Failed to update volume setting:",t)})}}u(ge,"key","soundModel");function pe(s,e,t,n){var l;e()||(s.key!=="Enter"&&s.key!==" "||(s.preventDefault(),t(!t())),(l=n.onkeydown)==null||l.call(n,s))}function me(s,e){var t;(t=e.onchange)==null||t.call(e,s)}var ve=P("<span> </span> <span> </span>",1),fe=P('<label><!> <input type="checkbox" role="switch"/></label>');function Te(s,e){ee(e,!0);let t=p(e,"checked",15,!1),n=p(e,"disabled",3,!1),l=p(e,"size",3,2),k=p(e,"ariaLabel",19,()=>{}),d=p(e,"onText",19,()=>{}),g=p(e,"offText",19,()=>{}),o=se(()=>d()||g());var a=fe();let m;var A=v(a),Q=c=>{var i=ve(),C=ae(i);let M;var X=v(C),$=U(C,2);let F;var Y=v($);B((Z,j)=>{M=w(C,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,M,Z),G(X,g()||""),F=w($,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,F,j),G(Y,d()||"")},[()=>({visible:!t()&&g()}),()=>({visible:t()&&d()})]),f(c,i)};te(A,c=>{z(o)&&c(Q)});var r=U(A,2);let T;r.__keydown=[pe,n,t,e],r.__change=[me,e],r.__click=function(...c){var i;(i=e.onclick)==null||i.apply(this,c)},B((c,i)=>{m=w(a,1,`c-toggle-track c-toggle-track-size--${l()??""}`,"svelte-xr5g0k",m,c),T=w(r,1,"c-toggle-input svelte-xr5g0k",null,T,i),r.disabled=n(),ne(r,"aria-label",k())},[()=>({checked:t(),disabled:n(),"has-text":z(o)}),()=>({disabled:n()})]),D("blur",r,function(...c){var i;(i=e.onblur)==null||i.apply(this,c)}),D("focus",r,function(...c){var i;(i=e.onfocus)==null||i.apply(this,c)}),le(r,t),f(s,a),oe()}function Me(s){const{rules:e,workspaceGuidelinesContent:t,contextRules:n=[]}=s,l=e.filter(o=>o.type===x.ALWAYS_ATTACHED).reduce((o,a)=>o+a.content.length+a.path.length,0),k=e.filter(o=>o.type===x.AGENT_REQUESTED).reduce((o,a)=>{var m;return o+100+(((m=a.description)==null?void 0:m.length)??0)+a.path.length},0),d=l+e.filter(o=>o.type===x.MANUAL).filter(o=>n.some(a=>a.path===o.path)).reduce((o,a)=>o+a.content.length+a.path.length,0)+k+t.length,g=s.rulesAndGuidelinesLimit&&d>s.rulesAndGuidelinesLimit;return{totalCharacterCount:d,isOverLimit:g,warningMessage:g&&s.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${d} chars)
        exceeds the limit of ${s.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}K(["keydown","change","click"]);var we=E("<svg><!></svg>");function $e(s,e){const t=V(e,["children","$$slots","$$events","$$legacy"]);var n=we();N(n,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...t}));var l=v(n);R(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',!0),f(s,n)}const I="extensionClient";function Fe(s){ie(I,s)}function ze(){const s=ce(I);if(!s)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return s}export{Le as C,Ee as D,ge as S,$e as T,Te as a,_e as b,Me as c,ze as g,Fe as s,Ae as u};
