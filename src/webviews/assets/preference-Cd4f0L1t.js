import{at as mt,N as X,x,y as h,z as j,u as r,B as a,C as N,E as z,b as v,X as K,a1 as tt,a0 as ht,aq as bt,ag as B,ah as ct,ax as gt,W as O,P as e,ac as _,ar as ft,R as st,H as rt,G as dt,F as yt,ay as wt,aj as kt,aP as _t}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import{c as D}from"./IconButtonAugment-CR0fVrwD.js";import{aJ as lt}from"./AugmentMessage-cgapx9im.js";import{b as At,a as qt}from"./BaseTextInput-BaUpeUef.js";import{C as Bt,S as xt}from"./folder-opened-b0Ugp2il.js";import{M as Dt}from"./message-broker-BygIEqPd.js";import{s as Ct}from"./chat-model-context-39sqbIF3.js";import{M as Rt}from"./index-CWns8XM2.js";import"./index-BzB60MCy.js";import"./preload-helper-Dv6uf1Os.js";import"./await-D3vig32v.js";import"./partner-mcp-utils-DYgwDbFd.js";import"./index-DON3DCoY.js";import"./async-messaging-Bp70swAv.js";import"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./CardAugment-DVbbQqkH.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./trash-ByjpApta.js";import"./index-C_brRns6.js";import"./expand-CPL6rEzo.js";import"./toggleHighContrast-C7wSWUJK.js";import"./ButtonAugment-CWDjQYWT.js";import"./MaterialIcon-D9dP7dAZ.js";import"./lodash-DkRojHDE.js";import"./Filespan-9fd1tyrF.js";import"./OpenFileButton-CVOPVJP1.js";import"./index-B528snJk.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";import"./SuccessfulButton-Btt1yYx9.js";import"./CopyButton-DSjqZJL1.js";import"./copy-oYDqgVJ5.js";import"./LanguageIcon-CJlkgv5n.js";import"./CollapseButtonAugment-Cnr_pz_w.js";import"./ellipsis-DBzJOEA0.js";import"./keypress-DD1aQVr0.js";import"./TextAreaAugment-DIwZ8JEM.js";import"./clock-B1EQOiug.js";import"./augment-logo-CNPb11gr.js";import"./index-BuQDLh4_.js";import"./branch-BXbTQdeh.js";import"./index-JMsuML6t.js";import"./CalloutAugment-C9yL-4XM.js";import"./file-type-utils-Zb3vtfL9.js";var Mt=h('<div class="header svelte-1894wv4"> </div>'),Pt=(p,t)=>t("A3"),Wt=(p,t)=>t("A2"),$t=(p,t)=>t("A1"),It=(p,t)=>t("="),zt=(p,t)=>t("B1"),Et=(p,t)=>t("B2"),St=(p,t)=>t("B3"),Ft=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(p,t){X(t,!0);let i=x(t,"selected",15,null),E=x(t,"question",3,null);function o(w){i(w)}var g=Ft(),C=r(g),l=w=>{var H=Mt(),J=r(H);N(()=>tt(J,E())),v(w,H)};j(C,w=>{E()&&w(l)});var c=a(C,2),f=r(c);let d;f.__click=[Pt,o];var b=a(f,2);let S;b.__click=[Wt,o];var R=a(b,2);let A;R.__click=[$t,o];var q=a(R,2);let M;q.__click=[It,o];var P=a(q,2);let y;P.__click=[zt,o];var F=a(P,2);let G;F.__click=[Et,o];var U=a(F,2);let V;U.__click=[St,o],N((w,H,J,et,s,u,Y)=>{d=z(f,1,"button large svelte-1894wv4",null,d,w),S=z(b,1,"button medium svelte-1894wv4",null,S,H),A=z(R,1,"button small svelte-1894wv4",null,A,J),M=z(q,1,"button equal svelte-1894wv4",null,M,et),y=z(P,1,"button small svelte-1894wv4",null,y,s),G=z(F,1,"button medium svelte-1894wv4",null,G,u),V=z(U,1,"button large svelte-1894wv4",null,V,Y)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})]),v(p,g),K()}mt(["click"]);var Ht=h('<div class="question svelte-1i0f73l"> </div>'),Lt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Nt=h('<button class="button svelte-2k5n"> </button>');mt(["click"]);var Ot=h("<div> </div>"),jt=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Gt=h("<!> <!> <!> <!> <!> <!>",1),Jt=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Qt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Tt(p,t){X(t,!0);const i=bt();let E=new Bt(new Dt(D),D,new xt);Ct(E);let o=B(null),g=B(null),C=null,l=B(null),c=B(""),f=B(!1),d=B(ct({a:null,b:null})),b=B(ct(t.inputData.data.a.response.length>0&&t.inputData.data.b.response.length>0));function S(){if(C="=",e(l)===null)return void i("notify","Overall rating is required");const s={overallRating:e(l),formattingRating:e(o)||"=",hallucinationRating:C||"=",instructionFollowingRating:e(g)||"=",isHighQuality:e(f),textFeedback:e(c)};i("result",s)}gt(()=>{window.addEventListener("message",s=>{const u=s.data;u.type===O.chatModelReply?(u.stream==="A"?e(d).a=u.data.text:u.stream==="B"&&(e(d).b=u.data.text),_(d,e(d),!0)):u.type===O.chatStreamDone&&_(b,!0)})});let R=st(()=>{return(s=e(l))==="="||s===null?"Is this a high quality comparison?":`Are you completely happy with response '${s.startsWith("A")?"A":"B"}'?`;var s}),A=st(()=>e(d).a!==null?e(d).a:t.inputData.data.a.response),q=st(()=>e(d).b!==null?e(d).b:t.inputData.data.b.response);ft(()=>{_(b,t.inputData.data.a.response.length>0&&t.inputData.data.b.response.length>0,!0)});var M=Qt(),P=r(M),y=a(r(P),2);lt(y,{get markdown(){return t.inputData.data.a.message}});var F=a(y,4),G=r(F),U=a(r(G),2);lt(U,{get markdown(){return e(A)}});var V=a(G,4),w=a(r(V),2);lt(w,{get markdown(){return e(q)}});var H=a(F,4),J=s=>{var u=Gt(),Y=rt(u);nt(Y,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return e(o)},set selected(n){_(o,n,!0)}});var ot=a(Y,2);nt(ot,{question:"Which response follows your instruction better?",get selected(){return e(g)},set selected(n){_(g,n,!0)}});var ut=a(ot,2);nt(ut,{question:"Which response is better overall?",get selected(){return e(l)},set selected(n){_(l,n,!0)}});var vt=a(ut,2);(function(n,m){X(m,!0);let Q=x(m,"isChecked",15,!1),k=x(m,"question",3,null);var L=jt(),W=r(L),$=I=>{var T=Ot(),it=r(T);N(()=>tt(it,k())),v(I,T)};j(W,I=>{k()&&I($)});var at=a(W,2),Z=r(at);qt(Z,Q),v(n,L),K()})(vt,{get question(){return e(R)},get isChecked(){return e(f)},set isChecked(n){_(f,n,!0)}});var pt=a(vt,2);(function(n,m){X(m,!0);let Q=x(m,"value",15,""),k=x(m,"question",3,null),L=x(m,"placeholder",3,"");var W=Lt(),$=r(W),at=I=>{var T=Ht(),it=r(T);N(()=>tt(it,k())),v(I,T)};j($,I=>{k()&&I(at)});var Z=a($,2);N(()=>ht(Z,"placeholder",L())),At(Z,Q),v(n,W),K()})(pt,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return e(c)},set value(n){_(c,n,!0)}}),function(n,m){let Q=x(m,"label",3,"Submit");var k=Nt();k.__click=function(...W){var $;($=m.onClick)==null||$.apply(this,W)};var L=r(k);N(()=>tt(L,Q())),v(n,k)}(a(pt,2),{label:"Submit",onClick:S}),v(s,u)},et=s=>{var u=Jt();v(s,u)};j(H,s=>{e(b)?s(J):s(et,!1)}),v(p,M),K()}var Xt=h("<main><!></main>");function Kt(p,t){X(t,!0);let i=B(void 0);function E(l){const c=l.detail;D.postMessage({type:O.preferenceResultMessage,data:c})}function o(l){D.postMessage({type:O.preferenceNotify,data:l.detail})}D.postMessage({type:O.preferencePanelLoaded});var g=dt();yt("message",wt,function(l){const c=l.data;c.type===O.preferenceInit&&_(i,c.data,!0)});var C=rt(g);kt(C,()=>Rt.Root,(l,c)=>{c(l,{children:(f,d)=>{var b=Xt(),S=r(b),R=A=>{var q=dt(),M=rt(q),P=y=>{Tt(y,{get inputData(){return e(i)},$$events:{result:E,notify:o}})};j(M,y=>{e(i).type==="Chat"&&y(P)}),v(A,q)};j(S,A=>{e(i)&&A(R)}),v(f,b)},$$slots:{default:!0}})}),v(p,g),K()}(async function(){D&&D.initialize&&await D.initialize(),_t(Kt,{target:document.getElementById("app")})})();
