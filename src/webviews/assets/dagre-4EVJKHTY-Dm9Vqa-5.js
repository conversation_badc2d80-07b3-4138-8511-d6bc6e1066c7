import{_ as w,av as L,aw as j,ax as M,ay as Y,l as o,c as H,az as z,aA as _,ah as q,am as K,ai as O,ag as Q,aB as U,aC as V,aD as W}from"./AugmentMessage-cgapx9im.js";import{G as k}from"./graph-6ZXxHZ6W.js";import{l as Z}from"./layout-CFJ75vu_.js";import{i as S}from"./_baseUniq-BrcrwDU6.js";import{c as $}from"./clone-DvxDEQG-.js";import{m as B}from"./_basePickBy-C7hsU9lQ.js";import"./GuardedIcon-BFT2yJIo.js";import"./index-BzB60MCy.js";import"./preload-helper-Dv6uf1Os.js";import"./await-D3vig32v.js";import"./chat-model-context-39sqbIF3.js";import"./IconButtonAugment-CR0fVrwD.js";import"./partner-mcp-utils-DYgwDbFd.js";import"./index-DON3DCoY.js";import"./async-messaging-Bp70swAv.js";import"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./CardAugment-DVbbQqkH.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./BaseTextInput-BaUpeUef.js";import"./trash-ByjpApta.js";import"./index-C_brRns6.js";import"./expand-CPL6rEzo.js";import"./toggleHighContrast-C7wSWUJK.js";import"./index-CWns8XM2.js";import"./ButtonAugment-CWDjQYWT.js";import"./MaterialIcon-D9dP7dAZ.js";import"./lodash-DkRojHDE.js";import"./Filespan-9fd1tyrF.js";import"./OpenFileButton-CVOPVJP1.js";import"./index-B528snJk.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";import"./SuccessfulButton-Btt1yYx9.js";import"./CopyButton-DSjqZJL1.js";import"./copy-oYDqgVJ5.js";import"./LanguageIcon-CJlkgv5n.js";import"./CollapseButtonAugment-Cnr_pz_w.js";import"./ellipsis-DBzJOEA0.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-b0Ugp2il.js";import"./message-broker-BygIEqPd.js";import"./file-type-utils-Zb3vtfL9.js";import"./TextAreaAugment-DIwZ8JEM.js";import"./clock-B1EQOiug.js";import"./augment-logo-CNPb11gr.js";import"./index-BuQDLh4_.js";import"./branch-BXbTQdeh.js";import"./index-JMsuML6t.js";import"./CalloutAugment-C9yL-4XM.js";function X(e){var n={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:ee(e),edges:re(e)};return S(e.graph())||(n.value=$(e.graph())),n}function ee(e){return B(e.nodes(),function(n){var r=e.node(n),a=e.parent(n),d={v:n};return S(r)||(d.value=r),S(a)||(d.parent=a),d})}function re(e){return B(e.edges(),function(n){var r=e.edge(n),a={v:n.v,w:n.w};return S(n.name)||(a.name=n.name),S(r)||(a.value=r),a})}var l=new Map,b=new Map,P=new Map,ne=w(()=>{b.clear(),P.clear(),l.clear()},"clear"),D=w((e,n)=>{const r=b.get(n)||[];return o.trace("In isDescendant",n," ",e," = ",r.includes(e)),r.includes(e)},"isDescendant"),te=w((e,n)=>{const r=b.get(n)||[];return o.info("Descendants of ",n," is ",r),o.info("Edge is ",e),e.v!==n&&e.w!==n&&(r?r.includes(e.v)||D(e.v,n)||D(e.w,n)||r.includes(e.w):(o.debug("Tilt, ",n,",not in descendants"),!1))},"edgeInCluster"),A=w((e,n,r,a)=>{o.warn("Copying children of ",e,"root",a,"data",n.node(e),a);const d=n.children(e)||[];e!==a&&d.push(e),o.warn("Copying (nodes) clusterId",e,"nodes",d),d.forEach(s=>{if(n.children(s).length>0)A(s,n,r,a);else{const i=n.node(s);o.info("cp ",s," to ",a," with parent ",e),r.setNode(s,i),a!==n.parent(s)&&(o.warn("Setting parent",s,n.parent(s)),r.setParent(s,n.parent(s))),e!==a&&s!==e?(o.debug("Setting parent",s,e),r.setParent(s,e)):(o.info("In copy ",e,"root",a,"data",n.node(e),a),o.debug("Not Setting parent for node=",s,"cluster!==rootId",e!==a,"node!==clusterId",s!==e));const c=n.edges(s);o.debug("Copying Edges",c),c.forEach(p=>{o.info("Edge",p);const E=n.edge(p.v,p.w,p.name);o.info("Edge data",E,a);try{te(p,a)?(o.info("Copying as ",p.v,p.w,E,p.name),r.setEdge(p.v,p.w,E,p.name),o.info("newGraph edges ",r.edges(),r.edge(r.edges()[0]))):o.info("Skipping copy of edge ",p.v,"-->",p.w," rootId: ",a," clusterId:",e)}catch(C){o.error(C)}})}o.debug("Removing node",s),n.removeNode(s)})},"copy"),J=w((e,n)=>{const r=n.children(e);let a=[...r];for(const d of r)P.set(d,e),a=[...a,...J(d,n)];return a},"extractDescendants"),oe=w((e,n,r)=>{const a=e.edges().filter(c=>c.v===n||c.w===n),d=e.edges().filter(c=>c.v===r||c.w===r),s=a.map(c=>({v:c.v===n?r:c.v,w:c.w===n?n:c.w})),i=d.map(c=>({v:c.v,w:c.w}));return s.filter(c=>i.some(p=>c.v===p.v&&c.w===p.w))},"findCommonEdges"),I=w((e,n,r)=>{const a=n.children(e);if(o.trace("Searching children of id ",e,a),a.length<1)return e;let d;for(const s of a){const i=I(s,n,r),c=oe(n,r,i);if(i){if(!(c.length>0))return i;d=i}}return d},"findNonClusterChild"),G=w(e=>l.has(e)&&l.get(e).externalConnections&&l.has(e)?l.get(e).id:e,"getAnchorId"),ie=w((e,n)=>{if(!e||n>10)o.debug("Opting out, no graph ");else{o.debug("Opting in, graph "),e.nodes().forEach(function(r){e.children(r).length>0&&(o.warn("Cluster identified",r," Replacement id in edges: ",I(r,e,r)),b.set(r,J(r,e)),l.set(r,{id:I(r,e,r),clusterData:e.node(r)}))}),e.nodes().forEach(function(r){const a=e.children(r),d=e.edges();a.length>0?(o.debug("Cluster identified",r,b),d.forEach(s=>{D(s.v,r)^D(s.w,r)&&(o.warn("Edge: ",s," leaves cluster ",r),o.warn("Descendants of XXX ",r,": ",b.get(r)),l.get(r).externalConnections=!0)})):o.debug("Not a cluster ",r,b)});for(let r of l.keys()){const a=l.get(r).id,d=e.parent(a);d!==r&&l.has(d)&&!l.get(d).externalConnections&&(l.get(r).id=d)}e.edges().forEach(function(r){const a=e.edge(r);o.warn("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(r)),o.warn("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(e.edge(r)));let d=r.v,s=r.w;if(o.warn("Fix XXX",l,"ids:",r.v,r.w,"Translating: ",l.get(r.v)," --- ",l.get(r.w)),l.get(r.v)||l.get(r.w)){if(o.warn("Fixing and trying - removing XXX",r.v,r.w,r.name),d=G(r.v),s=G(r.w),e.removeEdge(r.v,r.w,r.name),d!==r.v){const i=e.parent(d);l.get(i).externalConnections=!0,a.fromCluster=r.v}if(s!==r.w){const i=e.parent(s);l.get(i).externalConnections=!0,a.toCluster=r.w}o.warn("Fix Replacing with XXX",d,s,r.name),e.setEdge(d,s,a,r.name)}}),o.warn("Adjusted Graph",X(e)),R(e,0),o.trace(l)}},"adjustClustersAndEdges"),R=w((e,n)=>{var d,s;if(o.warn("extractor - ",n,X(e),e.children("D")),n>10)return void o.error("Bailing out");let r=e.nodes(),a=!1;for(const i of r){const c=e.children(i);a=a||c.length>0}if(a){o.debug("Nodes = ",r,n);for(const i of r)if(o.debug("Extracting node",i,l,l.has(i)&&!l.get(i).externalConnections,!e.parent(i),e.node(i),e.children("D")," Depth ",n),l.has(i))if(!l.get(i).externalConnections&&e.children(i)&&e.children(i).length>0){o.warn("Cluster without external connections, without a parent and with children",i,n);let c=e.graph().rankdir==="TB"?"LR":"TB";(s=(d=l.get(i))==null?void 0:d.clusterData)!=null&&s.dir&&(c=l.get(i).clusterData.dir,o.warn("Fixing dir",l.get(i).clusterData.dir,c));const p=new k({multigraph:!0,compound:!0}).setGraph({rankdir:c,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});o.warn("Old graph before copy",X(e)),A(i,e,p,i),e.setNode(i,{clusterNode:!0,id:i,clusterData:l.get(i).clusterData,label:l.get(i).label,graph:p}),o.warn("New graph after copy node: (",i,")",X(p)),o.debug("Old graph after copy",X(e))}else o.warn("Cluster ** ",i," **not meeting the criteria !externalConnections:",!l.get(i).externalConnections," no parent: ",!e.parent(i)," children ",e.children(i)&&e.children(i).length>0,e.children("D"),n),o.debug(l);else o.debug("Not a cluster",i,n);r=e.nodes(),o.warn("New list of nodes",r);for(const i of r){const c=e.node(i);o.warn(" Now next level",i,c),c!=null&&c.clusterNode&&R(c.graph,n+1)}}else o.debug("Done, no node has children",e.nodes())},"extractor"),T=w((e,n)=>{if(n.length===0)return[];let r=Object.assign([],n);return n.forEach(a=>{const d=e.children(a),s=T(e,d);r=[...r,...s]}),r},"sorter"),ae=w(e=>T(e,e.children()),"sortNodesByHierarchy"),F=w(async(e,n,r,a,d,s)=>{o.warn("Graph in recursive render:XAX",X(n),d);const i=n.graph().rankdir;o.trace("Dir in recursive render - dir:",i);const c=e.insert("g").attr("class","root");n.nodes()?o.info("Recursive render XXX",n.nodes()):o.info("No nodes found for",n),n.edges().length>0&&o.info("Recursive edges",n.edge(n.edges()[0]));const p=c.insert("g").attr("class","clusters"),E=c.insert("g").attr("class","edgePaths"),C=c.insert("g").attr("class","edgeLabels"),f=c.insert("g").attr("class","nodes");await Promise.all(n.nodes().map(async function(g){const t=n.node(g);if(d!==void 0){const u=JSON.parse(JSON.stringify(d.clusterData));o.trace(`Setting data for parent cluster XXX
 Node.id = `,g,`
 data=`,u.height,`
Parent cluster`,d.height),n.setNode(d.id,u),n.parent(g)||(o.trace("Setting parent",g,d.id),n.setParent(g,d.id,u))}if(o.info("(Insert) Node XXX"+g+": "+JSON.stringify(n.node(g))),t==null?void 0:t.clusterNode){o.info("Cluster identified XBX",g,t.width,n.node(g));const{ranksep:u,nodesep:m}=n.graph();t.graph.setGraph({...t.graph.graph(),ranksep:u+25,nodesep:m});const N=await F(f,t.graph,r,a,n.node(g),s),x=N.elem;z(t,x),t.diff=N.diff||0,o.info("New compound node after recursive render XAX",g,"width",t.width,"height",t.height),_(x,t)}else n.children(g).length>0?(o.trace("Cluster - the non recursive path XBX",g,t.id,t,t.width,"Graph:",n),o.trace(I(t.id,n)),l.set(t.id,{id:I(t.id,n),node:t})):(o.trace("Node - the non recursive path XAX",g,f,n.node(g),i),await q(f,n.node(g),{config:s,dir:i}))})),await w(async()=>{const g=n.edges().map(async function(t){const u=n.edge(t.v,t.w,t.name);o.info("Edge "+t.v+" -> "+t.w+": "+JSON.stringify(t)),o.info("Edge "+t.v+" -> "+t.w+": ",t," ",JSON.stringify(n.edge(t))),o.info("Fix",l,"ids:",t.v,t.w,"Translating: ",l.get(t.v),l.get(t.w)),await W(C,u)});await Promise.all(g)},"processEdges")(),o.info("Graph before layout:",JSON.stringify(X(n))),o.info("############################################# XXX"),o.info("###                Layout                 ### XXX"),o.info("############################################# XXX"),Z(n),o.info("Graph after layout:",JSON.stringify(X(n)));let y=0,{subGraphTitleTotalMargin:v}=K(s);return await Promise.all(ae(n).map(async function(g){var u;const t=n.node(g);if(o.info("Position XBX => "+g+": ("+t.x,","+t.y,") width: ",t.width," height: ",t.height),t==null?void 0:t.clusterNode)t.y+=v,o.info("A tainted cluster node XBX1",g,t.id,t.width,t.height,t.x,t.y,n.parent(g)),l.get(t.id).node=t,O(t);else if(n.children(g).length>0){o.info("A pure cluster node XBX1",g,t.id,t.x,t.y,t.width,t.height,n.parent(g)),t.height+=v,n.node(t.parentId);const m=(t==null?void 0:t.padding)/2||0,N=((u=t==null?void 0:t.labelBBox)==null?void 0:u.height)||0,x=N-m||0;o.debug("OffsetY",x,"labelHeight",N,"halfPadding",m),await Q(p,t),l.get(t.id).node=t}else{const m=n.node(t.parentId);t.y+=v/2,o.info("A regular node XBX1 - using the padding",t.id,"parent",t.parentId,t.width,t.height,t.x,t.y,"offsetY",t.offsetY,"parent",m,m==null?void 0:m.offsetY,t),O(t)}})),n.edges().forEach(function(g){const t=n.edge(g);o.info("Edge "+g.v+" -> "+g.w+": "+JSON.stringify(t),t),t.points.forEach(x=>x.y+=v/2);const u=n.node(g.v);var m=n.node(g.w);const N=U(E,t,l,r,u,m,a);V(t,N)}),n.nodes().forEach(function(g){const t=n.node(g);o.info(g,t.type,t.diff),t.isGroup&&(y=t.diff)}),o.warn("Returning from recursive render XAX",c,y),{elem:c,diff:y}},"recursiveRender"),ar=w(async(e,n)=>{var s,i,c,p,E,C;const r=new k({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((s=e.config)==null?void 0:s.nodeSpacing)||((c=(i=e.config)==null?void 0:i.flowchart)==null?void 0:c.nodeSpacing)||e.nodeSpacing,ranksep:((p=e.config)==null?void 0:p.rankSpacing)||((C=(E=e.config)==null?void 0:E.flowchart)==null?void 0:C.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),a=n.select("g");L(a,e.markers,e.type,e.diagramId),j(),M(),Y(),ne(),e.nodes.forEach(f=>{r.setNode(f.id,{...f}),f.parentId&&r.setParent(f.id,f.parentId)}),o.debug("Edges:",e.edges),e.edges.forEach(f=>{if(f.start===f.end){const h=f.start,y=h+"---"+h+"---1",v=h+"---"+h+"---2",g=r.node(h);r.setNode(y,{domId:y,id:y,parentId:g.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),r.setParent(y,g.parentId),r.setNode(v,{domId:v,id:v,parentId:g.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),r.setParent(v,g.parentId);const t=structuredClone(f),u=structuredClone(f),m=structuredClone(f);t.label="",t.arrowTypeEnd="none",t.id=h+"-cyclic-special-1",u.arrowTypeEnd="none",u.id=h+"-cyclic-special-mid",m.label="",g.isGroup&&(t.fromCluster=h,m.toCluster=h),m.id=h+"-cyclic-special-2",r.setEdge(h,y,t,h+"-cyclic-special-0"),r.setEdge(y,v,u,h+"-cyclic-special-1"),r.setEdge(v,h,m,h+"-cyc<lic-special-2")}else r.setEdge(f.start,f.end,{...f},f.id)}),o.warn("Graph at first:",JSON.stringify(X(r))),ie(r),o.warn("Graph after XAX:",JSON.stringify(X(r)));const d=H();await F(a,r,e.type,e.diagramId,void 0,d)},"render");export{ar as render};
