var i=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(i||{});function r(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}const g=6048e5,l=6e4,b=36e5;function s(e){const t=r(e);return t.setHours(0,0,0,0),t}function a(e){const t=r(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function f(e,t){const n=s(e),o=s(t),c=+n-a(n),u=+o-a(o);return Math.round((c-u)/864e5)}export{i as F,b as a,l as b,f as d,g as m,r as t};
