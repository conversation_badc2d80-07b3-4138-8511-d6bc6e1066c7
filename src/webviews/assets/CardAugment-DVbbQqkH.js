var Qn=Object.defineProperty;var Zn=(e,t,n)=>t in e?Qn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var H=(e,t,n)=>Zn(e,typeof t!="symbol"?t+"":t,n);import{bm as to,bn as eo,a2 as ie,aR as no,al as On,w as En,aU as oo,N as Rt,x as k,Z as ro,G as ue,H as At,S as Le,b as nt,X as Pt,V as ke,ak as $e,a5 as ye,y as _t,I as pe,aE as fe,C as le,E as io,u as Kt,F as xt,ay as xe,a6 as Tn,aa as _n,z as de,A as je,B as Cn,a0 as Ke,v as ao,$ as so,ad as co,P as gt,D as Ge,m as uo,as as po,T as fo,a7 as lo,a1 as vo,ac as ho,au as mo,a as Je,R as Oe,O as go}from"./GuardedIcon-BFT2yJIo.js";import{a as He,b as Me}from"./IconButtonAugment-CR0fVrwD.js";function Qe(e,t,n){var o=to(e,t);o&&o.set&&(e[t]=n,eo(()=>{e[t]=null}))}function Ze(e){return function(...t){return t[0].stopPropagation(),e==null?void 0:e.apply(this,t)}}function yr(e){return function(...t){return t[0].preventDefault(),e==null?void 0:e.apply(this,t)}}var tn=NaN,yo="[object Symbol]",bo=/^\s+|\s+$/g,wo=/^[-+]0x[0-9a-f]+$/i,xo=/^0b[01]+$/i,Oo=/^0o[0-7]+$/i,Eo=parseInt,To=typeof ie=="object"&&ie&&ie.Object===Object&&ie,_o=typeof self=="object"&&self&&self.Object===Object&&self,Co=To||_o||Function("return this")(),Do=Object.prototype.toString,Ao=Math.max,Lo=Math.min,Ee=function(){return Co.Date.now()};function Se(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function en(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(c){return!!c&&typeof c=="object"}(o)&&Do.call(o)==yo}(e))return tn;if(Se(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Se(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(bo,"");var n=xo.test(e);return n||Oo.test(e)?Eo(e.slice(2),n?2:8):wo.test(e)?tn:+e}const nn=no(function(e,t,n){var o,c,i,s,a,p,v=0,f=!1,l=!1,h=!0;if(typeof e!="function")throw new TypeError("Expected a function");function m(y){var r=o,w=c;return o=c=void 0,v=y,s=e.apply(w,r)}function x(y){var r=y-p;return p===void 0||r>=t||r<0||l&&y-v>=i}function O(){var y=Ee();if(x(y))return g(y);a=setTimeout(O,function(r){var w=t-(r-p);return l?Lo(w,i-(r-v)):w}(y))}function g(y){return a=void 0,h&&o?m(y):(o=c=void 0,s)}function E(){var y=Ee(),r=x(y);if(o=arguments,c=this,p=y,r){if(a===void 0)return function(w){return v=w,a=setTimeout(O,t),f?m(w):s}(p);if(l)return a=setTimeout(O,t),m(p)}return a===void 0&&(a=setTimeout(O,t)),s}return t=en(t)||0,Se(n)&&(f=!!n.leading,i=(l="maxWait"in n)?Ao(en(n.maxWait)||0,t):i,h="trailing"in n?!!n.trailing:h),E.cancel=function(){a!==void 0&&clearTimeout(a),v=0,o=p=c=a=void 0},E.flush=function(){return a===void 0?s:g(Ee())},E});var Y="top",rt="bottom",it="right",K="left",Re="auto",Qt=[Y,rt,it,K],kt="start",Gt="end",ko="clippingParents",Dn="viewport",$t="popper",jo="reference",on=Qt.reduce(function(e,t){return e.concat([t+"-"+kt,t+"-"+Gt])},[]),An=[].concat(Qt,[Re]).reduce(function(e,t){return e.concat([t,t+"-"+kt,t+"-"+Gt])},[]),Ho=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function lt(e){return e?(e.nodeName||"").toLowerCase():null}function J(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Et(e){return e instanceof J(e).Element||e instanceof Element}function ot(e){return e instanceof J(e).HTMLElement||e instanceof HTMLElement}function Ie(e){return typeof ShadowRoot<"u"&&(e instanceof J(e).ShadowRoot||e instanceof ShadowRoot)}const Ln={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},c=t.attributes[n]||{},i=t.elements[n];ot(i)&&lt(i)&&(Object.assign(i.style,o),Object.keys(c).forEach(function(s){var a=c[s];a===!1?i.removeAttribute(s):i.setAttribute(s,a===!0?"":a)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var c=t.elements[o],i=t.attributes[o]||{},s=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(a,p){return a[p]="",a},{});ot(c)&&lt(c)&&(Object.assign(c.style,s),Object.keys(i).forEach(function(a){c.removeAttribute(a)}))})}},requires:["computeStyles"]};function ft(e){return e.split("-")[0]}var Ot=Math.max,ve=Math.min,jt=Math.round;function Pe(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function kn(){return!/^((?!chrome|android).)*safari/i.test(Pe())}function Ht(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),c=1,i=1;t&&ot(e)&&(c=e.offsetWidth>0&&jt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&jt(o.height)/e.offsetHeight||1);var s=(Et(e)?J(e):window).visualViewport,a=!kn()&&n,p=(o.left+(a&&s?s.offsetLeft:0))/c,v=(o.top+(a&&s?s.offsetTop:0))/i,f=o.width/c,l=o.height/i;return{width:f,height:l,top:v,right:p+f,bottom:v+l,left:p,x:p,y:v}}function Ue(e){var t=Ht(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function jn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ie(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function ht(e){return J(e).getComputedStyle(e)}function Mo(e){return["table","td","th"].indexOf(lt(e))>=0}function yt(e){return((Et(e)?e.ownerDocument:e.document)||window.document).documentElement}function be(e){return lt(e)==="html"?e:e.assignedSlot||e.parentNode||(Ie(e)?e.host:null)||yt(e)}function rn(e){return ot(e)&&ht(e).position!=="fixed"?e.offsetParent:null}function Zt(e){for(var t=J(e),n=rn(e);n&&Mo(n)&&ht(n).position==="static";)n=rn(n);return n&&(lt(n)==="html"||lt(n)==="body"&&ht(n).position==="static")?t:n||function(o){var c=/firefox/i.test(Pe());if(/Trident/i.test(Pe())&&ot(o)&&ht(o).position==="fixed")return null;var i=be(o);for(Ie(i)&&(i=i.host);ot(i)&&["html","body"].indexOf(lt(i))<0;){var s=ht(i);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||c&&s.willChange==="filter"||c&&s.filter&&s.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function Fe(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function It(e,t,n){return Ot(e,ve(t,n))}function Hn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Mn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const So={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,c=e.options,i=n.elements.arrow,s=n.modifiersData.popperOffsets,a=ft(n.placement),p=Fe(a),v=[K,it].indexOf(a)>=0?"height":"width";if(i&&s){var f=function(j,A){return Hn(typeof(j=typeof j=="function"?j(Object.assign({},A.rects,{placement:A.placement})):j)!="number"?j:Mn(j,Qt))}(c.padding,n),l=Ue(i),h=p==="y"?Y:K,m=p==="y"?rt:it,x=n.rects.reference[v]+n.rects.reference[p]-s[p]-n.rects.popper[v],O=s[p]-n.rects.reference[p],g=Zt(i),E=g?p==="y"?g.clientHeight||0:g.clientWidth||0:0,y=x/2-O/2,r=f[h],w=E-l[v]-f[m],d=E/2-l[v]/2+y,_=It(r,d,w),C=p;n.modifiersData[o]=((t={})[C]=_,t.centerOffset=_-d,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&jn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Mt(e){return e.split("-")[1]}var Ro={top:"auto",right:"auto",bottom:"auto",left:"auto"};function an(e){var t,n=e.popper,o=e.popperRect,c=e.placement,i=e.variation,s=e.offsets,a=e.position,p=e.gpuAcceleration,v=e.adaptive,f=e.roundOffsets,l=e.isFixed,h=s.x,m=h===void 0?0:h,x=s.y,O=x===void 0?0:x,g=typeof f=="function"?f({x:m,y:O}):{x:m,y:O};m=g.x,O=g.y;var E=s.hasOwnProperty("x"),y=s.hasOwnProperty("y"),r=K,w=Y,d=window;if(v){var _=Zt(n),C="clientHeight",j="clientWidth";_===J(n)&&ht(_=yt(n)).position!=="static"&&a==="absolute"&&(C="scrollHeight",j="scrollWidth"),(c===Y||(c===K||c===it)&&i===Gt)&&(w=rt,O-=(l&&_===d&&d.visualViewport?d.visualViewport.height:_[C])-o.height,O*=p?1:-1),(c===K||(c===Y||c===rt)&&i===Gt)&&(r=it,m-=(l&&_===d&&d.visualViewport?d.visualViewport.width:_[j])-o.width,m*=p?1:-1)}var A,R=Object.assign({position:a},v&&Ro),M=f===!0?function(W,V){var I=W.x,F=W.y,S=V.devicePixelRatio||1;return{x:jt(I*S)/S||0,y:jt(F*S)/S||0}}({x:m,y:O},J(n)):{x:m,y:O};return m=M.x,O=M.y,p?Object.assign({},R,((A={})[w]=y?"0":"",A[r]=E?"0":"",A.transform=(d.devicePixelRatio||1)<=1?"translate("+m+"px, "+O+"px)":"translate3d("+m+"px, "+O+"px, 0)",A)):Object.assign({},R,((t={})[w]=y?O+"px":"",t[r]=E?m+"px":"",t.transform="",t))}var ae={passive:!0},Po={left:"right",right:"left",bottom:"top",top:"bottom"};function se(e){return e.replace(/left|right|bottom|top/g,function(t){return Po[t]})}var No={start:"end",end:"start"};function sn(e){return e.replace(/start|end/g,function(t){return No[t]})}function ze(e){var t=J(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Ne(e){return Ht(yt(e)).left+ze(e).scrollLeft}function Xe(e){var t=ht(e),n=t.overflow,o=t.overflowX,c=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+c+o)}function Sn(e){return["html","body","#document"].indexOf(lt(e))>=0?e.ownerDocument.body:ot(e)&&Xe(e)?e:Sn(be(e))}function Ft(e,t){var n;t===void 0&&(t=[]);var o=Sn(e),c=o===((n=e.ownerDocument)==null?void 0:n.body),i=J(o),s=c?[i].concat(i.visualViewport||[],Xe(o)?o:[]):o,a=t.concat(s);return c?a:a.concat(Ft(be(s)))}function Ve(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function cn(e,t,n){return t===Dn?Ve(function(o,c){var i=J(o),s=yt(o),a=i.visualViewport,p=s.clientWidth,v=s.clientHeight,f=0,l=0;if(a){p=a.width,v=a.height;var h=kn();(h||!h&&c==="fixed")&&(f=a.offsetLeft,l=a.offsetTop)}return{width:p,height:v,x:f+Ne(o),y:l}}(e,n)):Et(t)?function(o,c){var i=Ht(o,!1,c==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):Ve(function(o){var c,i=yt(o),s=ze(o),a=(c=o.ownerDocument)==null?void 0:c.body,p=Ot(i.scrollWidth,i.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),v=Ot(i.scrollHeight,i.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),f=-s.scrollLeft+Ne(o),l=-s.scrollTop;return ht(a||i).direction==="rtl"&&(f+=Ot(i.clientWidth,a?a.clientWidth:0)-p),{width:p,height:v,x:f,y:l}}(yt(e)))}function Vo(e,t,n,o){var c=t==="clippingParents"?function(p){var v=Ft(be(p)),f=["absolute","fixed"].indexOf(ht(p).position)>=0&&ot(p)?Zt(p):p;return Et(f)?v.filter(function(l){return Et(l)&&jn(l,f)&&lt(l)!=="body"}):[]}(e):[].concat(t),i=[].concat(c,[n]),s=i[0],a=i.reduce(function(p,v){var f=cn(e,v,o);return p.top=Ot(f.top,p.top),p.right=ve(f.right,p.right),p.bottom=ve(f.bottom,p.bottom),p.left=Ot(f.left,p.left),p},cn(e,s,o));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Rn(e){var t,n=e.reference,o=e.element,c=e.placement,i=c?ft(c):null,s=c?Mt(c):null,a=n.x+n.width/2-o.width/2,p=n.y+n.height/2-o.height/2;switch(i){case Y:t={x:a,y:n.y-o.height};break;case rt:t={x:a,y:n.y+n.height};break;case it:t={x:n.x+n.width,y:p};break;case K:t={x:n.x-o.width,y:p};break;default:t={x:n.x,y:n.y}}var v=i?Fe(i):null;if(v!=null){var f=v==="y"?"height":"width";switch(s){case kt:t[v]=t[v]-(n[f]/2-o[f]/2);break;case Gt:t[v]=t[v]+(n[f]/2-o[f]/2)}}return t}function Jt(e,t){t===void 0&&(t={});var n=t,o=n.placement,c=o===void 0?e.placement:o,i=n.strategy,s=i===void 0?e.strategy:i,a=n.boundary,p=a===void 0?ko:a,v=n.rootBoundary,f=v===void 0?Dn:v,l=n.elementContext,h=l===void 0?$t:l,m=n.altBoundary,x=m!==void 0&&m,O=n.padding,g=O===void 0?0:O,E=Hn(typeof g!="number"?g:Mn(g,Qt)),y=h===$t?jo:$t,r=e.rects.popper,w=e.elements[x?y:h],d=Vo(Et(w)?w:w.contextElement||yt(e.elements.popper),p,f,s),_=Ht(e.elements.reference),C=Rn({reference:_,element:r,placement:c}),j=Ve(Object.assign({},r,C)),A=h===$t?j:_,R={top:d.top-A.top+E.top,bottom:A.bottom-d.bottom+E.bottom,left:d.left-A.left+E.left,right:A.right-d.right+E.right},M=e.modifiersData.offset;if(h===$t&&M){var W=M[c];Object.keys(R).forEach(function(V){var I=[it,rt].indexOf(V)>=0?1:-1,F=[Y,rt].indexOf(V)>=0?"y":"x";R[V]+=W[F]*I})}return R}function Wo(e,t){t===void 0&&(t={});var n=t,o=n.placement,c=n.boundary,i=n.rootBoundary,s=n.padding,a=n.flipVariations,p=n.allowedAutoPlacements,v=p===void 0?An:p,f=Mt(o),l=f?a?on:on.filter(function(x){return Mt(x)===f}):Qt,h=l.filter(function(x){return v.indexOf(x)>=0});h.length===0&&(h=l);var m=h.reduce(function(x,O){return x[O]=Jt(e,{placement:O,boundary:c,rootBoundary:i,padding:s})[ft(O)],x},{});return Object.keys(m).sort(function(x,O){return m[x]-m[O]})}const Bo={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var c=n.mainAxis,i=c===void 0||c,s=n.altAxis,a=s===void 0||s,p=n.fallbackPlacements,v=n.padding,f=n.boundary,l=n.rootBoundary,h=n.altBoundary,m=n.flipVariations,x=m===void 0||m,O=n.allowedAutoPlacements,g=t.options.placement,E=ft(g),y=p||(E===g||!x?[se(g)]:function(P){if(ft(P)===Re)return[];var B=se(P);return[sn(P),B,sn(B)]}(g)),r=[g].concat(y).reduce(function(P,B){return P.concat(ft(B)===Re?Wo(t,{placement:B,boundary:f,rootBoundary:l,padding:v,flipVariations:x,allowedAutoPlacements:O}):B)},[]),w=t.rects.reference,d=t.rects.popper,_=new Map,C=!0,j=r[0],A=0;A<r.length;A++){var R=r[A],M=ft(R),W=Mt(R)===kt,V=[Y,rt].indexOf(M)>=0,I=V?"width":"height",F=Jt(t,{placement:R,boundary:f,rootBoundary:l,altBoundary:h,padding:v}),S=V?W?it:K:W?rt:Y;w[I]>d[I]&&(S=se(S));var N=se(S),Q=[];if(i&&Q.push(F[M]<=0),a&&Q.push(F[S]<=0,F[N]<=0),Q.every(function(P){return P})){j=R,C=!1;break}_.set(R,Q)}if(C)for(var Z=function(P){var B=r.find(function(st){var ct=_.get(st);if(ct)return ct.slice(0,P).every(function(mt){return mt})});if(B)return j=B,"break"},tt=x?3:1;tt>0&&Z(tt)!=="break";tt--);t.placement!==j&&(t.modifiersData[o]._skip=!0,t.placement=j,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function un(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function pn(e){return[Y,it,rt,K].some(function(t){return e[t]>=0})}const qo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,c=n.offset,i=c===void 0?[0,0]:c,s=An.reduce(function(f,l){return f[l]=function(h,m,x){var O=ft(h),g=[K,Y].indexOf(O)>=0?-1:1,E=typeof x=="function"?x(Object.assign({},m,{placement:h})):x,y=E[0],r=E[1];return y=y||0,r=(r||0)*g,[K,it].indexOf(O)>=0?{x:r,y}:{x:y,y:r}}(l,t.rects,i),f},{}),a=s[t.placement],p=a.x,v=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=v),t.modifiersData[o]=s}},$o={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,c=n.mainAxis,i=c===void 0||c,s=n.altAxis,a=s!==void 0&&s,p=n.boundary,v=n.rootBoundary,f=n.altBoundary,l=n.padding,h=n.tether,m=h===void 0||h,x=n.tetherOffset,O=x===void 0?0:x,g=Jt(t,{boundary:p,rootBoundary:v,padding:l,altBoundary:f}),E=ft(t.placement),y=Mt(t.placement),r=!y,w=Fe(E),d=w==="x"?"y":"x",_=t.modifiersData.popperOffsets,C=t.rects.reference,j=t.rects.popper,A=typeof O=="function"?O(Object.assign({},t.rects,{placement:t.placement})):O,R=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),M=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,W={x:0,y:0};if(_){if(i){var V,I=w==="y"?Y:K,F=w==="y"?rt:it,S=w==="y"?"height":"width",N=_[w],Q=N+g[I],Z=N-g[F],tt=m?-j[S]/2:0,P=y===kt?C[S]:j[S],B=y===kt?-j[S]:-C[S],st=t.elements.arrow,ct=m&&st?Ue(st):{width:0,height:0},mt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},Nt=mt[I],dt=mt[F],bt=It(0,C[S],ct[S]),te=r?C[S]/2-tt-bt-Nt-R.mainAxis:P-bt-Nt-R.mainAxis,ee=r?-C[S]/2+tt+bt+dt+R.mainAxis:B+bt+dt+R.mainAxis,Ct=t.elements.arrow&&Zt(t.elements.arrow),ne=Ct?w==="y"?Ct.clientTop||0:Ct.clientLeft||0:0,Vt=(V=M==null?void 0:M[w])!=null?V:0,oe=N+ee-Vt,Wt=It(m?ve(Q,N+te-Vt-ne):Q,N,m?Ot(Z,oe):Z);_[w]=Wt,W[w]=Wt-N}if(a){var Bt,qt=w==="x"?Y:K,re=w==="x"?rt:it,et=_[d],u=d==="y"?"height":"width",b=et+g[qt],T=et-g[re],D=[Y,K].indexOf(E)!==-1,L=(Bt=M==null?void 0:M[d])!=null?Bt:0,q=D?b:et-C[u]-j[u]-L+R.altAxis,U=D?et+C[u]+j[u]-L-R.altAxis:T,z=m&&D?function($,vt,X){var G=It($,vt,X);return G>X?X:G}(q,et,U):It(m?q:b,et,m?U:T);_[d]=z,W[d]=z-et}t.modifiersData[o]=W}},requiresIfExists:["offset"]};function Io(e,t,n){n===void 0&&(n=!1);var o,c,i=ot(t),s=ot(t)&&function(l){var h=l.getBoundingClientRect(),m=jt(h.width)/l.offsetWidth||1,x=jt(h.height)/l.offsetHeight||1;return m!==1||x!==1}(t),a=yt(t),p=Ht(e,s,n),v={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(i||!i&&!n)&&((lt(t)!=="body"||Xe(a))&&(v=(o=t)!==J(o)&&ot(o)?{scrollLeft:(c=o).scrollLeft,scrollTop:c.scrollTop}:ze(o)),ot(t)?((f=Ht(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):a&&(f.x=Ne(a))),{x:p.left+v.scrollLeft-f.x,y:p.top+v.scrollTop-f.y,width:p.width,height:p.height}}function Uo(e){var t=new Map,n=new Set,o=[];function c(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(s){if(!n.has(s)){var a=t.get(s);a&&c(a)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||c(i)}),o}var fn={placement:"bottom",modifiers:[],strategy:"absolute"};function ln(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function Fo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,c=t.defaultOptions,i=c===void 0?fn:c;return function(s,a,p){p===void 0&&(p=i);var v,f,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},fn,i),modifiersData:{},elements:{reference:s,popper:a},attributes:{},styles:{}},h=[],m=!1,x={state:l,setOptions:function(g){var E=typeof g=="function"?g(l.options):g;O(),l.options=Object.assign({},i,l.options,E),l.scrollParents={reference:Et(s)?Ft(s):s.contextElement?Ft(s.contextElement):[],popper:Ft(a)};var y,r,w=function(d){var _=Uo(d);return Ho.reduce(function(C,j){return C.concat(_.filter(function(A){return A.phase===j}))},[])}((y=[].concat(o,l.options.modifiers),r=y.reduce(function(d,_){var C=d[_.name];return d[_.name]=C?Object.assign({},C,_,{options:Object.assign({},C.options,_.options),data:Object.assign({},C.data,_.data)}):_,d},{}),Object.keys(r).map(function(d){return r[d]})));return l.orderedModifiers=w.filter(function(d){return d.enabled}),l.orderedModifiers.forEach(function(d){var _=d.name,C=d.options,j=C===void 0?{}:C,A=d.effect;if(typeof A=="function"){var R=A({state:l,name:_,instance:x,options:j}),M=function(){};h.push(R||M)}}),x.update()},forceUpdate:function(){if(!m){var g=l.elements,E=g.reference,y=g.popper;if(ln(E,y)){l.rects={reference:Io(E,Zt(y),l.options.strategy==="fixed"),popper:Ue(y)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(A){return l.modifiersData[A.name]=Object.assign({},A.data)});for(var r=0;r<l.orderedModifiers.length;r++)if(l.reset!==!0){var w=l.orderedModifiers[r],d=w.fn,_=w.options,C=_===void 0?{}:_,j=w.name;typeof d=="function"&&(l=d({state:l,options:C,name:j,instance:x})||l)}else l.reset=!1,r=-1}}},update:(v=function(){return new Promise(function(g){x.forceUpdate(),g(l)})},function(){return f||(f=new Promise(function(g){Promise.resolve().then(function(){f=void 0,g(v())})})),f}),destroy:function(){O(),m=!0}};if(!ln(s,a))return x;function O(){h.forEach(function(g){return g()}),h=[]}return x.setOptions(p).then(function(g){!m&&p.onFirstUpdate&&p.onFirstUpdate(g)}),x}}var zo=Fo({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,c=o.scroll,i=c===void 0||c,s=o.resize,a=s===void 0||s,p=J(t.elements.popper),v=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&v.forEach(function(f){f.addEventListener("scroll",n.update,ae)}),a&&p.addEventListener("resize",n.update,ae),function(){i&&v.forEach(function(f){f.removeEventListener("scroll",n.update,ae)}),a&&p.removeEventListener("resize",n.update,ae)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Rn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,c=o===void 0||o,i=n.adaptive,s=i===void 0||i,a=n.roundOffsets,p=a===void 0||a,v={placement:ft(t.placement),variation:Mt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:c,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,an(Object.assign({},v,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:p})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,an(Object.assign({},v,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Ln,qo,Bo,$o,So,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,c=t.rects.popper,i=t.modifiersData.preventOverflow,s=Jt(t,{elementContext:"reference"}),a=Jt(t,{altBoundary:!0}),p=un(s,o),v=un(a,c,i),f=pn(p),l=pn(v);t.modifiersData[n]={referenceClippingOffsets:p,popperEscapeOffsets:v,isReferenceHidden:f,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":l})}}]}),Pn="tippy-content",Xo="tippy-backdrop",Nn="tippy-arrow",Vn="tippy-svg-arrow",wt={passive:!0,capture:!0},Wn=function(){return document.body};function Te(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function Ye(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function Bn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function dn(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function Dt(e){return[].concat(e)}function vn(e,t){e.indexOf(t)===-1&&e.push(t)}function he(e){return[].slice.call(e)}function hn(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function zt(){return document.createElement("div")}function we(e){return["Element","Fragment"].some(function(t){return Ye(e,t)})}function Yo(e){return we(e)?[e]:function(t){return Ye(t,"NodeList")}(e)?he(e):Array.isArray(e)?e:he(document.querySelectorAll(e))}function _e(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function mn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function Ce(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(c){e[o](c,n)})}function gn(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var pt={isTouch:!1},yn=0;function Ko(){pt.isTouch||(pt.isTouch=!0,window.performance&&document.addEventListener("mousemove",qn))}function qn(){var e=performance.now();e-yn<20&&(pt.isTouch=!1,document.removeEventListener("mousemove",qn)),yn=e}function Go(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Jo=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,at=Object.assign({appendTo:Wn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Qo=Object.keys(at);function $n(e){var t=(e.plugins||[]).reduce(function(n,o){var c,i=o.name,s=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(c=at[i])!=null?c:s),n},{});return Object.assign({},e,t)}function bn(e,t){var n=Object.assign({},t,{content:Bn(t.content,[e])},t.ignoreAttributes?{}:function(o,c){return(c?Object.keys($n(Object.assign({},at,{plugins:c}))):Qo).reduce(function(i,s){var a=(o.getAttribute("data-tippy-"+s)||"").trim();if(!a)return i;if(s==="content")i[s]=a;else try{i[s]=JSON.parse(a)}catch{i[s]=a}return i},{})}(e,t.plugins));return n.aria=Object.assign({},at.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var Zo=function(){return"innerHTML"};function We(e,t){e[Zo()]=t}function wn(e){var t=zt();return e===!0?t.className=Nn:(t.className=Vn,we(e)?t.appendChild(e):We(t,e)),t}function xn(e,t){we(t.content)?(We(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?We(e,t.content):e.textContent=t.content)}function Be(e){var t=e.firstElementChild,n=he(t.children);return{box:t,content:n.find(function(o){return o.classList.contains(Pn)}),arrow:n.find(function(o){return o.classList.contains(Nn)||o.classList.contains(Vn)}),backdrop:n.find(function(o){return o.classList.contains(Xo)})}}function In(e){var t=zt(),n=zt();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=zt();function c(i,s){var a=Be(t),p=a.box,v=a.content,f=a.arrow;s.theme?p.setAttribute("data-theme",s.theme):p.removeAttribute("data-theme"),typeof s.animation=="string"?p.setAttribute("data-animation",s.animation):p.removeAttribute("data-animation"),s.inertia?p.setAttribute("data-inertia",""):p.removeAttribute("data-inertia"),p.style.maxWidth=typeof s.maxWidth=="number"?s.maxWidth+"px":s.maxWidth,s.role?p.setAttribute("role",s.role):p.removeAttribute("role"),i.content===s.content&&i.allowHTML===s.allowHTML||xn(v,e.props),s.arrow?f?i.arrow!==s.arrow&&(p.removeChild(f),p.appendChild(wn(s.arrow))):p.appendChild(wn(s.arrow)):f&&p.removeChild(f)}return o.className=Pn,o.setAttribute("data-state","hidden"),xn(o,e.props),t.appendChild(n),n.appendChild(o),c(e.props,e.props),{popper:t,onUpdate:c}}In.$$tippy=!0;var tr=1,ce=[],De=[];function er(e,t){var n,o,c,i,s,a,p,v,f=bn(e,Object.assign({},at,$n(hn(t)))),l=!1,h=!1,m=!1,x=!1,O=[],g=dn(Ct,f.interactiveDebounce),E=tr++,y=(v=f.plugins).filter(function(u,b){return v.indexOf(u)===b}),r={id:E,reference:e,popper:zt(),popperInstance:null,props:f,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:y,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(c)},setProps:function(u){if(!r.state.isDestroyed){N("onBeforeUpdate",[r,u]),te();var b=r.props,T=bn(e,Object.assign({},b,hn(u),{ignoreAttributes:!0}));r.props=T,bt(),b.interactiveDebounce!==T.interactiveDebounce&&(tt(),g=dn(Ct,T.interactiveDebounce)),b.triggerTarget&&!T.triggerTarget?Dt(b.triggerTarget).forEach(function(D){D.removeAttribute("aria-expanded")}):T.triggerTarget&&e.removeAttribute("aria-expanded"),Z(),S(),_&&_(b,T),r.popperInstance&&(Wt(),qt().forEach(function(D){requestAnimationFrame(D._tippy.popperInstance.forceUpdate)})),N("onAfterUpdate",[r,u])}},setContent:function(u){r.setProps({content:u})},show:function(){var u=r.state.isVisible,b=r.state.isDestroyed,T=!r.state.isEnabled,D=pt.isTouch&&!r.props.touch,L=Te(r.props.duration,0,at.duration);if(!(u||b||T||D)&&!W().hasAttribute("disabled")&&(N("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,M()&&(d.style.visibility="visible"),S(),ct(),r.state.isMounted||(d.style.transition="none"),M()){var q=I();_e([q.box,q.content],0)}a=function(){var U;if(r.state.isVisible&&!x){if(x=!0,d.offsetHeight,d.style.transition=r.props.moveTransition,M()&&r.props.animation){var z=I(),$=z.box,vt=z.content;_e([$,vt],L),mn([$,vt],"visible")}Q(),Z(),vn(De,r),(U=r.popperInstance)==null||U.forceUpdate(),N("onMount",[r]),r.props.animation&&M()&&function(X,G){Nt(X,G)}(L,function(){r.state.isShown=!0,N("onShown",[r])})}},function(){var U,z=r.props.appendTo,$=W();U=r.props.interactive&&z===Wn||z==="parent"?$.parentNode:Bn(z,[$]),U.contains(d)||U.appendChild(d),r.state.isMounted=!0,Wt()}()}},hide:function(){var u=!r.state.isVisible,b=r.state.isDestroyed,T=!r.state.isEnabled,D=Te(r.props.duration,1,at.duration);if(!(u||b||T)&&(N("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,x=!1,l=!1,M()&&(d.style.visibility="hidden"),tt(),mt(),S(!0),M()){var L=I(),q=L.box,U=L.content;r.props.animation&&(_e([q,U],D),mn([q,U],"hidden"))}Q(),Z(),r.props.animation?M()&&function(z,$){Nt(z,function(){!r.state.isVisible&&d.parentNode&&d.parentNode.contains(d)&&$()})}(D,r.unmount):r.unmount()}},hideWithInteractivity:function(u){V().addEventListener("mousemove",g),vn(ce,g),g(u)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(Bt(),qt().forEach(function(u){u._tippy.unmount()}),d.parentNode&&d.parentNode.removeChild(d),De=De.filter(function(u){return u!==r}),r.state.isMounted=!1,N("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),te(),delete e._tippy,r.state.isDestroyed=!0,N("onDestroy",[r]))}};if(!f.render)return r;var w=f.render(r),d=w.popper,_=w.onUpdate;d.setAttribute("data-tippy-root",""),d.id="tippy-"+r.id,r.popper=d,e._tippy=r,d._tippy=r;var C=y.map(function(u){return u.fn(r)}),j=e.hasAttribute("aria-expanded");return bt(),Z(),S(),N("onCreate",[r]),f.showOnCreate&&re(),d.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),d.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&V().addEventListener("mousemove",g)}),r;function A(){var u=r.props.touch;return Array.isArray(u)?u:[u,0]}function R(){return A()[0]==="hold"}function M(){var u;return!((u=r.props.render)==null||!u.$$tippy)}function W(){return p||e}function V(){var u,b,T=W().parentNode;return T&&(b=Dt(T)[0])!=null&&(u=b.ownerDocument)!=null&&u.body?b.ownerDocument:document}function I(){return Be(d)}function F(u){return r.state.isMounted&&!r.state.isVisible||pt.isTouch||i&&i.type==="focus"?0:Te(r.props.delay,u?0:1,at.delay)}function S(u){u===void 0&&(u=!1),d.style.pointerEvents=r.props.interactive&&!u?"":"none",d.style.zIndex=""+r.props.zIndex}function N(u,b,T){var D;T===void 0&&(T=!0),C.forEach(function(L){L[u]&&L[u].apply(L,b)}),T&&(D=r.props)[u].apply(D,b)}function Q(){var u=r.props.aria;if(u.content){var b="aria-"+u.content,T=d.id;Dt(r.props.triggerTarget||e).forEach(function(D){var L=D.getAttribute(b);if(r.state.isVisible)D.setAttribute(b,L?L+" "+T:T);else{var q=L&&L.replace(T,"").trim();q?D.setAttribute(b,q):D.removeAttribute(b)}})}}function Z(){!j&&r.props.aria.expanded&&Dt(r.props.triggerTarget||e).forEach(function(u){r.props.interactive?u.setAttribute("aria-expanded",r.state.isVisible&&u===W()?"true":"false"):u.removeAttribute("aria-expanded")})}function tt(){V().removeEventListener("mousemove",g),ce=ce.filter(function(u){return u!==g})}function P(u){if(!pt.isTouch||!m&&u.type!=="mousedown"){var b=u.composedPath&&u.composedPath()[0]||u.target;if(!r.props.interactive||!gn(d,b)){if(Dt(r.props.triggerTarget||e).some(function(T){return gn(T,b)})){if(pt.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else N("onClickOutside",[r,u]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),h=!0,setTimeout(function(){h=!1}),r.state.isMounted||mt())}}}function B(){m=!0}function st(){m=!1}function ct(){var u=V();u.addEventListener("mousedown",P,!0),u.addEventListener("touchend",P,wt),u.addEventListener("touchstart",st,wt),u.addEventListener("touchmove",B,wt)}function mt(){var u=V();u.removeEventListener("mousedown",P,!0),u.removeEventListener("touchend",P,wt),u.removeEventListener("touchstart",st,wt),u.removeEventListener("touchmove",B,wt)}function Nt(u,b){var T=I().box;function D(L){L.target===T&&(Ce(T,"remove",D),b())}if(u===0)return b();Ce(T,"remove",s),Ce(T,"add",D),s=D}function dt(u,b,T){T===void 0&&(T=!1),Dt(r.props.triggerTarget||e).forEach(function(D){D.addEventListener(u,b,T),O.push({node:D,eventType:u,handler:b,options:T})})}function bt(){var u;R()&&(dt("touchstart",ee,{passive:!0}),dt("touchend",ne,{passive:!0})),(u=r.props.trigger,u.split(/\s+/).filter(Boolean)).forEach(function(b){if(b!=="manual")switch(dt(b,ee),b){case"mouseenter":dt("mouseleave",ne);break;case"focus":dt(Jo?"focusout":"blur",Vt);break;case"focusin":dt("focusout",Vt)}})}function te(){O.forEach(function(u){var b=u.node,T=u.eventType,D=u.handler,L=u.options;b.removeEventListener(T,D,L)}),O=[]}function ee(u){var b,T=!1;if(r.state.isEnabled&&!oe(u)&&!h){var D=((b=i)==null?void 0:b.type)==="focus";i=u,p=u.currentTarget,Z(),!r.state.isVisible&&Ye(u,"MouseEvent")&&ce.forEach(function(L){return L(u)}),u.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||l)&&r.props.hideOnClick!==!1&&r.state.isVisible?T=!0:re(u),u.type==="click"&&(l=!T),T&&!D&&et(u)}}function Ct(u){var b=u.target,T=W().contains(b)||d.contains(b);u.type==="mousemove"&&T||function(D,L){var q=L.clientX,U=L.clientY;return D.every(function(z){var $=z.popperRect,vt=z.popperState,X=z.props.interactiveBorder,G=vt.placement.split("-")[0],ut=vt.modifiersData.offset;if(!ut)return!0;var Un=G==="bottom"?ut.top.y:0,Fn=G==="top"?ut.bottom.y:0,zn=G==="right"?ut.left.x:0,Xn=G==="left"?ut.right.x:0,Yn=$.top-U+Un>X,Kn=U-$.bottom-Fn>X,Gn=$.left-q+zn>X,Jn=q-$.right-Xn>X;return Yn||Kn||Gn||Jn})}(qt().concat(d).map(function(D){var L,q=(L=D._tippy.popperInstance)==null?void 0:L.state;return q?{popperRect:D.getBoundingClientRect(),popperState:q,props:f}:null}).filter(Boolean),u)&&(tt(),et(u))}function ne(u){oe(u)||r.props.trigger.indexOf("click")>=0&&l||(r.props.interactive?r.hideWithInteractivity(u):et(u))}function Vt(u){r.props.trigger.indexOf("focusin")<0&&u.target!==W()||r.props.interactive&&u.relatedTarget&&d.contains(u.relatedTarget)||et(u)}function oe(u){return!!pt.isTouch&&R()!==u.type.indexOf("touch")>=0}function Wt(){Bt();var u=r.props,b=u.popperOptions,T=u.placement,D=u.offset,L=u.getReferenceClientRect,q=u.moveTransition,U=M()?Be(d).arrow:null,z=L?{getBoundingClientRect:L,contextElement:L.contextElement||W()}:e,$=[{name:"offset",options:{offset:D}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!q}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(vt){var X=vt.state;if(M()){var G=I().box;["placement","reference-hidden","escaped"].forEach(function(ut){ut==="placement"?G.setAttribute("data-placement",X.placement):X.attributes.popper["data-popper-"+ut]?G.setAttribute("data-"+ut,""):G.removeAttribute("data-"+ut)}),X.attributes.popper={}}}}];M()&&U&&$.push({name:"arrow",options:{element:U,padding:3}}),$.push.apply($,(b==null?void 0:b.modifiers)||[]),r.popperInstance=zo(z,d,Object.assign({},b,{placement:T,onFirstUpdate:a,modifiers:$}))}function Bt(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function qt(){return he(d.querySelectorAll("[data-tippy-root]"))}function re(u){r.clearDelayTimeouts(),u&&N("onTrigger",[r,u]),ct();var b=F(!0),T=A(),D=T[0],L=T[1];pt.isTouch&&D==="hold"&&L&&(b=L),b?n=setTimeout(function(){r.show()},b):r.show()}function et(u){if(r.clearDelayTimeouts(),N("onUntrigger",[r,u]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(u.type)>=0&&l)){var b=F(!1);b?o=setTimeout(function(){r.state.isVisible&&r.hide()},b):c=requestAnimationFrame(function(){r.hide()})}}else mt()}}function Ut(e,t){t===void 0&&(t={});var n=at.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Ko,wt),window.addEventListener("blur",Go);var o=Object.assign({},t,{plugins:n}),c=Yo(e).reduce(function(i,s){var a=s&&er(s,o);return a&&i.push(a),i},[]);return we(e)?c[0]:c}Ut.defaultProps=at,Ut.setDefaultProps=function(e){Object.keys(e).forEach(function(t){at[t]=e[t]})},Ut.currentInput=pt,Object.assign({},Ln,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Ut.setDefaultProps({render:In});var St=(e=>(e.Hover="hover",e.Click="click",e))(St||{});const Xt=class Xt extends Event{constructor(){super(Xt.eventType,{bubbles:!0})}static isEvent(t){return t.type===Xt.eventType}};H(Xt,"eventType","augment-ds-event__close-tooltip-request");let Lt=Xt;const ge=class ge{constructor(t){H(this,"debouncedHoverStart");H(this,"debouncedHoverEnd");H(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});H(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});H(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});H(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=nn(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=nn(t.onHoverEnd,ge.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};H(ge,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let me=ge;function qe(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const nr=Symbol("hover-action-context");function br(e=100){const t=En(!1);On(nr,t);const n=new me({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return qe(o,n)}}const Yt=class Yt{constructor(t){H(this,"_state");H(this,"_tippy");H(this,"_triggerElement");H(this,"_contentElement");H(this,"_contentProps");H(this,"_hoverContext");H(this,"_referenceClientRect");H(this,"_hasPointerEvents",!0);H(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(c=>({...c,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});H(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});H(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});H(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});H(this,"forceControlSetOpen",t=>{t!==void 0&&this._setOpen(t)});H(this,"externalControlSetOpen",t=>{this._opts.open=t,this.forceControlSetOpen(t)});H(this,"updateTippyTheme",t=>{this._opts.tippyTheme!==t&&(this._opts.tippyTheme=t,this._updateTippy())});H(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});H(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:this._opts.offset??[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed",modifiers:[{name:"preventOverflow",options:{padding:this._opts.nested?12:0}}]},duration:0,delay:0,placement:or(this._contentProps),hideOnClick:!0,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(c=>{var i,s;c.open?(i=this._tippy)==null||i.show():(s=this._tippy)==null||s.hide()});this._tippy=Ut(this._triggerElement,{...t,onDestroy:o})}});H(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});H(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&qe(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:c=>{this._referenceClientRect=c,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});H(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&qe(this._contentElement,this._hoverContext);this._updateTippy();const c=function(i,s){const a=new ResizeObserver(()=>s());return a.observe(i),()=>a.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),c()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});H(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new Lt)});this._opts=t,this._state=En({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new me({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(St.Hover)}get supportsClick(){return this._opts.triggerOn.includes(St.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??Yt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return oo(this._state).open}};H(Yt,"CONTEXT_KEY","augment-tooltip-context"),H(Yt,"DEFAULT_DELAY_DURATION_MS",250);let Tt=Yt;function or(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function rr(e,t){Rt(t,!0);const n=k(t,"onHoverStart",3,()=>{}),o=k(t,"onHoverEnd",3,()=>{}),c=k(t,"triggerOn",19,()=>[St.Hover,St.Click]),i=new Tt({defaultOpen:t.defaultOpen,open:t.open,onOpenChange:t.onOpenChange,delayDurationMs:t.delayDurationMs,nested:t.nested,onHoverStart:n(),onHoverEnd:o(),triggerOn:c(),tippyTheme:t.tippyTheme,hasPointerEvents:t.hasPointerEvents,offset:t.offset});On(Tt.CONTEXT_KEY,i),ro(()=>{i.externalControlSetOpen(t.open),i.updateTippyTheme(t.tippyTheme)});var s=ue(),a=At(s);return Le(a,()=>t.children??ke),nt(e,s),Pt({requestOpen:()=>i.openTooltip(),requestClose:()=>i.closeTooltip()})}var ir=_t('<div role="button" tabindex="-1"><!></div>');function ar(e,t){Rt(t,!1);let n=k(t,"referenceClientRect",24,()=>{}),o=k(t,"class",8,"");const c=$e(Tt.CONTEXT_KEY),i=p=>{c.supportsClick&&(c.toggleTooltip(),p.stopPropagation())};ye();var s=ir(),a=Kt(s);pe(a,t,"default",{},null),fe(()=>xt("click",s,i)),fe(()=>xt("keydown",s,function(p){Me.call(this,t,p)})),He(s,(p,v)=>{var f;return(f=c.registerTrigger)==null?void 0:f.call(c,p,v)},n),le(()=>io(s,1,`l-tooltip-trigger ${o()}`)),nt(e,s),Pt()}function sr(e,t){Rt(t,!1);const[n,o]=Tn(),c=()=>_n(p,"$state",n);let i=k(t,"onEscapeKeyDown",8,()=>{}),s=k(t,"onClickOutside",8,()=>{});const a=$e(Tt.CONTEXT_KEY),p=a.state,v=h=>{h.target!==null&&h.target instanceof Node&&a.contentElement&&a.triggerElement&&c().open&&(h.composedPath().includes(a.contentElement)||h.composedPath().includes(a.triggerElement)||(a.closeTooltip(),s()(h)))},f=h=>{h.target!==null&&h.target instanceof Node&&a.contentElement&&c().open&&h.key==="Escape"&&(a.closeTooltip(),i()(h))},l=h=>{h.target===window&&a.requestClose()};ye(),xt("click",xe,function(...h){var m;(m=c().open?v:void 0)==null||m.apply(this,h)},!0),xt("keydown",xe,function(...h){var m;(m=c().open?f:void 0)==null||m.apply(this,h)},!0),xt("blur",xe,function(...h){var m;(m=c().open?l:void 0)==null||m.apply(this,h)},!0),Pt(),o()}var cr=_t('<div class="l-tooltip-contents svelte-1h3nmyv" role="button" tabindex="-1"><!></div>'),ur=_t("<!> <!>",1);function pr(e,t){Rt(t,!1);const[n,o]=Tn(),c=()=>_n(l,"$state",n);let i=k(t,"onEscapeKeyDown",8,()=>{}),s=k(t,"onClickOutside",8,()=>{}),a=k(t,"onRequestClose",8,()=>{}),p=k(t,"side",8,"top"),v=k(t,"align",8,"center");const f=$e(Tt.CONTEXT_KEY),l=f.state,h=E=>{var y;if(Lt.isEvent(E)&&E.target&&((y=f.contentElement)!=null&&y.contains(E.target)))return f.closeTooltip(),a()(E),void E.stopPropagation()};ye();var m=ur(),x=At(m);sr(0,{onEscapeKeyDown:i(),onClickOutside:s()});var O=Cn(x,2),g=E=>{var y=cr(),r=Kt(y);pe(r,t,"default",{},null),He(y,(w,d)=>((_,{onRequestClose:C})=>(_.addEventListener(Lt.eventType,C),{destroy:()=>{_.removeEventListener(Lt.eventType,C)}}))(w,d),()=>({onRequestClose:h})),He(y,(w,d)=>{var _;return(_=f.registerContents)==null?void 0:_.call(f,w,d)},()=>({side:p(),align:v()})),fe(()=>xt("click",y,Ze(function(w){Me.call(this,t,w)}))),fe(()=>xt("keydown",y,Ze(function(w){Me.call(this,t,w)}))),le(()=>{Ke(y,"data-position-side",p()),Ke(y,"data-position-align",v())}),nt(E,y)};de(O,E=>{c(),je(()=>c().open)&&E(g)}),nt(e,m),Pt(),o()}const Ae={Root:rr,Trigger:ar,Content:pr};var fr=_t('<div class="svelte-hdzv5n"><!></div>'),lr=_t("<!> <!>",1);function wr(e,t){const n=ao(t);Rt(t,!1);let o=k(t,"content",24,()=>{}),c=k(t,"width",24,()=>{}),i=k(t,"minWidth",24,()=>{}),s=k(t,"maxWidth",8,"250px"),a=k(t,"delayDurationMs",24,()=>{}),p=k(t,"triggerOn",24,()=>[St.Hover]),v=k(t,"side",8,"top"),f=k(t,"nested",8,!1),l=k(t,"hasPointerEvents",24,()=>{}),h=k(t,"offset",24,()=>v()==="top"||v()==="bottom"?[0,5]:[5,0]),m=k(t,"open",24,()=>{}),x=k(t,"align",8,"center"),O=k(t,"class",8,""),g=k(t,"onOpenChange",24,()=>{}),E=k(t,"referenceClientRect",24,()=>{}),y=k(t,"theme",8,""),r=uo(void 0);const w=()=>{var C;return(C=gt(r))==null?void 0:C.requestOpen()},d=()=>{var C;return(C=gt(r))==null?void 0:C.requestClose()};ye();const _=Ge(()=>y()||"");return so(Ae.Root(e,{get delayDurationMs(){return a()},get onOpenChange(){return g()},get triggerOn(){return p()},get nested(){return f()},get hasPointerEvents(){return l()},get offset(){return h()},get open(){return m()},get tippyTheme(){return`default text-tooltip-augment ${gt(_)??""}`},children:(C,j)=>{var A=lr(),R=At(A);Ae.Trigger(R,{get referenceClientRect(){return E()},get class(){return O()},children:(V,I)=>{var F=ue(),S=At(F);pe(S,t,"default",{},null),nt(V,F)},$$slots:{default:!0}});var M=Cn(R,2),W=V=>{Ae.Content(V,{get side(){return v()},get align(){return x()},children:(I,F)=>{var S=fr();let N;var Q=Kt(S),Z=P=>{var B=ue(),st=At(B);pe(st,t,"content",{},null),nt(P,B)},tt=P=>{fo(P,{size:1,class:"tooltip-text",children:(B,st)=>{var ct=lo();le(()=>vo(ct,o())),nt(B,ct)},$$slots:{default:!0}})};de(Q,P=>{je(()=>n.content)?P(Z):P(tt,!1)}),le(P=>N=po(S,"",N,P),[()=>({width:c(),"min-width":i(),"max-width":s()})],Ge),nt(I,S)},$$slots:{default:!0}})};de(M,V=>{co(o()),je(()=>o()||n.content)&&V(W)}),nt(C,A)},$$slots:{default:!0},$$legacy:!0}),C=>ho(r,C),()=>gt(r)),Qe(t,"requestOpen",w),Qe(t,"requestClose",d),Pt({requestOpen:w,requestClose:d})}var dr=_t("<div><!></div>"),vr=_t("<div><!></div>");function xr(e,t){Rt(t,!0);const n=mo();let o=k(t,"size",3,1),c=k(t,"insetContent",3,!1),i=k(t,"variant",3,"surface"),s=k(t,"interactive",3,!1),a=k(t,"includeBackground",3,!0),p=k(t,"borderless",3,!1),v=Oe(()=>t.class),f=Oe(()=>["c-card",`c-card--size-${o()}`,`c-card--${i()}`,c()?"c-card--insetContent":"",s()?"c-card--interactive":"",a()?"c-card--with-background":"",p()?"c-card--borderless":"",gt(v)]),l=Oe(()=>({...go("accent"),class:gt(f).join(" ")}));var h=ue(),m=At(h),x=g=>{var E=dr();Je(E,(r,w,d,_,C,j,A,R,M)=>({...gt(l),role:"button",tabindex:"0",onclick:r,onkeyup:w,onkeydown:d,onmousedown:_,onmouseover:C,onfocus:j,onmouseleave:A,onblur:R,oncontextmenu:M}),[()=>n("click"),()=>n("keyup"),()=>n("keydown"),()=>n("mousedown"),()=>n("mouseover"),()=>n("focus"),()=>n("mouseleave"),()=>n("blur"),()=>n("contextmenu")],"svelte-x444gv");var y=Kt(E);Le(y,()=>t.children??ke),nt(g,E)},O=g=>{var E=vr();Je(E,()=>({...gt(l)}),void 0,"svelte-x444gv");var y=Kt(E);Le(y,()=>t.children??ke),nt(g,E)};de(m,g=>{s()?g(x):g(O,!1)}),nt(e,h),Pt()}export{xr as C,me as H,rr as R,wr as T,St as a,Qe as b,Lt as c,nn as d,Tt as e,pr as f,ar as g,br as h,qe as o,yr as p,Ze as s,Ut as t};
