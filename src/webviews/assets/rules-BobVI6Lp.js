import{N as G,aQ as me,y as $,ac as M,ag as fe,P as e,R as p,u as n,b as l,X as H,H as ge,a8 as he,B as h,T as C,a7 as L,z as K,W as N,ax as ye,a5 as $e,F as be,ay as ke,aa as we,a6 as Fe,w as xe,aP as Re}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{c as y}from"./IconButtonAugment-CR0fVrwD.js";import{l as ze}from"./lodash-DkRojHDE.js";import{M as U}from"./message-broker-BygIEqPd.js";import{B as Ee}from"./ButtonAugment-CWDjQYWT.js";import{M as Te}from"./MonacoMarkdownEditor-CEUFXrwt.js";import{T as Me}from"./TextFieldAugment-DfCJMerV.js";import{T as Ce}from"./CardAugment-DVbbQqkH.js";import{C as Le}from"./chevron-left-CXiZfA98.js";import{R as Ne}from"./chat-types-BDRYChZT.js";import{R as _e,a as E,E as Be}from"./index-DON3DCoY.js";import{O as De}from"./OpenFileButton-CVOPVJP1.js";import{R as Ie}from"./RulesModeSelector-CNCQbfap.js";import"./async-messaging-Bp70swAv.js";import"./index-CWns8XM2.js";import"./BaseTextInput-BaUpeUef.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./focusTrapStack-CDv9v5kQ.js";import"./chat-model-context-39sqbIF3.js";import"./index-B528snJk.js";import"./index-C_brRns6.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";import"./SuccessfulButton-Btt1yYx9.js";import"./chevron-down-CkTAocB8.js";var Oe=$('<div class="c-rule-config svelte-1ivykbn"><div class="c-rule-field c-rule-field-full-width svelte-1ivykbn"><!> <!></div></div>'),Pe=$('<div class="l-file-controls svelte-1ivykbn"><div class="l-file-controls-left svelte-1ivykbn"><div class="c-trigger-section svelte-1ivykbn"><!> <!> <!></div></div> <!></div> <!>',1),Qe=$('<div class="l-rules-editor svelte-1ivykbn"><div class="c-rules-editor__content svelte-1ivykbn"><!></div></div>'),Se=$("<div>Loading...</div>"),We=$('<div class="c-rules-container svelte-1vbu0zh"><!></div>');Re(function(V,X){G(X,!1);const[j,q]=Fe(),_=()=>we(D,"$rule",j),B=new U(y),D=xe(null),J={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===N.loadFile&&t){const o=t.data.content;if(o!==void 0){const i=o.replace(/^\n+/,""),v=E.getDescriptionFrontmatterKey(i),b=E.getRuleTypeFromContent(i),c=E.extractContent(i);D.set({path:t.data.pathName,content:c,type:b,description:v})}}return!0}};ye(()=>{B.registerConsumer(J),y.postMessage({type:N.rulesLoaded})}),$e();var I=We();be("message",ke,function(...s){var t;(t=B.onMessageFromExtension)==null||t.apply(this,s)});var Y=n(I),Z=s=>{(function(t,o){G(o,!0);let i=p(()=>o.rule.path),v=p(()=>o.rule.type),b=p(()=>o.rule.content),c=p(()=>o.rule.description),O=p(()=>E.extractContent(e(b))),k=fe(void 0),te=p(()=>({path:e(i),type:e(v),content:e(b),description:e(c)}));const P=new U(y),se=new me,ae=new Be(y,P,se),re=new _e(P),Q=async(u,a)=>{if(!e(k))return;const w=e(k).getValue(),F=u??e(v),x=a??e(c),T={type:F,path:e(i),content:w,description:x};try{await re.updateRuleContent(T)}catch(m){if(console.error("Failed to save rule:",m),u===void 0&&a===void 0)throw m}},S=(u,a)=>Q(u,a),oe=ze.debounce(S,500),ie=()=>{y.postMessage({type:N.openSettingsPage,data:"guidelines"})};var W=Qe(),ne=n(W),le=n(ne);Te(le,{saveFunction:Q,get value(){return e(O)},set value(a){M(O,a)},get editorInstance(){return e(k)},set editorInstance(a){M(k,a,!0)},header:a=>{var w=Pe(),F=ge(w),x=n(F),T=n(x),m=n(T);Ce(m,{content:"Navigate back to all Rules & Guidelines",children:(r,f)=>{Ee(r,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(d,R)=>{he(d,{slot:"iconLeft",name:"chevron-left",children:(g,z)=>{Le(g,{})},$$slots:{default:!0}})}}})},$$slots:{default:!0}});var A=h(m,2);C(A,{size:1,class:"c-field-label",children:(r,f)=>{var d=L("Trigger:");l(r,d)},$$slots:{default:!0}});var ce=h(A,2);Ie(ce,{onSave:S,get rule(){return e(te)}});var de=h(x,2);De(de,{size:1,get path(){return e(i)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(i)}),"success"),$$slots:{text:(r,f)=>{C(r,{slot:"text",size:1,children:(d,R)=>{var g=L("Open file");l(d,g)},$$slots:{default:!0}})}}});var ve=h(F,2),pe=r=>{var f=Oe(),d=n(f),R=n(d);C(R,{size:1,class:"c-field-label",children:(z,Ae)=>{var ue=L("Description");l(z,ue)},$$slots:{default:!0}});var g=h(R,2);Me(g,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(c)},set value(z){M(c,z)},$$events:{input:()=>oe(e(v),e(c))}}),l(r,f)};K(ve,r=>{e(v)===Ne.AGENT_REQUESTED&&r(pe)}),l(a,w)},$$slots:{header:!0}}),l(t,W),H()})(s,{get rule(){return _()}})},ee=s=>{var t=Se();l(s,t)};K(Y,s=>{_()!==null?s(Z):s(ee,!1)}),l(V,I),H(),q()},{target:document.getElementById("app")});
