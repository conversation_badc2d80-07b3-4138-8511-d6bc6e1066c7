import{t as _e,f as De,a as He,u as w,b as r,N as ye,x as me,aQ as Ne,ax as Te,W as be,w as oe,a3 as he,a4 as Ue,a5 as xe,G as ze,H as de,z as ae,aa as Q,X as Ce,a6 as Ae,P as e,m as y,ac as h,ad as $e,y as O,$ as Fe,a8 as V,a7 as J,C as ke,a1 as Ge,A as q,D as A,B as ne,a9 as qe,ae as We,ab as Qe,T as Oe,ai as Ve,F as Xe,ay as Je,aP as Ke}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{h as Ye,c as X}from"./IconButtonAugment-CR0fVrwD.js";import{M as Me}from"./message-broker-BygIEqPd.js";import{M as Ze}from"./MonacoMarkdownEditor-CEUFXrwt.js";import{C as je}from"./check-CI07bzZx.js";import{E as Be,D as W,R as et,M as we,A as tt,b as st,c as ot}from"./index-DON3DCoY.js";import{S as at}from"./SuccessfulButton-Btt1yYx9.js";import{O as nt}from"./OpenFileButton-CVOPVJP1.js";import{F as rt}from"./Filespan-9fd1tyrF.js";import{T as Ee,a as ue}from"./CardAugment-DVbbQqkH.js";import{B as Ie}from"./ButtonAugment-CWDjQYWT.js";import{C as Se}from"./chevron-down-CkTAocB8.js";import"./async-messaging-Bp70swAv.js";import"./index-CWns8XM2.js";import"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./BaseTextInput-BaUpeUef.js";import"./chat-model-context-39sqbIF3.js";import"./index-B528snJk.js";import"./index-C_brRns6.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";var it=De("<svg><!></svg>");function Le(K,E){const b=_e(E,["children","$$slots","$$events","$$legacy"]);var I=it();He(I,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...b}));var c=w(I);Ye(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),r(K,I)}var lt=O("<!> <!>",1),ct=O('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),dt=O("<!> <!>",1);function ut(K,E){ye(E,!1);const[b,I]=Ae(),c=()=>Q(s,"$rulesFiles",b),B=()=>Q(R,"$selectedRule",b),x=()=>Q(e(k),"$focusedIndex",b),L=y(),Y=y(),Z=y();let re=me(E,"onRuleSelected",8),ie=me(E,"disabled",8,!1);const ve=new Me(X),pe=new Ne,d=new Be(X,ve,pe),s=oe([]),C=oe(!0),R=oe(void 0);let k=y(void 0),P=y(()=>{});Te(()=>{(async function(){try{C.set(!0);const v=await d.findRules("",100);s.set(v)}catch(v){console.error("Failed to load rules:",v),s.set([])}finally{C.set(!1)}})();const m=v=>{var S;((S=v.data)==null?void 0:S.type)===be.getRulesListResponse&&(s.set(v.data.data||[]),C.set(!1))};return window.addEventListener("message",m),()=>{window.removeEventListener("message",m)}});let _=y(),M=y(!1);function ge(m){h(M,m)}he(()=>c(),()=>{h(L,c().length>0)}),he(()=>($e(ie()),e(L)),()=>{h(Y,ie()||!e(L))}),he(()=>e(L),()=>{h(Z,e(L)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),Ue(),xe();var u=ze(),j=de(u),ee=m=>{var v=ze(),S=de(v),fe=G=>{W.Root(G,{onOpenChange:ge,get requestClose(){return e(P)},set requestClose(z){h(P,z)},get focusedIndex(){return e(k)},set focusedIndex(z){Qe(h(k,z),"$focusedIndex",b)},children:(z,t)=>{var o=dt(),n=de(o);W.Trigger(n,{children:(a,N)=>{const D=A(()=>($e(ue),q(()=>[ue.Hover]))),H=A(()=>!e(M)&&void 0);Fe(Ee(a,{get content(){return e(Z)},get triggerOn(){return e(D)},side:"top",get open(){return e(H)},children:(T,ce)=>{Ie(T,{color:"neutral",variant:"soft",size:1,get disabled(){return e(Y)},children:(l,$)=>{var i=J();ke(()=>Ge(i,(B(),q(()=>B()?B().path:"Rules")))),r(l,i)},$$slots:{default:!0,iconLeft:(l,$)=>{V(l,{slot:"iconLeft",name:"arrow-right",children:(i,g)=>{Le(i,{})},$$slots:{default:!0}})},iconRight:(l,$)=>{V(l,{slot:"iconRight",name:"chevron-down",children:(i,g)=>{Se(i,{})},$$slots:{default:!0}})}}})},$$slots:{default:!0},$$legacy:!0}),T=>h(_,T),()=>e(_))},$$slots:{default:!0}});var p=ne(n,2);W.Content(p,{side:"bottom",align:"start",children:(a,N)=>{var D=ct(),H=w(D);qe(H,1,c,We,(l,$,i)=>{const g=A(()=>x()===i);W.Item(l,{onSelect:()=>function(f){R.set(f),re()(f),e(P)()}(e($)),get highlight(){return e(g)},children:(f,U)=>{rt(f,{get filepath(){return e($),q(()=>e($).path)}})},$$slots:{default:!0}})});var T=ne(H,2),ce=l=>{var $=lt(),i=de($);W.Separator(i,{});var g=ne(i,2);W.Label(g,{children:(f,U)=>{Oe(f,{size:1,color:"neutral",children:(te,Re)=>{var F=J();ke(se=>Ge(F,se),[()=>(c(),x(),q(()=>`Move to ${c()[x()].path}`))],A),r(te,F)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(l,$)};ae(T,l=>{x(),c(),q(()=>x()!==void 0&&c()[x()])&&l(ce)}),r(a,D)},$$slots:{default:!0}}),r(z,o)},$$slots:{default:!0},$$legacy:!0})},le=G=>{const z=A(()=>($e(ue),q(()=>[ue.Hover])));Fe(Ee(G,{get content(){return e(Z)},get triggerOn(){return e(z)},side:"top",children:(t,o)=>{Ie(t,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(n,p)=>{var a=J("Rules");r(n,a)},$$slots:{default:!0,iconLeft:(n,p)=>{V(n,{slot:"iconLeft",name:"arrow-right",children:(a,N)=>{Le(a,{})},$$slots:{default:!0}})},iconRight:(n,p)=>{V(n,{slot:"iconRight",name:"chevron-down",children:(a,N)=>{Se(a,{})},$$slots:{default:!0}})}}})},$$slots:{default:!0},$$legacy:!0}),t=>h(_,t),()=>e(_))};ae(S,G=>{e(L)?G(fe):G(le,!1)}),r(m,v)};ae(j,m=>{Q(C,"$loading",b)||m(ee)}),r(K,u),Ce(),I()}var mt=O('<div class="c-move-text-btn__left_icon svelte-ipzk4b"><!></div>'),vt=O('<div class="l-file-controls svelte-ipzk4b"><div class="l-file-controls-left svelte-ipzk4b"><div class="c-move-text-btn svelte-ipzk4b"><!></div> <div class="c-move-text-btn svelte-ipzk4b"><!></div></div> <div class="l-file-controls-right svelte-ipzk4b"><!></div></div>'),pt=O('<div class="l-memories-editor svelte-ipzk4b"><div class="c-memories-editor__content svelte-ipzk4b"><!></div></div>'),gt=O('<div class="c-memories-container svelte-1vchs21"><!></div>');Ke(function(K,E){ye(E,!1);const[b,I]=Ae(),c=()=>Q(L,"$editorContent",b),B=()=>Q(Y,"$editorPath",b),x=new Me(X),L=oe(null),Y=oe(null),Z={handleMessageFromExtension(d){const s=d.data;if(s&&s.type===be.loadFile){if(s.data.content!==void 0){const C=s.data.content.replace(/^\n+/,"");L.set(C)}s.data.pathName&&Y.set(s.data.pathName)}return!0}};Te(()=>{x.registerConsumer(Z),X.postMessage({type:be.memoriesLoaded})}),Ve(()=>{x.dispose()}),xe();var re=gt();Xe("message",Je,function(...d){var s;(s=x.onMessageFromExtension)==null||s.apply(this,d)});var ie=w(re),ve=d=>{(function(s,C){ye(C,!1);let R=me(C,"text",12),k=me(C,"path",8);const P=new Me(X),_=new Ne,M=new Be(X,P,_),ge=new et(P);let u=y(""),j=y(0),ee=y(0),m=y("neutral");const v=async()=>{k()&&M.saveFile({repoRoot:"",pathName:k(),content:R()})};async function S(t){if(!e(u))return;let o,n,p;const a=e(u).slice(0,20);if(t==="userGuidelines"?(o="Move Content to User Guidelines",n=`Are you sure you want to move the selected content "${a}" to your user guidelines?`,p=we.userGuidelines):t==="augmentGuidelines"?(o="Move Content to Workspace Guidelines",n=`Are you sure you want to move the selected content "${a}" to workspace guidelines?`,p=we.augmentGuidelines):(o="Move Content to Rule",n=`Are you sure you want to move the selected content "${a}" to rule file "${t.rule.path}"?`,p=we.rules),!await M.openConfirmationModal({title:o,message:n,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?M.updateUserGuidelines(e(u)):t==="augmentGuidelines"?M.updateWorkspaceGuidelines(e(u)):(await ge.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(u),description:t.rule.description}),M.showNotification({message:`Moved content "${a}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${st}/${ot}/${t.rule.path}`}}));const N=R().substring(0,e(j))+R().substring(e(ee));return R(N),await v(),M.reportAgentSessionEvent({eventName:tt.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:p}}}),"success"}async function fe(t){await S({rule:t})}xe();var le=pt(),G=w(le),z=w(G);Ze(z,{saveFunction:v,get selectedText(){return e(u)},set selectedText(o){h(u,o)},get selectionStart(){return e(j)},set selectionStart(o){h(j,o)},get selectionEnd(){return e(ee)},set selectionEnd(o){h(ee,o)},get value(){return R()},set value(o){R(o)},header:o=>{var n=vt(),p=w(n),a=w(p),N=w(a);const D=A(()=>!e(u));at(N,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>S("userGuidelines"),get disabled(){return e(D)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(m)},set state(g){h(m,g)},iconLeft:g=>{var f=mt(),U=w(f),te=F=>{V(F,{name:"check",children:(se,Pe)=>{je(se,{})},$$slots:{default:!0}})},Re=F=>{V(F,{name:"arrow-right",children:(se,Pe)=>{Le(se,{})},$$slots:{default:!0}})};ae(U,F=>{e(m)==="success"?F(te):F(Re,!1)}),r(g,f)},children:(g,f)=>{var U=J("User Guidelines");r(g,U)},$$slots:{iconLeft:!0,default:!0},$$legacy:!0});var H=ne(a,2),T=w(H);const ce=A(()=>!e(u));ut(T,{onRuleSelected:fe,get disabled(){return e(ce)}});var l=ne(p,2),$=w(l);nt($,{size:1,get path(){return k()},variant:"soft",onOpenLocalFile:async()=>(M.openFile({repoRoot:"",pathName:k()}),"success"),$$slots:{text:(i,g)=>{Oe(i,{slot:"text",size:1,children:(f,U)=>{var te=J("Augment-Memories.md");r(f,te)},$$slots:{default:!0}})}}}),r(o,n)},$$slots:{header:!0},$$legacy:!0}),r(s,le),Ce()})(d,{get text(){return c()},get path(){return B()}})},pe=d=>{var s=J("Loading memories...");r(d,s)};ae(ie,d=>{c()!==null&&B()!==null?d(ve):d(pe,!1)}),r(K,re),Ce(),I()},{target:document.getElementById("app")});
