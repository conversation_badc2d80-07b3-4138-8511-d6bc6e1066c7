import{az as q,aA as J,aB as K,aC as O,aD as P,aE as T,A as V,aF as _,aG as L,aH as N,q as Q,V as b,h as U,g as W,aI as R,aJ as X,aK as Y}from"./GuardedIcon-BFT2yJIo.js";const g={tick:a=>requestAnimationFrame(a),now:()=>performance.now(),tasks:new Set};function G(){const a=g.now();g.tasks.forEach(t=>{t.c(a)||(g.tasks.delete(t),t.f())}),g.tasks.size!==0&&g.tick(G)}function k(a,t){L(()=>{a.dispatchEvent(new CustomEvent(t))})}function Z(a){if(a==="float")return"cssFloat";if(a==="offset")return"cssOffset";if(a.startsWith("--"))return a;const t=a.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(o=>o[0].toUpperCase()+o.slice(1)).join("")}function B(a){const t={},o=a.split(";");for(const e of o){const[c,n]=e.split(":");if(!c||n===void 0)break;t[Z(c.trim())]=n.trim()}return t}const j=a=>a;function nt(a,t,o,e){var c,n,r,d=!!(a&X),v=!!(a&Y),f=!!(a&_),i=d&&v?"both":d?"in":"out",$=t.inert,y=t.style.overflow;function h(){var u=R,x=q;U(null),W(null);try{return c??(c=o()(t,(e==null?void 0:e())??{},{direction:i}))}finally{U(u),W(x)}}var m={is_global:f,in(){var u;if(t.inert=$,!d)return r==null||r.abort(),void((u=r==null?void 0:r.reset)==null?void 0:u.call(r));v||(n==null||n.abort()),k(t,"introstart"),n=C(t,h(),r,1,()=>{k(t,"introend"),n==null||n.abort(),n=c=void 0,t.style.overflow=y})},out(u){if(!v)return u==null||u(),void(c=void 0);t.inert=!0,k(t,"outrostart"),r=C(t,h(),n,0,()=>{k(t,"outroend"),u==null||u()})},stop:()=>{n==null||n.abort(),r==null||r.abort()}},l=q;if((l.transitions??(l.transitions=[])).push(m),d&&J){var p=f;if(!p){for(var s=l.parent;s&&s.f&K;)for(;(s=s.parent)&&!(s.f&O););p=!s||!!(s.f&P)}p&&T(()=>{V(()=>m.in())})}}function C(a,t,o,e,c){var n=e===1;if(N(t)){var r,d=!1;return Q(()=>{if(!d){var p=t({direction:n?"in":"out"});r=C(a,p,o,e,c)}}),{abort:()=>{d=!0,r==null||r.abort()},deactivate:()=>r.deactivate(),reset:()=>r.reset(),t:()=>r.t()}}if(o==null||o.deactivate(),!(t!=null&&t.duration))return c(),{abort:b,deactivate:b,reset:b,t:()=>e};const{delay:v=0,css:f,tick:i,easing:$=j}=t;var y=[];if(n&&o===void 0&&(i&&i(0,1),f)){var h=B(f(0,1));y.push(h,h)}var m=()=>1-e,l=a.animate(y,{duration:v,fill:"forwards"});return l.onfinish=()=>{l.cancel();var p=(o==null?void 0:o.t())??1-e;o==null||o.abort();var s=e-p,u=t.duration*Math.abs(s),x=[];if(u>0){var E=!1;if(f)for(var z=Math.ceil(u/(1e3/60)),F=0;F<=z;F+=1){var A=p+s*$(F/z),M=B(f(A,1-A));x.push(M),E||(E=M.overflow==="hidden")}E&&(a.style.overflow="hidden"),m=()=>{var w=l.currentTime;return p+s*$(w/u)},i&&function(w){let H;g.tasks.size===0&&g.tick(G),new Promise(I=>{g.tasks.add(H={c:w,f:I})})}(()=>{if(l.playState!=="running")return!1;var w=m();return i(w,1-w),!0})}(l=a.animate(x,{duration:u,fill:"forwards"})).onfinish=()=>{m=()=>e,i==null||i(e,1-e),c()}},{abort:()=>{l&&(l.cancel(),l.effect=null,l.onfinish=b)},deactivate:()=>{c=b},reset:()=>{e===0&&(i==null||i(1,0))},t:()=>m()}}const tt=a=>a;function S(a){const t=a-1;return t*t*t+1}function D(a){const t=typeof a=="string"&&a.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[a,"px"]}function rt(a,{delay:t=0,duration:o=400,easing:e=tt}={}){const c=+getComputedStyle(a).opacity;return{delay:t,duration:o,easing:e,css:n=>"opacity: "+n*c}}function ot(a,{delay:t=0,duration:o=400,easing:e=S,x:c=0,y:n=0,opacity:r=0}={}){const d=getComputedStyle(a),v=+d.opacity,f=d.transform==="none"?"":d.transform,i=v*(1-r),[$,y]=D(c),[h,m]=D(n);return{delay:t,duration:o,easing:e,css:(l,p)=>`
			transform: ${f} translate(${(1-l)*$}${y}, ${(1-l)*h}${m});
			opacity: ${v-i*p}`}}function et(a,{delay:t=0,duration:o=400,easing:e=S,axis:c="y"}={}){const n=getComputedStyle(a),r=+n.opacity,d=c==="y"?"height":"width",v=parseFloat(n[d]),f=c==="y"?["top","bottom"]:["left","right"],i=f.map(s=>`${s[0].toUpperCase()}${s.slice(1)}`),$=parseFloat(n[`padding${i[0]}`]),y=parseFloat(n[`padding${i[1]}`]),h=parseFloat(n[`margin${i[0]}`]),m=parseFloat(n[`margin${i[1]}`]),l=parseFloat(n[`border${i[0]}Width`]),p=parseFloat(n[`border${i[1]}Width`]);return{delay:t,duration:o,easing:e,css:s=>`overflow: hidden;opacity: ${Math.min(20*s,1)*r};${d}: ${s*v}px;padding-${f[0]}: ${s*$}px;padding-${f[1]}: ${s*y}px;margin-${f[0]}: ${s*h}px;margin-${f[1]}: ${s*m}px;border-${f[0]}-width: ${s*l}px;border-${f[1]}-width: ${s*p}px;min-${d}: 0`}}function it(a,{delay:t=0,duration:o=400,easing:e=S,start:c=0,opacity:n=0}={}){const r=getComputedStyle(a),d=+r.opacity,v=r.transform==="none"?"":r.transform,f=1-c,i=d*(1-n);return{delay:t,duration:o,easing:e,css:($,y)=>`
			transform: ${v} scale(${1-f*y});
			opacity: ${d-i*y}
		`}}export{rt as a,it as b,ot as f,et as s,nt as t};
