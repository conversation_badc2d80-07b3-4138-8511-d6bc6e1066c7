var Vi=Object.defineProperty;var Hi=(he,n,ee)=>n in he?Vi(he,n,{enumerable:!0,configurable:!0,writable:!0,value:ee}):he[n]=ee;var oi=(he,n,ee)=>Hi(he,typeof n!="symbol"?n+"":n,ee);import{W as zi,N as lt,x as V,y as v,z as x,P as e,R as ke,u as o,$ as vi,C as ye,E as Wt,as as ii,b as t,X as ot,a7 as re,a1 as Pe,D as xe,A as Q,ad as N,ac as i,ag as tt,ax as ni,ai as Fi,ar as Bt,B as w,a0 as it,F as gi,aq as Bi,ah as Ai,aa as zt,w as fi,a6 as Qt,bc as xi,H as _e,G as qt,a9 as ut,ae as At,al as mi,ak as Ut,m as I,a3 as de,a4 as Di,a5 as _i,a8 as ht,aM as Ui,T as Oe,at as Wi,f as Oi,aO as ci,bd as Yt,ay as Gi,aj as Ji,aP as Yi}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";/* empty css                                */import{a as Ki,I as ki,c as Ni}from"./IconButtonAugment-CR0fVrwD.js";import{M as Qi}from"./message-broker-BygIEqPd.js";import{R as Kt}from"./ra-diff-ops-model-RUDtUaBz.js";import{C as Xi,a as en,T as Mi,b as tn,k as nn}from"./CollapseButtonAugment-Cnr_pz_w.js";import{t as an,s as sn}from"./index-BzB60MCy.js";import{c as yi,p as ln}from"./index-C_brRns6.js";import{M as on,g as Vt,a as rn,i as dn,b as cn,P as pi,O as Pi,D as vn,C as fn,E as pn}from"./expand-CPL6rEzo.js";import{a as hn,b as un,g as gn,M as mn}from"./index-CWns8XM2.js";import{V as wi}from"./VSCodeCodicon-4Sfbv3fq.js";import{d as _n,T as Tt,a as Ht,C as yn}from"./CardAugment-DVbbQqkH.js";import{B as kt}from"./ButtonAugment-CWDjQYWT.js";import{M as $i}from"./MaterialIcon-D9dP7dAZ.js";import{n as wn,g as je,a as ri}from"./file-paths-BcSg4gks.js";import{i as $n,g as ti,a as Cn,b as Li,M as bn}from"./file-type-utils-Zb3vtfL9.js";import{F as An,g as di,p as qi,d as kn}from"./index-B528snJk.js";import{L as Si}from"./LanguageIcon-CJlkgv5n.js";import{A as zn}from"./async-messaging-Bp70swAv.js";import{E as Ei}from"./exclamation-triangle-uRBrTmWU.js";import{F as Fn}from"./Filespan-9fd1tyrF.js";import{M as xn}from"./ModalAugment-xq6pa268.js";import"./toggleHighContrast-C7wSWUJK.js";import"./preload-helper-Dv6uf1Os.js";import"./chat-types-BDRYChZT.js";import"./focusTrapStack-CDv9v5kQ.js";class ai{constructor(n){oi(this,"_opts",null);oi(this,"_subscribers",new Set);this._asyncMsgSender=n}subscribe(n){return this._subscribers.add(n),n(this),()=>{this._subscribers.delete(n)}}notifySubscribers(){this._subscribers.forEach(n=>n(this))}get opts(){return this._opts}updateOpts(n){this._opts=n,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const n=await this._asyncMsgSender.send({type:zi.remoteAgentDiffPanelLoaded});this.updateOpts(n.data)}catch(n){console.error("Failed to load diff panel:",n),this.updateOpts(null)}}handleMessageFromExtension(n){const ee=n.data;return!(!ee||!ee.type)&&ee.type===zi.remoteAgentDiffPanelSetOpts&&(this.updateOpts(ee.data),!0)}}oi(ai,"key","remoteAgentDiffModel");var Mn=v("<span><code><!></code></span>");function Ln(he,n){lt(n,!0);let ee=V(n,"element",15),ce=ke(()=>n.token.raw.slice(1,n.token.raw.length-1)),f=ke(()=>e(ce).startsWith('"')),m=ke(()=>/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(e(ce))),ne=ke(()=>e(m)&&function(p){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(p))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let $,ge,J;return p.length===4?($=parseInt(p.charAt(1),16),ge=parseInt(p.charAt(2),16),J=parseInt(p.charAt(3),16),$*=17,ge*=17,J*=17):($=parseInt(p.slice(1,3),16),ge=parseInt(p.slice(3,5),16),J=parseInt(p.slice(5,7),16)),.299*$+.587*ge+.114*J<130}(e(ce)));var P=Mn(),Z=o(P);let we;var G=o(Z),ve=p=>{var $=re();ye(()=>Pe($,e(ce))),t(p,$)},b=p=>{var $=re();ye(()=>Pe($,e(ce))),t(p,$)};x(G,p=>{e(m)?p(ve):p(b,!1)}),vi(P,p=>ee(p),()=>ee()),ye(p=>{we=Wt(Z,1,"markdown-codespan svelte-11ta4gi",null,we,p),ii(Z,e(m)?`background-color: ${e(ce)}; color: ${e(ne)?"white":"black"}`:"")},[()=>({"markdown-string":e(f)})]),t(he,P),ot()}function Ci(he,n){let ee=V(n,"markdown",8);const ce={codespan:Ln},f=xe(()=>(N(ee()),Q(()=>ee().replace(/`?#[0-9a-fA-F]{3,6}`?/g,m=>m.startsWith("`")?m:`\`${m}\``))));on(he,{get markdown(){return e(f)},get renderers(){return ce}})}const ui=(he,n)=>{let ee=null,ce=null,f=null,m=!1;function ne(){f&&cancelAnimationFrame(f),f=requestAnimationFrame(()=>{const{path:G,onCollapseStateChange:ve}=n;if(m)return void(f=null);const b=Array.from(document.querySelectorAll(`[data-description-id^="${G}:"]`));let p=!1;for(const $ of b)if($!==he&&P(he,$)){p=!0;break}p&&(m=!0),ve&&ve(p),f=null})}function P(G,ve){const b=G.getBoundingClientRect(),p=ve.getBoundingClientRect();return!(b.bottom<=p.top||p.bottom<=b.top)}function Z(){we(),ee=new MutationObserver(()=>{ne()});const G=he.closest(".descriptions")||document.body;ee.observe(G,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(ce=new ResizeObserver(()=>{ne()}),ce.observe(he)),window.addEventListener("resize",ne),window.addEventListener("scroll",ne)}function we(){ee&&(ee.disconnect(),ee=null),ce&&(ce.disconnect(),ce=null),f&&(cancelAnimationFrame(f),f=null),window.removeEventListener("resize",ne),window.removeEventListener("scroll",ne)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{Z(),ne()}):requestAnimationFrame(()=>{Z(),ne()}),{update:G=>{n=G,m=!1,ne()},destroy:we}};var qn=v('<div role="region" aria-label="Code diff description"><div class="c-diff-description__content svelte-wweiw1"><!></div> <div class="c-diff-description__truncated-content svelte-wweiw1"><!> <div class="c-diff-description__expand-hint svelte-wweiw1">hover to expand</div></div></div>'),En=v('<div class="toggle-button svelte-1r29xbx"><!></div> <div class="descriptions svelte-1r29xbx"></div>',1),Dn=v('<div><div class="editor-container svelte-1r29xbx"></div> <!></div>');function On(he,n){lt(n,!0);const[ee,ce]=Qt(),f=()=>zt(ge,"$monaco",ee),m=Bi();let ne=V(n,"originalCode",3,""),P=V(n,"modifiedCode",3,""),Z=V(n,"descriptions",19,()=>[]),we=V(n,"lineOffset",3,0),G=V(n,"extraPrefixLines",19,()=>[]),ve=V(n,"extraSuffixLines",19,()=>[]),b=V(n,"areDescriptionsVisible",15,!0),p=V(n,"isNewFile",3,!1),$=V(n,"isDeletedFile",3,!1);const ge=hn.getContext().monaco;let J,g=tt(void 0),h=tt(void 0),A=tt(void 0),D=[],L=tt(void 0);const k=un();let _,H=fi(0),F=tt(Ai(p()?20*P().split(`
`).length+40:100));const S=f()?f().languages.getLanguages().map(y=>y.id):[];function r(y,c){var C,Y,oe;if(c){const ae=(C=c.split(".").pop())==null?void 0:C.toLowerCase();if(ae){const Le=(oe=(Y=f())==null?void 0:Y.languages.getLanguages().find($e=>{var Ae;return(Ae=$e.extensions)==null?void 0:Ae.includes("."+ae)}))==null?void 0:oe.id;if(Le&&S.includes(Le))return Le}}return"plaintext"}const Me=fi({});let se=null;function ze(){if(!e(g))return;D=D.filter(C=>(C.dispose(),!1));const y=e(g).getOriginalEditor(),c=e(g).getModifiedEditor();D.push(y.onDidScrollChange(()=>{xi(H,y.getScrollTop())}),c.onDidScrollChange(()=>{xi(H,c.getScrollTop())}))}function Se(){if(!e(g)||!e(L))return;const y=e(g).getOriginalEditor(),c=e(g).getModifiedEditor();D.push(c.onDidContentSizeChange(()=>k.requestLayout()),y.onDidContentSizeChange(()=>k.requestLayout()),e(g).onDidUpdateDiff(()=>k.requestLayout()),c.onDidChangeHiddenAreas(()=>k.requestLayout()),y.onDidChangeHiddenAreas(()=>k.requestLayout()),c.onDidLayoutChange(()=>k.requestLayout()),y.onDidLayoutChange(()=>k.requestLayout()),c.onDidFocusEditorWidget(()=>{Te(!0)}),y.onDidFocusEditorWidget(()=>{Te(!0)}),c.onDidBlurEditorWidget(()=>{Te(!1)}),y.onDidBlurEditorWidget(()=>{Te(!1)}),c.onDidChangeModelContent(()=>{var ae;Ce=!0,le=Date.now();const C=((ae=e(A))==null?void 0:ae.getValue())||"";if(C===P())return;const Y=C.replace(G().join(""),"").replace(ve().join(""),"");m("codeChange",{modifiedCode:Y});const oe=setTimeout(()=>{Ce=!1},500);D.push({dispose:()=>clearTimeout(oe)})})),function(){!e(L)||!e(g)||(se&&clearTimeout(se),se=setTimeout(()=>{var C;if(!e(L).__hasClickListener){const Y=oe=>{const ae=oe.target;ae&&(ae.closest('[title="Show Unchanged Region"]')||ae.closest('[title="Hide Unchanged Region"]'))&&ie()};(C=e(L))==null||C.addEventListener("click",Y),e(L).__hasClickListener=!0,D.push({dispose:()=>{var oe;(oe=e(L))==null||oe.removeEventListener("click",Y)}})}e(g)&&D.push(e(g).onDidUpdateDiff(()=>{ie()}))},300))}()}Fi(()=>{var y,c,C;(y=e(g))==null||y.dispose(),(c=e(h))==null||c.dispose(),J==null||J.dispose(),(C=e(A))==null||C.dispose(),D.forEach(Y=>Y.dispose()),se&&clearTimeout(se),_==null||_()});let ue=null;function ie(){ue&&clearTimeout(ue),ue=setTimeout(()=>{k.requestLayout(),ue=null},100),ue&&D.push({dispose:()=>{ue&&(clearTimeout(ue),ue=null)}})}function He(y,c,C,Y=[],oe=[]){var $e;if(!f())return void console.error("Monaco not loaded. Diff view cannot be updated.");J==null||J.dispose(),($e=e(A))==null||$e.dispose(),c=c||"",C=C||"";const ae=Y.join(""),Le=oe.join("");if(c=p()?C.split(`
`).map(()=>" ").join(`
`):ae+c+Le,C=ae+C+Le,J=f().editor.createModel(c,void 0,y!==void 0?f().Uri.parse("file://"+y+`#${crypto.randomUUID()}`):void 0),$()&&(C=C.split(`
`).map(()=>" ").join(`
`)),i(A,f().editor.createModel(C,void 0,y!==void 0?f().Uri.parse("file://"+y+`#${crypto.randomUUID()}`):void 0),!0),e(g)){e(g).setModel({original:J,modified:e(A)});const Ae=e(g).getOriginalEditor();Ae&&Ae.updateOptions({lineNumbers:"off"}),ze(),se&&clearTimeout(se),se=setTimeout(()=>{Se(),se=null},300)}}ni(()=>{if(f()&&e(L))if(p()){i(h,f().editor.create(e(L),{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:n.theme,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:Y=>`${we()-G().length+Y}`}),!0);const y=r(P(),n.path);i(A,f().editor.createModel(P(),y,n.path!==void 0?f().Uri.parse("file://"+n.path+`#${crypto.randomUUID()}`):void 0),!0),e(h).setModel(e(A)),D.push(e(h).onDidChangeModelContent(()=>{var ae;Ce=!0,le=Date.now();const Y=((ae=e(A))==null?void 0:ae.getValue())||"";if(Y===P())return;m("codeChange",{modifiedCode:Y});const oe=setTimeout(()=>{Ce=!1},500);D.push({dispose:()=>clearTimeout(oe)})})),D.push(e(h).onDidFocusEditorWidget(()=>{var Y;(Y=e(h))==null||Y.updateOptions({scrollbar:{handleMouseWheel:!0}})}),e(h).onDidBlurEditorWidget(()=>{var Y;(Y=e(h))==null||Y.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const c=e(h).getContentHeight();i(F,Math.max(c,60),!0);const C=setTimeout(()=>{var Y;(Y=e(h))==null||Y.layout()},0);D.push({dispose:()=>clearTimeout(C)})}else i(g,f().editor.createDiffEditor(e(L),{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:n.theme,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:y=>`${we()-G().length+y}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}}),!0),_&&_(),_=k.registerEditor({editor:e(g),updateHeight:Ye,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),He(n.path,ne(),P(),G(),ve()),ze(),Se(),se&&clearTimeout(se),se=setTimeout(()=>{k.requestLayout(),se=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ce=!1,le=0;function Be(y,c=!0){return e(g)?(c?e(g).getModifiedEditor():e(g).getOriginalEditor()).getTopForLineNumber(y):18*y}function Ye(){if(!e(g))return;const y=e(g).getModel(),c=y==null?void 0:y.original,C=y==null?void 0:y.modified;if(!c||!C)return;const Y=e(g).getOriginalEditor(),oe=e(g).getModifiedEditor(),ae=e(g).getLineChanges()||[];let Le;if(ae.length===0){const $e=Y.getContentHeight(),Ae=oe.getContentHeight();Le=Math.max(100,$e,Ae)}else{let $e=0,Ae=0;for(const u of ae)u.originalEndLineNumber>0&&($e=Math.max($e,u.originalEndLineNumber)),u.modifiedEndLineNumber>0&&(Ae=Math.max(Ae,u.modifiedEndLineNumber));$e=Math.min($e+3,c.getLineCount()),Ae=Math.min(Ae+3,C.getLineCount());const Qe=Y.getTopForLineNumber($e),l=oe.getTopForLineNumber(Ae);Le=Math.max(Qe,l)+60}i(F,Math.min(Le,2e4),!0),e(g).layout(),Ne()}function Te(y){if(!e(g))return;const c=e(g).getOriginalEditor(),C=e(g).getModifiedEditor();c.updateOptions({scrollbar:{handleMouseWheel:y}}),C.updateOptions({scrollbar:{handleMouseWheel:y}})}function me(y){if(!e(g))return e(h)?e(h).getTopForLineNumber(y.range.start+1):0;const c=e(g).getModel(),C=c==null?void 0:c.original,Y=c==null?void 0:c.modified;if(!C||!Y)return 0;const oe=Be(y.range.start+1,!1),ae=Be(y.range.start+1,!0);return oe&&!ae?oe:!oe&&ae?ae:Math.min(oe,ae)}function Ne(){if(!e(g)&&!e(h)||Z().length===0)return;const y={};Z().forEach((c,C)=>{y[C]=me(c)}),function(c,C=50){const Y=Object.keys(c).sort((oe,ae)=>c[Number(oe)]-c[Number(ae)]);for(let oe=0;oe<Y.length-1;oe++){const ae=Number(Y[oe]),Le=c[ae];c[ae+1]-Le<C&&(c[Number(Y[oe+1])]=Le+C)}}(y),Me.set(y)}const Ke=crypto.randomUUID();Bt(()=>{if(y=P(),!(Ce||Date.now()-le<1e3||e(A)&&e(A).getValue()===G().join("")+y+ve().join("")))if(p()&&e(h)){if(e(A))e(A).setValue(P());else{const c=r(P(),n.path);f()&&i(A,f().editor.createModel(P(),c,n.path!==void 0?f().Uri.parse("file://"+n.path+`#${crypto.randomUUID()}`):void 0),!0),e(A)&&e(h).setModel(e(A))}i(F,20*P().split(`
`).length+40),e(h).layout()}else!p()&&e(g)&&(He(n.path,ne(),P(),G(),ve()),k.requestLayout());var y}),Bt(()=>{(e(g)||e(h))&&Z().length>0&&Ne()}),Bt(()=>{if(p()&&P()&&e(h)){const y=e(h).getContentHeight();i(F,Math.max(y,60),!0),e(h).layout()}});var Ue=Dn();let gt;var mt=o(Ue);vi(mt,y=>i(L,y),()=>e(L));var We=w(mt,2),rt=y=>{var c=En(),C=_e(c),Y=o(C);ki(Y,{variant:"ghost",color:"neutral",size:1,onclick:()=>b(!b()),children:(ae,Le)=>{var $e=qt(),Ae=_e($e),Qe=u=>{wi(u,{icon:"x"})},l=u=>{wi(u,{icon:"book"})};x(Ae,u=>{b()?u(Qe):u(l,!1)}),t(ae,$e)},$$slots:{default:!0}});var oe=w(C,2);ut(oe,21,Z,At,(ae,Le,$e)=>{const Ae=ke(()=>zt(Me,"$descriptionPositions",ee)[$e]||me(e(Le)));(function(Qe,l){lt(l,!0);let u=tt(!1),B=tt(!1),O=tt(void 0),X=tt(0);const T=_n(s=>{i(u,s,!0)},100);function pe(s){const d=document.createElement("canvas").getContext("2d");return d?d.measureText(s).width:8*s.length}function M(){if(e(O)){const s=e(O).getBoundingClientRect();i(X,s.width-128)}}let q=null;ni(()=>{e(O)&&typeof ResizeObserver<"u"&&(q=new ResizeObserver(()=>{M()}),q.observe(e(O)),M())}),Fi(()=>{q&&q.disconnect()}),Bt(()=>{e(O)&&M()});let K=ke(()=>(()=>{const s=l.description.text.split(`
`)[0].split(" ");let d="";if(e(X)<=0)for(const U of s){const W=d+(d?" ":"")+U;if(W.length>30)break;d=W}else for(const U of s){const W=d+(d?" ":"")+U;if(pe(W+"...")>e(X))break;d=W}return d+"..."})());var j=qn();let z;var R=o(j);Ci(o(R),{get markdown(){return l.description.text}});var a=w(R,2);Ci(o(a),{get markdown(){return e(K)}}),vi(j,s=>i(O,s),()=>e(O)),Ki(j,(s,d)=>ui==null?void 0:ui(s,d),()=>({path:l.fileId,onCollapseStateChange:s=>i(B,s,!0)})),ye((s,d)=>{z=Wt(j,1,"c-diff-description svelte-wweiw1",null,z,s),ii(j,`top: ${l.position??""}px;`),it(j,"data-description-id",d)},[()=>({"c-diff-description__collapsed":e(B)&&!e(u),"c-diff-description__hovered":e(u)}),()=>function(s,d){return`${s}:${d}`}(l.fileId,l.index)]),gi("mouseenter",j,s=>{T.cancel(),i(u,!0),s.stopPropagation()}),gi("mouseleave",j,s=>{T(!1),s.stopPropagation()}),t(Qe,j),ot()})(ae,{get description(){return e(Le)},get position(){return e(Ae)},get fileId(){return Ke},index:$e})}),ye(()=>ii(oe,`transform: translateY(${-zt(H,"$scrollY",ee)}px)`)),t(y,c)};x(We,y=>{Z().length>0&&y(rt)}),ye(y=>{gt=Wt(Ue,1,"monaco-diff-container svelte-1r29xbx",null,gt,y),ii(mt,`height: ${e(F)??""}px`)},[()=>({"monaco-diff-container-with-descriptions":Z().length>0&&b()})]),t(he,Ue),ot(),ce()}const Ti=Symbol("focusedPath");function ji(){return Ut(Ti)}function bi(he){return`file-diff-${Vt(he)}`}var Nn=v('<!> <img class="image-preview svelte-1536g7w"/>',1),Pn=v("<!> <!>",1),Sn=v("<!> ",1),Tn=v('<!> <img class="image-preview image-preview--previous svelte-1536g7w"/>',1),jn=v('<!> <img class="image-preview svelte-1536g7w"/> <!>',1),Rn=v('<div class="image-container svelte-1536g7w"><!></div>'),Zn=v("<!> No text preview available.",1),In=v('<div class="binary-file-message svelte-1536g7w"><!></div>'),Vn=v('<div class="too-large-message svelte-1536g7w"><!></div>'),Hn=v('<div class="changes svelte-1536g7w"><!></div>'),Bn=v('<span class="c-directory svelte-1536g7w"> </span>'),Un=v('<span class="new-file-badge svelte-1536g7w">New File</span>'),Wn=v('<span class="additions svelte-1536g7w"><!></span>'),Gn=v('<span class="deletions svelte-1536g7w"><!></span>'),Jn=v('<div class="changes-indicator svelte-1536g7w"><!> <!></div>'),Yn=v('<div class="applied svelte-1536g7w"><!></div>'),Kn=v('<div class="applied__icon svelte-1536g7w"><!></div>'),Qn=v("<!> <!>",1),Xn=v('<div slot="header" class="header svelte-1536g7w"><!> <div class="c-path svelte-1536g7w"><!> <!></div> <!> <!> <!></div>'),ea=v("<div><!></div>");function Ri(he,n){lt(n,!1);const[ee,ce]=Qt(),f=()=>zt(Ui,"$themeStore",ee),m=I(),ne=I(),P=I(),Z=I(),we=I(),G=I(),ve=I(),b=I(),p=I(),$=I(),ge=I(),J=I(),g=I(),h=I(),A=I(),D=I(),L=I(),k=I(),_=I(),H=I(),F=I();let S=V(n,"path",8),r=V(n,"change",12),Me=V(n,"descriptions",24,()=>[]),se=V(n,"areDescriptionsVisible",12,!0),ze=V(n,"isExpandedDefault",8),Se=V(n,"isCollapsed",28,()=>!ze()),ue=V(n,"isApplying",8),ie=V(n,"hasApplied",8),He=V(n,"onApplyChanges",24,()=>{}),Ce=V(n,"onCodeChange",24,()=>{}),le=V(n,"onOpenFile",24,()=>{}),Be=V(n,"isAgentFromDifferentRepo",8,!1);const Ye=ji(),Te=Ut(Kt.key);let me=I(r().modifiedCode);function Ne(c){var C;i(me,c.detail.modifiedCode),(C=Ce())==null||C(e(me))}function Ke(){var c,C;Te.reportApplyChangesEvent(),r(r().modifiedCode=e(me),!0),(c=Ce())==null||c(e(me)),(C=He())==null||C()}let Ue=I(e(_));function gt(){i(Ue,`Open ${e(_)??"file"}`)}async function mt(){le()&&(i(Ue,"Opening file..."),await le()()?gt():(i(Ue,"Failed to open file. Does the file exist?"),setTimeout(()=>{gt()},2e3)))}ni(()=>{gt()}),de(()=>N(r()),()=>{i(me,r().modifiedCode)}),de(()=>N(r()),()=>{i(m,rn(r().diff))}),de(()=>e(m),()=>{i(ne,e(m).additions)}),de(()=>e(m),()=>{i(P,e(m).deletions)}),de(()=>N(r()),()=>{i(Z,dn(r()))}),de(()=>N(r()),()=>{i(we,cn(r()))}),de(()=>N(S()),()=>{i(G,$n(S()))}),de(()=>N(S()),()=>{i(ve,ti(S()))}),de(()=>N(S()),()=>{i(b,Cn(S()))}),de(()=>N(r()),()=>{var c;i(p,((c=r().originalCode)==null?void 0:c.length)||0)}),de(()=>e(me),()=>{var c;i($,((c=e(me))==null?void 0:c.length)||0)}),de(()=>e(p),()=>{i(ge,Li(e(p)))}),de(()=>e($),()=>{i(J,Li(e($)))}),de(()=>(e(me),N(r())),()=>{i(g,!e(me)&&!!r().originalCode)}),de(()=>(e(me),N(r())),()=>{i(h,!!e(me)&&!r().originalCode)}),de(()=>e(G),()=>{i(A,e(G))}),de(()=>(e(G),e(b)),()=>{i(D,!e(G)&&e(b))}),de(()=>(e(G),e(b),e(J),e(g),e(ge),e(h)),()=>{i(L,!e(G)&&!e(b)&&(e(J)||e(g)&&e(ge)||e(h)&&e(J)))}),de(()=>f(),()=>{var c,C;i(k,gn((c=f())==null?void 0:c.category,(C=f())==null?void 0:C.intensity))}),de(()=>N(S()),()=>{i(_,wn(S()))}),de(()=>(N(ue()),N(Be())),()=>{i(H,ue()||Be())}),de(()=>(N(ue()),N(ie()),N(Be())),()=>{i(F,ue()?"Applying changes...":ie()?"Reapply changes to local file":Be()?"Cannot apply changes from a different repository locally":"Apply changes to local file")}),Di(),_i();var We=ea();let rt;var y=o(We);Xi(y,{stickyHeader:!0,get collapsed(){return Se()},set collapsed(c){Se(c)},children:(c,C)=>{var Y=Hn(),oe=o(Y),ae=$e=>{var Ae=Rn(),Qe=o(Ae),l=B=>{var O=Pn(),X=_e(O);Oe(X,{class:"image-info-text",children:(M,q)=>{var K=re();ye(j=>Pe(K,`Image deleted: ${j??""}`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(M,K)},$$slots:{default:!0}});var T=w(X,2),pe=M=>{var q=Nn(),K=_e(q);Oe(K,{class:"image-info-text",children:(z,R)=>{var a=re("Previous version:");t(z,a)},$$slots:{default:!0}});var j=w(K,2);ye((z,R,a)=>{it(j,"src",`data:${z??""};base64,${R??""}`),it(j,"alt",`Original ${a??""}`)},[()=>(N(ti),N(S()),Q(()=>ti(S()))),()=>(N(r()),Q(()=>btoa(r().originalCode))),()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(M,q)};x(T,M=>{N(r()),Q(()=>r().originalCode)&&M(pe)}),t(B,O)},u=(B,O)=>{var X=T=>{var pe=jn(),M=_e(pe);Oe(M,{class:"image-info-text",children:(z,R)=>{var a=Sn(),s=_e(a),d=E=>{var te=re("New image added");t(E,te)},U=E=>{var te=re("Image modified");t(E,te)};x(s,E=>{e(Z)||e(h)?E(d):E(U,!1)});var W=w(s);ye(E=>Pe(W,`: ${E??""}`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(z,a)},$$slots:{default:!0}});var q=w(M,2),K=w(q,2),j=z=>{var R=Tn(),a=_e(R);Oe(a,{class:"image-info-text",children:(d,U)=>{var W=re("Previous version:");t(d,W)},$$slots:{default:!0}});var s=w(a,2);ye((d,U,W)=>{it(s,"src",`data:${d??""};base64,${U??""}`),it(s,"alt",`Original ${W??""}`)},[()=>(N(ti),N(S()),Q(()=>ti(S()))),()=>(N(r()),Q(()=>btoa(r().originalCode))),()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(z,R)};x(K,z=>{N(r()),e(me),e(Z),Q(()=>r().originalCode&&e(me)!==r().originalCode&&!e(Z))&&z(j)}),ye((z,R)=>{it(q,"src",`data:${e(ve)??""};base64,${z??""}`),it(q,"alt",`Current ${R??""}`)},[()=>(e(me),Q(()=>btoa(e(me)))),()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(T,pe)};x(B,T=>{e(me)&&T(X)},O)};x(Qe,B=>{e(g)?B(l):B(u,!1)}),t($e,Ae)},Le=($e,Ae)=>{var Qe=u=>{var B=In(),O=o(B);Oe(O,{children:(X,T)=>{var pe=Zn(),M=_e(pe),q=j=>{var z=re();ye(R=>Pe(z,`Binary file added: ${R??""}.`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(j,z)},K=(j,z)=>{var R=s=>{var d=re();ye(U=>Pe(d,`Binary file deleted: ${U??""}.`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(s,d)},a=s=>{var d=re();ye(U=>Pe(d,`Binary file modified: ${U??""}.`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(s,d)};x(j,s=>{e(g)?s(R):s(a,!1)},z)};x(M,j=>{e(Z)||e(h)?j(q):j(K,!1)}),t(X,pe)},$$slots:{default:!0}}),t(u,B)},l=(u,B)=>{var O=T=>{var pe=Vn(),M=o(pe);Oe(M,{size:1,children:(q,K)=>{var j=re();ye(z=>Pe(j,`File "${z??""}" is too large to display a diff (size: ${(e(g)?e(p):e($))??""} bytes, max: ${bn} bytes).`),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(q,j)},$$slots:{default:!0}}),t(T,pe)},X=T=>{On(T,{get path(){return S()},get originalCode(){return N(r()),Q(()=>r().originalCode)},get modifiedCode(){return e(me)},get theme(){return e(k)},get descriptions(){return Me()},get isNewFile(){return e(Z)},get isDeletedFile(){return e(we)},get areDescriptionsVisible(){return se()},set areDescriptionsVisible(pe){se(pe)},$$events:{codeChange:Ne},$$legacy:!0})};x(u,T=>{e(L)?T(O):T(X,!1)},B)};x($e,u=>{e(D)?u(Qe):u(l,!1)},Ae)};x(oe,$e=>{e(A)?$e(ae):$e(Le,!1)}),t(c,Y)},$$slots:{default:!0,header:(c,C)=>{var Y=Xn(),oe=o(Y);en(oe,{});var ae=w(oe,2),Le=o(ae);const $e=xe(()=>(N(Ht),Q(()=>[Ht.Hover])));Tt(Le,{get content(){return e(Ue)},get triggerOn(){return e($e)},delayDurationMs:300,children:(M,q)=>{kt(M,{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$events:{click:mt},children:(K,j)=>{var z=re();ye(R=>Pe(z,R),[()=>(N(je),e(_),Q(()=>je(e(_))))],xe),t(K,z)},$$slots:{default:!0}})},$$slots:{default:!0}});var Ae=w(Le,2),Qe=M=>{var q=Bn(),K=o(q);ye(j=>Pe(K,j),[()=>(N(ri),e(_),Q(()=>ri(e(_))))],xe),t(M,q)};x(Ae,M=>{N(ri),e(_),Q(()=>ri(e(_)))&&M(Qe)});var l=w(ae,2),u=M=>{var q=Un();t(M,q)},B=M=>{var q=Jn(),K=o(q),j=a=>{var s=Wn(),d=o(s);Oe(d,{size:1,children:(U,W)=>{var E=re();ye(()=>Pe(E,`+${e(ne)??""}`)),t(U,E)},$$slots:{default:!0}}),t(a,s)};x(K,a=>{e(ne)>0&&a(j)});var z=w(K,2),R=a=>{var s=Gn(),d=o(s);Oe(d,{size:1,children:(U,W)=>{var E=re();ye(()=>Pe(E,`-${e(P)??""}`)),t(U,E)},$$slots:{default:!0}}),t(a,s)};x(z,a=>{e(P)>0&&a(R)}),t(M,q)};x(l,M=>{e(Z)?M(u):M(B,!1)});var O=w(l,2);const X=xe(()=>(N(Ht),Q(()=>[Ht.Hover])));Tt(O,{get content(){return e(F)},get triggerOn(){return e(X)},delayDurationMs:300,children:(M,q)=>{kt(M,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(H)},$$events:{click:Ke},children:(K,j)=>{var z=Qn(),R=_e(z),a=E=>{var te=re("Applied");t(E,te)},s=E=>{var te=re("Apply");t(E,te)};x(R,E=>{ie()?E(a):E(s,!1)});var d=w(R,2),U=E=>{var te=Yn(),qe=o(te);$i(qe,{iconName:"check"}),t(E,te)},W=E=>{var te=Kn(),qe=o(te);ht(qe,{name:"circle-play",children:(Ze,Ie)=>{pi(Ze,{})},$$slots:{default:!0}}),t(E,te)};x(d,E=>{ie()?E(U):E(W,!1)}),t(K,z)},$$slots:{default:!0}})},$$slots:{default:!0}});var T=w(O,2),pe=M=>{const q=xe(()=>(N(Ht),Q(()=>[Ht.Hover])));Tt(M,{get content(){return e(Ue)},get triggerOn(){return e(q)},delayDurationMs:300,children:(K,j)=>{ki(K,{size:1,variant:"ghost",color:"neutral",$$events:{click:mt},children:(z,R)=>{ht(z,{name:"external-link",children:(a,s)=>{Pi(a,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}})};x(T,M=>{ie()&&M(pe)}),t(c,Y)}},$$legacy:!0}),ye((c,C)=>{rt=Wt(We,1,"c svelte-1536g7w",null,rt,c),it(We,"id",C)},[()=>({focused:zt(Ye,"$focusedPath",ee)===S()}),()=>(N(bi),N(S()),Q(()=>bi(S())))],xe),t(he,We),ot(),ce()}var ta=(he,n)=>he.key==="Enter"&&n(),ia=v('<span class="full-path-text svelte-qnxoj"> </span>'),na=v('<div class="tree-node__children svelte-qnxoj" role="group"></div>'),aa=v('<div class="tree-node svelte-qnxoj"><div role="treeitem" tabindex="0"><div class="tree-node__indent svelte-qnxoj"></div> <div class="tree-node__icon-container svelte-qnxoj"><!></div> <span><!></span></div> <!></div>');function Zi(he,n){lt(n,!0);const[ee,ce]=Qt(),f=()=>zt(P,"$focusedPath",ee);let m=V(n,"node",15),ne=V(n,"indentLevel",3,0);const P=ji();function Z(){m().isFile?P.set(m().path):m(m().isExpanded=!m().isExpanded,!0)}var we=aa(),G=o(we);let ve;G.__click=Z,G.__keydown=[ta,Z];var b=o(G),p=w(b,2),$=o(p),ge=k=>{const _=ke(()=>m().isExpanded?"chevron-down":"chevron-right");wi(k,{get icon(){return e(_)}})},J=k=>{Si(k,{get filename(){return m().name}})};x($,k=>{m().isFile?k(J,!1):k(ge)});var g=w(p,2);let h;var A=o(g);Oe(A,{size:1,children:(k,_)=>{var H=ia(),F=o(H);ye(()=>Pe(F,m().displayName||m().name)),t(k,H)},$$slots:{default:!0}});var D=w(G,2),L=k=>{var _=na();ut(_,21,()=>Array.from(m().children.values()).sort((H,F)=>H.isFile===F.isFile?H.name.localeCompare(F.name):H.isFile?1:-1),At,(H,F)=>{const S=ke(()=>ne()+1);Zi(H,{get node(){return e(F)},get indentLevel(){return e(S)}})}),t(k,_)};x(D,k=>{!m().isFile&&m().isExpanded&&m().children.size>0&&k(L)}),ye((k,_)=>{ve=Wt(G,1,"tree-node__content svelte-qnxoj",null,ve,k),it(G,"aria-selected",m().path===f()),it(G,"aria-expanded",m().isFile?void 0:m().isExpanded),ii(b,`width: ${6*ne()}px`),h=Wt(g,1,"tree-node__label svelte-qnxoj",null,h,_),it(g,"title",m().displayName||m().name)},[()=>({selected:m().path===f(),"collapsed-folder":m().displayName&&!m().isFile}),()=>({"full-path":m().displayName})]),t(he,we),ot(),ce()}Wi(["click","keydown"]);var sa=v('<div class="tree-view__loading svelte-1tnd9l7"><div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div></div>'),la=v('<div class="tree-view__empty svelte-1tnd9l7"><!></div>'),oa=v('<div class="tree-view svelte-1tnd9l7"><div class="tree-view__content svelte-1tnd9l7" role="tree" aria-label="Changed Files"><!></div></div>');function Ii(he,n){lt(n,!0);let ee=V(n,"changedFiles",19,()=>[]),ce=V(n,"isLoading",3,!1);function f(b){const p={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return b.forEach($=>{const ge=$.change_type===An.deleted?$.old_path:$.new_path;ge&&function(J,g){const h=g.split("/");let A=J;for(let D=0;D<h.length;D++){const L=h[D],k=D===h.length-1,_=h.slice(0,D+1).join("/");A.children.has(L)||A.children.set(L,{name:L,path:_,isFile:k,children:new Map,isExpanded:!0}),A=A.children.get(L)}}(p,ge)}),function($){if(!$.isFile){if($.path===""){const ge=Array.from($.children.values()).filter(J=>!J.isFile);for(const J of ge)m(J,!0);return}m($)}}(p),p}function m(b,p=!1){if(b.isFile)return;let $="";p&&($=function(h){let A=h.path.split("/"),D=h;for(;;){const L=Array.from(D.children.values()).filter(_=>!_.isFile),k=Array.from(D.children.values()).filter(_=>_.isFile);if(L.length!==1||k.length!==0)break;D=L[0],A.push(D.name)}return A.join("/")}(b));const ge=Array.from(b.children.values()).filter(h=>!h.isFile);for(const h of ge)m(h);const J=Array.from(b.children.values()).filter(h=>!h.isFile),g=Array.from(b.children.values()).filter(h=>h.isFile);if(J.length===1&&g.length===0){const h=J[0],A=h.name;if(p){b.displayName=$||`${b.name}/${A}`;for(const[D,L]of h.children.entries()){const k=`${D}`;b.children.set(k,L)}b.children.delete(A)}else{b.displayName?h.displayName=`${b.displayName}/${A}`:h.displayName=`${b.name}/${A}`;for(const[D,L]of h.children.entries()){const k=`${A}/${D}`;b.children.set(k,L)}b.children.delete(A)}}}let ne=ke(()=>f(ee()));var P=oa(),Z=o(P),we=o(Z),G=b=>{var p=sa();t(b,p)},ve=(b,p)=>{var $=J=>{var g=la(),h=o(g);Oe(h,{size:1,color:"neutral",children:(A,D)=>{var L=re("No changed files");t(A,L)},$$slots:{default:!0}}),t(J,g)},ge=J=>{var g=qt(),h=_e(g);ut(h,17,()=>Array.from(e(ne).children.values()).sort((A,D)=>A.isFile===D.isFile?A.name.localeCompare(D.name):A.isFile?1:-1),At,(A,D)=>{Zi(A,{get node(){return e(D)},indentLevel:0})}),t(J,g)};x(b,J=>{e(ne).children.size===0?J($):J(ge,!1)},p)};x(we,b=>{ce()?b(G):b(ve,!1)}),t(he,P),ot()}var ra=v('<!> <div class="c-edits-list-controls__icon svelte-6iqvaj"><!></div>',1),da=v("<div><!></div>"),ca=v('<div class="c-edits-list-header svelte-6iqvaj"><div class="c-edits-list-controls svelte-6iqvaj"><!></div></div> <div class="c-edits-list svelte-6iqvaj"><div class="c-edits-section svelte-6iqvaj"></div></div>',1),va=v('<div class="c-edits-list c-edits-list--empty svelte-6iqvaj"><!></div>'),fa=v('<div class="c-edits-list-container svelte-6iqvaj"><div class="c-file-explorer__layout svelte-6iqvaj"><div class="c-file-explorer__tree svelte-6iqvaj"><div class="c-file-explorer__tree__header svelte-6iqvaj"><!> <!></div></div> <div class="c-file-explorer__details svelte-6iqvaj"><!></div></div></div>');function pa(he,n){lt(n,!0);let ee=V(n,"onOpenFile",19,()=>{}),ce=V(n,"pendingFiles",19,()=>[]),f=V(n,"appliedFiles",19,()=>[]),m=V(n,"isLoadingTreeView",3,!1),ne=tt(Ai({})),P=tt(!1),Z=tt(!1);function we(){if(!n.onApplyChanges)return;const H=e(p).map(S=>S.qualifiedPathName.relPath);if(H.every(S=>f().includes(S)))return void i(Z,!0);const F=H.filter(S=>!f().includes(S)&&!ce().includes(S));F.length!==0&&(i(P,!0),i(Z,!1),F.forEach(S=>{const r=e(p).find(Me=>Me.qualifiedPathName.relPath===S);if(r){const Me=e(ne)[S]||r.newContents;n.onApplyChanges(S,r.oldContents,Me)}}))}let G=ke(()=>JSON.stringify(n.changedFiles)),ve=ke(()=>JSON.stringify(f())),b=ke(()=>JSON.stringify(ce()));Bt(()=>{e(G)&&(i(ne,{},!0),i(P,!1),i(Z,!1))});let p=ke(()=>n.changedFiles.map(H=>{const F=H.new_path||H.old_path,S=H.old_contents||"",r=H.new_contents||"",Me=vn.generateDiff(H.old_path,H.new_path,S,r),se=function(ze,Se){const ue=yi("oldFile","newFile",ze,Se,"","",{context:3}),ie=ln(ue);let He=0,Ce=0,le=[];for(const Be of ie)for(const Ye of Be.hunks)for(const Te of Ye.lines){const me=Te.startsWith("+"),Ne=Te.startsWith("-");me&&He++,Ne&&Ce++,le.push({value:Te,added:me,removed:Ne})}return{totalAddedLines:He,totalRemovedLines:Ce,changes:le,diff:ue}}(S,r);return e(ne)[F]||(e(ne)[F]=r),{qualifiedPathName:{rootPath:"",relPath:F},lineChanges:se,oldContents:S,newContents:r,diff:Me}})),$=ke(()=>(()=>{if(e(G)&&e(ve)&&e(b)){const H=e(p).map(F=>F.qualifiedPathName.relPath);return H.length!==0&&H.some(F=>!f().includes(F)&&!ce().includes(F))}return!1})());Bt(()=>{if(e(P)){const H=e(p).map(F=>F.qualifiedPathName.relPath);H.filter(F=>!f().includes(F)&&!ce().includes(F)).length===0&&H.every(F=>f().includes(F)||ce().includes(F))&&ce().length===0&&f().length>0&&(i(P,!1),i(Z,!0))}}),Bt(()=>{if(e(p).length>0&&!e(P)&&e(ve)){const H=e(p).map(F=>F.qualifiedPathName.relPath);if(H.length>0){const F=H.every(S=>f().includes(S));F&&f().length>0?i(Z,!0):!F&&e(Z)&&i(Z,!1)}}});var ge=fa(),J=o(ge),g=o(J),h=o(g),A=o(h);Oe(A,{size:1,class:"c-file-explorer__tree__header__label",children:(H,F)=>{var S=re("Changed files");t(H,S)},$$slots:{default:!0}}),Ii(w(A,2),{get changedFiles(){return n.changedFiles},get isLoading(){return m()}});var D=w(g,2),L=o(D),k=H=>{var F=ca(),S=_e(F),r=o(S),Me=o(r),se=ue=>{const ie=ke(()=>e(P)||e(Z)||ce().length>0||!e($));kt(ue,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(ie)},$$events:{click:we},children:(He,Ce)=>{var le=ra(),Be=_e(le),Ye=Ke=>{var Ue=re("Applying...");t(Ke,Ue)},Te=(Ke,Ue)=>{var gt=We=>{var rt=re("All applied");t(We,rt)},mt=We=>{var rt=re("Apply all");t(We,rt)};x(Ke,We=>{e(Z)?We(gt):We(mt,!1)},Ue)};x(Be,Ke=>{e(P)?Ke(Ye):Ke(Te,!1)});var me=w(Be,2),Ne=o(me);ht(Ne,{name:"circle-play",children:(Ke,Ue)=>{pi(Ke,{})},$$slots:{default:!0}}),t(He,le)},$$slots:{default:!0}})};x(Me,ue=>{e(p).length>0&&ue(se)});var ze=w(S,2),Se=o(ze);ut(Se,21,()=>e(p),ue=>ue.qualifiedPathName.relPath,(ue,ie)=>{var He=da(),Ce=o(He);const le=ke(()=>ce().includes(e(ie).qualifiedPathName.relPath)),Be=ke(()=>f().includes(e(ie).qualifiedPathName.relPath)),Ye=ke(()=>ee()?()=>ee()(e(ie).qualifiedPathName.relPath):void 0);Ri(Ce,{get path(){return e(ie).qualifiedPathName.relPath},get change(){return e(ie).diff},get isApplying(){return e(le)},get hasApplied(){return e(Be)},onCodeChange:Te=>{(function(me,Ne){e(ne)[me]=Ne})(e(ie).qualifiedPathName.relPath,Te)},onApplyChanges:()=>{const Te=e(ne)[e(ie).qualifiedPathName.relPath]||e(ie).newContents;n.onApplyChanges(e(ie).qualifiedPathName.relPath,e(ie).oldContents,Te)},get onOpenFile(){return e(Ye)},isExpandedDefault:!0}),an(3,He,()=>sn),t(ue,He)}),t(H,F)},_=H=>{var F=va(),S=o(F);Oe(S,{size:1,color:"neutral",children:(r,Me)=>{var se=re("No changes to show");t(r,se)},$$slots:{default:!0}}),t(H,F)};x(L,H=>{e(p).length>0?H(k):H(_,!1)}),t(he,ge),ot()}var ha=Oi('<path fill-rule="evenodd" clip-rule="evenodd"></path>'),ua=Oi('<svg width="14" viewBox="0 0 20 20" fill="currentColor" class="svelte-10h4f31"><!></svg>'),ga=v('<div class="c-skeleton-diff__controls svelte-1eiztmz"><div class="c-skeleton-diff__button svelte-1eiztmz"></div></div>'),ma=v('<div class="c-skeleton-diff__changes-item svelte-1eiztmz"><div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div></div>'),_a=v('<div class="c-skeleton-diff__subsection svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__changes svelte-1eiztmz"></div></div>'),ya=v('<div class="c-skeleton-diff__section svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div></div> <!></div> <!></div>'),wa=v('<div class="c-skeleton-diff svelte-1eiztmz"></div>'),$a=v("<!> <!>",1),Ca=v('<div class="c-conflicts-card__file svelte-1bce35u"><!> <!></div>'),ba=v('<div class="c-conflicts-card__header svelte-1bce35u"><div class="c-conflicts-card__icon svelte-1bce35u"><!></div> <span class="c-conflicts-card__title svelte-1bce35u"> </span></div> <div class="c-conflicts-card__description svelte-1bce35u"><!></div> <!>',1),Aa=v('<div class="c-conflicts-card"><!></div>'),ka=v('<div class="c-unstaged-changes-modal__stash-button-loading svelte-9eyy34"><!></div>'),za=v("<!> <span>Stash & Apply Locally</span>",1),Fa=v('<div class="c-unstaged-changes-modal__footer svelte-9eyy34"><!> <!></div>'),xa=v(`There are unstaged changes in your working directory. Please commit your changes or we will
      run <!> to stash your changes before applying changes from the remote agent.`,1),Ma=v('<div class="c-unstaged-changes-modal__body svelte-9eyy34"><!> <!></div>'),La=v('<div class="c-diff-view__error svelte-ibi4q5"><!> <!> <!></div>'),qa=v('<div class="c-diff-view__empty svelte-ibi4q5"><!></div>'),Ea=v("<!> <!>",1),Da=v("<!> <!>",1),Oa=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),Na=v("Applied <!>",1),Pa=v('<div class="c-diff-view__applied svelte-ibi4q5"><!></div>'),Sa=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Ta=v('<div class="c-diff-view__controls svelte-ibi4q5"><!></div> <!>',1),ja=v('<div class="c-diff-view__skeleton-title svelte-ibi4q5"></div>'),Ra=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div> <div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>',1),Za=v("<!> Collapse All",1),Ia=v("<!> Expand All",1),Va=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),Ha=v('<div class="c-diff-view__applied svelte-ibi4q5"><!> <!></div>'),Ba=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Ua=v('<div class="c-diff-view__controls svelte-ibi4q5"><!> <!></div>'),Wa=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>'),Ga=v('<div class="c-diff-view__warning svelte-ibi4q5"><!> </div>'),Ja=v('<div class="c-diff-view__changes-item svelte-ibi4q5"><!></div>'),Ya=v('<div class="c-diff-view__subsection svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><div class="c-diff-view__icon svelte-ibi4q5"><!></div> <h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <!></div></div> <div class="c-diff-view__changes svelte-ibi4q5"></div></div>'),Ka=v('<div class="c-diff-view__section svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <div class="c-diff-view__description svelte-ibi4q5"><!></div></div> <!></div> <!> <!></div>'),Qa=v('<div class="c-diff-view__layout svelte-ibi4q5"><div class="c-diff-view__tree svelte-ibi4q5"><div class="c-diff-view__tree__header svelte-ibi4q5"><!> <!> <!> <!></div></div> <div class="c-diff-view__explanation svelte-ibi4q5"><!></div></div>'),Xa=v('<div class="c-diff-view svelte-ibi4q5"><!> <!></div> <!>',1);function es(he,n){lt(n,!1);const[ee,ce]=Qt(),f=()=>zt(me,"$diffViewFilesMap",ee),m=()=>zt(H,"$diffModel",ee),ne=I(),P=I(),Z=I(),we=I(),G=I(),ve=I(),b=I(),p=I();let $=V(n,"changedFiles",8),ge=V(n,"agentLabel",24,()=>{}),J=V(n,"latestUserPrompt",24,()=>{}),g=V(n,"onApplyChanges",24,()=>{}),h=V(n,"onOpenFile",24,()=>{}),A=V(n,"onRenderBackup",24,()=>{}),D=V(n,"preloadedExplanation",24,()=>{}),L=V(n,"isAgentFromDifferentRepo",8,!1),k=V(n,"conflictFiles",24,()=>new Set);const _=Ut(Kt.key),H=Ut(ai.key);let F="",S=I(!1),r=I([]),Me=I([]),se=I(!1),ze=I(!1),Se=I(null),ue=I(!0),ie=I({}),He=I([]),Ce=I(!1),le=I(!1),Be=I(!0),Ye=I(new Set),Te=I(!1);const me=fi({});let Ne=I({});function Ke(){const l=di(e(r)),u=Object.values(e(ie)).some(Boolean);i(ue,u),Array.from(l).forEach(B=>{Yt(ie,e(ie)[B]=!e(ue))})}async function Ue(l,u,B){if(g())return me.update(O=>(O[l]="pending",O)),new Promise(O=>{var X;(X=g())==null||X(l,u,B).then(()=>{me.update(T=>(T[l]="applied",T)),O()})})}async function gt(){const l=await _.canApplyChanges();l.canApply?mt():l.hasUnstagedChanges&&i(Te,!0)}function mt(){if(!g())return;_.reportApplyChangesEvent(),i(Ce,!0),i(le,!1);const{filesToApply:l,areAllPathsApplied:u}=qi(e(r),$(),e(Ne));u||l.length===0?i(le,u):kn(l,Ue).then(()=>{i(Ce,!1),i(le,!0)})}function We(l){const u={title:"Changed Files",description:`${l.length} files were changed`,sections:[]},B=[],O=[],X=[];return l.forEach(T=>{T.old_path?T.new_path?O.push(T):X.push(T):B.push(T)}),B.length>0&&u.sections.push(rt("Added files","feature",B)),O.length>0&&u.sections.push(rt("Modified files","fix",O)),X.length>0&&u.sections.push(rt("Deleted files","chore",X)),[u]}function rt(l,u,B){const O=[];return B.forEach(X=>{const T=X.new_path||X.old_path,pe=X.old_contents||"",M=X.new_contents||"",q=X.old_path?X.old_path:"",K=yi(q,X.new_path||"/dev/null",pe,M,"","",{context:3}),j=`${Vt(T)}-${Vt(pe+M)}`;O.push({id:j,path:T,diff:K,originalCode:pe,modifiedCode:M})}),{title:l,descriptions:[],type:u,changes:O}}async function y(){var B,O,X,T,pe,M;if(!e(S))return;if(i(se,!0),i(ze,!1),i(Se,null),i(Me,[]),i(r,[]),e(G))return void i(se,!1);const l=102400;let u=0;if($().forEach(q=>{var K,j;u+=(((K=q.old_contents)==null?void 0:K.length)||0)+(((j=q.new_contents)==null?void 0:j.length)||0)}),$().length>12||u>512e3){try{i(r,We($()))}catch(q){console.error("Failed to create simple explanation:",q),i(Se,"Failed to create explanation for large changes.")}i(se,!1)}else try{const q=new zn(z=>Ni.postMessage(z)),K=new Map,j=$().map(z=>{const R=z.new_path||z.old_path,a=z.old_contents||"",s=z.new_contents||"",d=`${Vt(R)}-${Vt(a+s)}`;return K.set(d,{old_path:z.old_path,new_path:z.new_path,old_contents:a,new_contents:s,change_type:z.change_type}),{id:d,old_path:z.old_path,new_path:z.new_path,change_type:z.change_type}});try{const z=j.length===1;let R=[];z?R=j.map(a=>({path:a.new_path||a.old_path,changes:[{id:a.id,path:a.new_path||a.old_path,diff:`File: ${a.new_path||a.old_path}
Change type: ${a.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):R=(await q.send({type:"get-diff-group-changes-request",data:{changedFiles:j,changesById:!0,apikey:F}},3e4)).data.groupedChanges,i(Me,R.map(a=>({path:a.path,changes:a.changes.map(s=>{if(s.id&&K.has(s.id)){const d=K.get(s.id);let U=s.diff;return U&&!U.startsWith("File:")||(U=yi(d.old_path||"",d.new_path||"",d.old_contents||"",d.new_contents||"")),{...s,diff:U,old_path:d.old_path,new_path:d.new_path,old_contents:d.old_contents,new_contents:d.new_contents,change_type:d.change_type,originalCode:d.old_contents||"",modifiedCode:d.new_contents||""}}return s})})))}catch(z){console.error("Failed to group changes with LLM, falling back to simple grouping:",z);try{const R=j.map(a=>{if(a.id&&K.has(a.id)){const s=K.get(a.id);return{...a,old_path:s.old_path,new_path:s.new_path,old_contents:s.old_contents||"",new_contents:s.new_contents||"",change_type:s.change_type}}return a});i(r,We(R)),i(Me,e(r)[0].sections.map(a=>({path:a.title,changes:a.changes}))),i(ze,!1)}catch(R){console.error("Failed to create simple explanation:",R),i(Se,"Failed to group changes. Please try again.")}}if(i(se,!1),!e(Me)||e(Me).length===0)throw new Error("Failed to group changes");if(!e(r)||e(r).length===0){i(r,function(R){const a={title:"Loading...",description:"",sections:[]};return R.forEach(s=>{const d=s.changes.map(W=>{if(W.id)return W;const E=Vt(W.path),te=Vt(W.originalCode+W.modifiedCode);return{...W,id:`${E}-${te}`}}),U={title:s.path,descriptions:[],type:"other",changes:d};a.sections.push(U)}),[a]}(e(Me)));const z=e(r)[0].sections.map(R=>({path:R.title,changes:R.changes.map(a=>{var W,E,te;const s=((W=a.originalCode)==null?void 0:W.length)||0,d=((E=a.modifiedCode)==null?void 0:E.length)||0,U=((te=a.diff)==null?void 0:te.length)||0;return s>l||d>l||U>l?{id:a.id,path:a.path,diff:`File: ${a.path}
Content too large to include in explanation request (${Math.max(s,d,U)} bytes)`,originalCode:s>l?`[File content too large: ${s} bytes]`:a.originalCode,modifiedCode:d>l?`[File content too large: ${d} bytes]`:a.modifiedCode}:{id:a.id,path:a.path,diff:a.diff,originalCode:a.originalCode,modifiedCode:a.modifiedCode}})}));i(ze,!0);try{const R=(O=(B=m())==null?void 0:B.opts)==null?void 0:O.remoteAgentId;if(!R)throw new Error("Remote agent ID not available");const a=((T=(X=m())==null?void 0:X.opts)==null?void 0:T.startSequenceId)??0,s=((M=(pe=m())==null?void 0:pe.opts)==null?void 0:M.endSequenceId)??1e5,{explanation:d,error:U}=await _.getDescriptionsFromRemoteAgentSummary(R,z,a,s);if(U==="Token limit exceeded")return i(r,We($())),i(se,!1),void i(ze,!1);if(d&&d.length>0){const W=d.map((E,te)=>{var Ze;const qe=e(r)[te];return{...E,sections:((Ze=E.sections)==null?void 0:Ze.map((Ie,_t)=>{var $t;const dt=($t=qe==null?void 0:qe.sections)==null?void 0:$t[_t];return{...Ie,changes:(dt==null?void 0:dt.changes)||(Ie==null?void 0:Ie.changes)||[]}}))||[]}});i(r,W)}}catch(R){console.error("Failed to get descriptions, using skeleton explanation:",R)}}e(r).length===0&&i(Se,"Failed to generate explanation.")}catch(q){console.error("Failed to get explanation:",q),i(Se,q instanceof Error?q.message:"An error occurred while generating the explanation.")}finally{i(se,!1),i(ze,!1)}}ni(()=>{const l=localStorage.getItem("anthropic_apikey");l&&(F=l),i(S,!0)});let c=I(""),C=I("Apply all changes locally");de(()=>(N($()),f()),()=>{$()&&me.set($().reduce((l,u)=>{const B=u.new_path||u.old_path;return l[B]=f()[B]??"none",l},{}))}),de(()=>e(ie),()=>{i(ne,Object.values(e(ie)).some(Boolean))}),de(()=>N($()),()=>{i(ve,JSON.stringify($()))}),de(()=>(e(S),e(ve),e(c),N(D())),()=>{e(S)&&e(ve)&&e(ve)!==e(c)&&(i(c,e(ve)),D()&&D().length>0?(i(r,D()),i(se,!1),i(ze,!1)):y(),i(Ce,!1),i(le,!1),i(Ne,{}))}),de(()=>(e(r),e(Ne)),()=>{e(r)&&e(r).length>0&&e(r).flatMap(l=>l.sections||[]).flatMap(l=>l.changes).forEach(l=>{e(Ne)[l.path]||Yt(Ne,e(Ne)[l.path]=l.modifiedCode)})}),de(()=>e(r),()=>{i(P,JSON.stringify(e(r)))}),de(()=>(e(r),e(ie),e(ue)),()=>{if(e(r)&&e(r).length>0){const l=di(e(r));Array.from(l).forEach(O=>{e(ie)[O]===void 0&&Yt(ie,e(ie)[O]=!e(ue))});const u=Object.keys(e(ie)).filter(O=>e(ie)[O]),B=Array.from(l);B.length>0&&i(ue,!B.some(O=>u.includes(O)))}}),de(()=>(e(P),f(),e(r)),()=>{i(Z,(()=>{if(e(P)&&f()){const l=di(e(r));return l.size!==0&&Array.from(l).some(u=>f()[u]!=="applied")}return!1})())}),de(()=>f(),()=>{i(le,Object.keys(f()).every(l=>f()[l]==="applied"))}),de(()=>f(),()=>{i(we,Object.keys(f()).filter(l=>f()[l]==="pending"))}),de(()=>(e(r),N($()),e(Ne)),()=>{(async function(l,u,B){const{filesToApply:O}=qi(l,u,B),X=new Set;for(const T of O)(await _.previewApplyChanges(T.path,T.originalCode,T.newCode)).hasConflicts&&X.add(T.path);i(Ye,X)})(e(r),$(),e(Ne))}),de(()=>N($()),()=>{i(G,$().length===0)}),de(()=>(e(P),e(le),e(r),f()),()=>{if(e(P)&&e(le)){const l=di(e(r));Array.from(l).every(u=>f()[u]==="applied")||i(le,!1)}}),de(()=>(e(le),N(k())),()=>{i(b,e(le)&&k().size>0)}),de(()=>(N(L()),e(Ce),e(le),e(we),e(Z)),()=>{i(p,L()||e(Ce)||e(le)||e(we).length>0||!e(Z))}),de(()=>(e(p),N(L()),e(Ce),e(b),e(le),e(we),e(Z)),()=>{e(p)?L()?i(C,"Cannot apply changes from a different repository locally"):e(Ce)?i(C,"Applying changes..."):e(b)?i(C,"All changes applied, but conflicts need to be resolved manually"):e(le)?i(C,"All changes applied"):e(we).length>0?i(C,"Waiting for changes to apply"):e(Z)||i(C,"No changes to apply"):i(C,"Apply all changes locally")}),Di(),_i();var Y=Xa(),oe=_e(Y),ae=o(oe),Le=l=>{var u=La(),B=o(u);ht(B,{name:"circle-alert",children:(M,q)=>{Ei(M,{})},$$slots:{default:!0}});var O=w(B),X=w(O);kt(X,{variant:"ghost",size:1,$$events:{click:y},children:(M,q)=>{var K=re("Retry");t(M,K)},$$slots:{default:!0}});var T=w(X,2),pe=M=>{kt(M,{variant:"ghost",size:1,$$events:{click(...q){var K;(K=A())==null||K.apply(this,q)}},children:(q,K)=>{var j=re("Render as list");t(q,j)},$$slots:{default:!0}})};x(T,M=>{A()&&M(pe)}),ye(()=>Pe(O,` ${e(Se)??""} `)),t(l,u)};x(ae,l=>{e(Se)&&l(Le)});var $e=w(ae,2),Ae=l=>{var u=qa(),B=o(u);Oe(B,{size:2,color:"secondary",children:(O,X)=>{var T=re("No files changed");t(O,T)},$$slots:{default:!0}}),t(l,u)},Qe=l=>{var u=Qa(),B=o(u),O=o(B),X=o(O),T=a=>{var s=Ea(),d=_e(s);Oe(d,{size:1,class:"c-diff-view__tree__header__label",children:(W,E)=>{var te=re("Changes from agent");t(W,te)},$$slots:{default:!0}});var U=w(d,2);Oe(U,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(W,E)=>{var te=re();ye(()=>Pe(te,ge())),t(W,te)},$$slots:{default:!0}}),t(a,s)};x(X,a=>{ge()&&J()!==ge()&&a(T)});var pe=w(X,2),M=a=>{var s=Da(),d=_e(s);Oe(d,{size:1,class:"c-diff-view__tree__header__label",children:(W,E)=>{var te=re("Last user prompt");t(W,te)},$$slots:{default:!0}});var U=w(d,2);Oe(U,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(W,E)=>{var te=re();ye(()=>Pe(te,J())),t(W,te)},$$slots:{default:!0}}),t(a,s)};x(pe,a=>{J()&&a(M)});var q=w(pe,2);Oe(q,{size:1,class:"c-diff-view__tree__header__label",children:(a,s)=>{var d=re("Changed files");t(a,d)},$$slots:{default:!0}}),Ii(w(q,2),{get changedFiles(){return $()}});var K=w(B,2),j=o(K),z=a=>{var s=Ta(),d=_e(s),U=o(d);const W=xe(()=>e(Ce)?"Applying changes...":e(le)?"All changes applied":e(Z)?"Apply all changes":"No changes to apply");Tt(U,{get content(){return e(W)},children:(E,te)=>{const qe=xe(()=>(e(Ce),e(le),e(we),e(Z),Q(()=>e(Ce)||e(le)||e(we).length>0||!e(Z))));kt(E,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(qe)},$$events:{click:gt},children:(Ze,Ie)=>{var _t=qt(),dt=_e(_t),$t=Ct=>{var Et=Oa(),Dt=o(Et);ci(Dt,{size:1,useCurrentColor:!0});var yt=w(Dt,2);Oe(yt,{size:2,children:(nt,Ft)=>{var Ot=re("Applying...");t(nt,Ot)},$$slots:{default:!0}}),t(Ct,Et)},Gt=(Ct,Et)=>{var Dt=nt=>{var Ft=Pa(),Ot=o(Ft);Oe(Ot,{size:2,children:(Nt,jt)=>{var Rt=Na(),si=w(_e(Rt));$i(si,{iconName:"check"}),t(Nt,Rt)},$$slots:{default:!0}}),t(nt,Ft)},yt=nt=>{var Ft=Sa(),Ot=w(_e(Ft)),Nt=o(Ot);ht(Nt,{name:"circle-play",children:(jt,Rt)=>{pi(jt,{})},$$slots:{default:!0}}),t(nt,Ft)};x(Ct,nt=>{e(le)?nt(Dt):nt(yt,!1)},Et)};x(dt,Ct=>{e(Ce)?Ct($t):Ct(Gt,!1)}),t(Ze,_t)},$$slots:{default:!0}})},$$slots:{default:!0}}),function(E,te){let qe=V(te,"count",3,2);var Ze=wa();ut(Ze,21,()=>Array(qe()),At,(Ie,_t,dt)=>{var $t=ya(),Gt=o($t),Ct=w(o(Gt),2),Et=yt=>{var nt=ga();t(yt,nt)};x(Ct,yt=>{dt===0&&yt(Et)});var Dt=w(Gt,2);ut(Dt,16,()=>Array(2),At,(yt,nt,Ft,Ot)=>{var Nt=_a(),jt=w(o(Nt),2);ut(jt,20,()=>Array(2),At,(Rt,si,Ee,be)=>{var xt=ma();t(Rt,xt)}),t(yt,Nt)}),t(Ie,$t)}),t(E,Ze)}(w(d,2),{count:2}),t(a,s)},R=(a,s)=>{var d=U=>{var W=qt(),E=_e(W);ut(E,1,()=>e(r),At,(te,qe,Ze)=>{var Ie=Ka();it(Ie,"id",`section-${Ze}`);var _t=o(Ie),dt=o(_t),$t=o(dt),Gt=o($t),Ct=Ee=>{var be=ja();t(Ee,be)},Et=Ee=>{var be=re();ye(()=>Pe(be,(e(qe),Q(()=>e(qe).title)))),t(Ee,be)};x(Gt,Ee=>{e(ze),e(qe),Q(()=>e(ze)&&e(qe).title==="Loading...")?Ee(Ct):Ee(Et,!1)});var Dt=w($t,2),yt=o(Dt),nt=Ee=>{var be=Ra();t(Ee,be)},Ft=Ee=>{Ci(Ee,{get markdown(){return e(qe),Q(()=>e(qe).description)}})};x(yt,Ee=>{e(ze),e(qe),Q(()=>e(ze)&&e(qe).description==="")?Ee(nt):Ee(Ft,!1)});var Ot=w(dt,2),Nt=Ee=>{var be=Ua(),xt=o(be);kt(xt,{variant:"ghost-block",color:"neutral",size:2,$$events:{click:Ke},children:(Pt,Jt)=>{var Mt=qt(),Zt=_e(Mt),It=at=>{var ct=Za(),Xe=_e(ct);ht(Xe,{name:"chevrons-down-up",children:(vt,Fe)=>{tn(vt,{})},$$slots:{default:!0}}),t(at,ct)},St=at=>{var ct=Ia(),Xe=_e(ct);ht(Xe,{name:"chevrons-up-down",children:(vt,Fe)=>{pn(vt)},$$slots:{default:!0}}),t(at,ct)};x(Zt,at=>{e(ne)?at(St,!1):at(It)}),t(Pt,Mt)},$$slots:{default:!0}});var wt=w(xt,2);Tt(wt,{get content(){return e(C)},children:(Pt,Jt)=>{kt(Pt,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(p)},$$events:{click:gt},children:(Mt,Zt)=>{var It=qt(),St=_e(It),at=Xe=>{var vt=Va(),Fe=o(vt);ci(Fe,{size:1,useCurrentColor:!0});var fe=w(Fe,2);Oe(fe,{size:2,children:(Re,Ge)=>{var et=re("Applying...");t(Re,et)},$$slots:{default:!0}}),t(Xe,vt)},ct=(Xe,vt)=>{var Fe=Re=>{var Ge=Ha(),et=o(Ge);Oe(et,{size:2,children:(De,st)=>{var ft=re("Applied");t(De,ft)},$$slots:{default:!0}});var bt=w(et,2),Ve=De=>{ht(De,{slot:"rightIcon",name:"circle-alert",children:(st,ft)=>{Mi(st,{})},$$slots:{default:!0}})},Je=De=>{$i(De,{iconName:"check"})};x(bt,De=>{e(b)?De(Ve):De(Je,!1)}),t(Re,Ge)},fe=Re=>{var Ge=Ba(),et=w(_e(Ge)),bt=o(et);ht(bt,{name:"circle-play",children:(Ve,Je)=>{pi(Ve,{})},$$slots:{default:!0}}),t(Re,Ge)};x(Xe,Re=>{e(le)?Re(Fe):Re(fe,!1)},vt)};x(St,Xe=>{e(Ce)?Xe(at):Xe(ct,!1)}),t(Mt,It)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(Ee,be)};x(Ot,Ee=>{Ze===0&&Ee(Nt)});var jt=w(_t,2),Rt=Ee=>{const be=xe(()=>e(le)?k():e(Ye));(function(xt,wt){lt(wt,!0);let Pt=V(wt,"onOpenFile",19,()=>{});var Jt=Aa(),Mt=o(Jt);yn(Mt,{includeBackground:!1,children:(Zt,It)=>{var St=ba(),at=_e(St),ct=o(at),Xe=o(ct);ht(Xe,{name:"circle-alert",children:(Ve,Je)=>{Mi(Ve,{})},$$slots:{default:!0}});var vt=w(ct,2),Fe=o(vt),fe=w(at,2),Re=o(fe),Ge=Ve=>{var Je=re("The following files have merge conflicts that need to be resolved manually.");t(Ve,Je)},et=Ve=>{var Je=re(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`);t(Ve,Je)};x(Re,Ve=>{wt.hasAppliedAll?Ve(Ge):Ve(et,!1)});var bt=w(fe,2);ut(bt,17,()=>wt.files,At,(Ve,Je)=>{var De=Ca(),st=o(De);Tt(st,{get content(){return e(Je)},nested:!0,children:(pt,li)=>{var Lt=$a(),Xt=_e(Lt);Si(Xt,{get filename(){return e(Je)}});var ei=w(Xt,2);Fn(ei,{get filepath(){return e(Je)}}),t(pt,Lt)},$$slots:{default:!0}});var ft=w(st,2);Tt(ft,{content:"Open file",children:(pt,li)=>{ki(pt,{size:1,variant:"ghost-block",color:"neutral",onclick:()=>{var Lt;return(Lt=Pt())==null?void 0:Lt(e(Je))},children:(Lt,Xt)=>{ht(Lt,{name:"external-link",children:(ei,hi)=>{Pi(ei,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}}),t(Ve,De)}),ye(()=>Pe(Fe,`Conflicts (${wt.files.size??""})`)),t(Zt,St)},$$slots:{default:!0}}),t(xt,Jt),ot()})(Ee,{get files(){return e(be)},get hasAppliedAll(){return e(le)},get onOpenFile(){return h()}})};x(jt,Ee=>{e(le),N(k()),e(Ye),Q(()=>(e(le)&&k().size>0||!e(le)&&e(Ye).size>0)&&Ze===0)&&Ee(Rt)});var si=w(jt,2);ut(si,1,()=>(e(qe),Q(()=>e(qe).sections||[])),At,(Ee,be,xt)=>{var wt=Ya();it(wt,"id",`subsection-${Ze}-${xt}`);var Pt=o(wt),Jt=o(Pt),Mt=o(Jt);(function(Fe,fe){lt(fe,!0);const Re={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};let Ge=ke(()=>Re[fe.type]??Re.other);const et=ke(()=>`This is a ${fe.type} change`),bt=ke(()=>[Ht.Hover]);Tt(Fe,{get content(){return e(et)},get triggerOn(){return e(bt)},children:(Ve,Je)=>{var De=ua(),st=o(De),ft=pt=>{var li=qt(),Lt=_e(li);ut(Lt,17,()=>e(Ge).paths,At,(Xt,ei)=>{var hi=ha();ye(()=>it(hi,"d",e(ei))),t(Xt,hi)}),t(pt,li)};x(st,pt=>{e(Ge)&&pt(ft)}),t(Ve,De)},$$slots:{default:!0}}),ot()})(o(Mt),{get type(){return e(be),Q(()=>e(be).type)}});var Zt=w(Mt,2),It=o(Zt),St=Fe=>{var fe=Wa();t(Fe,fe)},at=Fe=>{var fe=re();ye(()=>Pe(fe,(e(be),Q(()=>e(be).title)))),t(Fe,fe)};x(It,Fe=>{e(ze),e(be),Q(()=>e(ze)&&e(be).descriptions.length===0)?Fe(St):Fe(at,!1)});var ct=w(Zt,2),Xe=Fe=>{var fe=Ga(),Re=o(fe);ht(Re,{name:"circle-alert",children:(et,bt)=>{Ei(et,{})},$$slots:{default:!0}});var Ge=w(Re);ye(()=>Pe(Ge,` ${e(be),Q(()=>e(be).warning)??""}`)),t(Fe,fe)};x(ct,Fe=>{e(ze),e(be),Q(()=>!e(ze)&&e(be).warning)&&Fe(Xe)});var vt=w(Pt,2);ut(vt,5,()=>(e(be),Q(()=>e(be).changes)),Fe=>Fe.id,(Fe,fe)=>{var Re=Ja(),Ge=o(Re);const et=xe(()=>(e(ie),e(fe),e(ue),Q(()=>e(ie)[e(fe).path]!==void 0?!e(ie)[e(fe).path]:e(ue)))),bt=xe(()=>(f(),e(fe),Q(()=>f()[e(fe).path]==="pending"))),Ve=xe(()=>(f(),e(fe),Q(()=>f()[e(fe).path]==="applied"))),Je=xe(()=>h()?()=>h()(e(fe).path):void 0);vi(Ri(Ge,{get path(){return e(fe),Q(()=>e(fe).path)},get change(){return e(fe)},get descriptions(){return e(be),Q(()=>e(be).descriptions)},get isExpandedDefault(){return e(et)},get isApplying(){return e(bt)},get hasApplied(){return e(Ve)},onCodeChange:De=>{(function(st,ft){Yt(Ne,e(Ne)[st]=ft)})(e(fe).path,De)},onApplyChanges:()=>{Ue(e(fe).path,e(fe).originalCode,e(fe).modifiedCode)},get onOpenFile(){return e(Je)},get isAgentFromDifferentRepo(){return L()},get isCollapsed(){return e(ie)[e(fe).path]},set isCollapsed(De){Yt(ie,e(ie)[e(fe).path]=De)},get areDescriptionsVisible(){return e(Be)},set areDescriptionsVisible(De){i(Be,De)},$$legacy:!0}),(De,st,ft,pt)=>Yt(He,e(He)[100*st+10*ft+pt.path.length%10]=De),(De,st,ft)=>{var pt;return(pt=e(He))==null?void 0:pt[100*De+10*st+ft.path.length%10]},()=>[Ze,xt,e(fe)]),t(Fe,Re)}),t(Ee,wt)}),t(te,Ie)}),t(U,W)};x(a,U=>{e(r),Q(()=>e(r)&&e(r).length>0)&&U(d)},s)};x(j,a=>{e(se),e(Me),Q(()=>e(se)&&e(Me).length===0)?a(z):a(R,!1)}),t(l,u)};x($e,l=>{e(G)?l(Ae):l(Qe,!1)}),function(l,u){lt(u,!1);let B=V(u,"showModal",12,!1),O=V(u,"applyAllChanges",8);const X=Ut(Kt.key);let T=I(void 0),pe=I(!1);async function M(){if(i(pe,!0),!await X.stashUnstagedChanges())return i(T,"Failed to stash changes. Please manually stash or commit your unstaged changes."),void i(pe,!1);await new Promise(K=>setTimeout(K,1500)),i(T,void 0),B(!1),O()(),i(pe,!1)}function q(){B(!1),i(T,void 0)}_i(),xn(l,{get show(){return B()},title:"Unstaged changes",$$events:{cancel:q},footer:j=>{var z=Fa(),R=o(z);const a=xe(()=>!!e(T)||e(pe));kt(R,{variant:"solid",color:"accent",get disabled(){return e(a)},$$events:{click:M},children:(d,U)=>{var W=za(),E=_e(W),te=Ie=>{var _t=ka(),dt=o(_t);ci(dt,{size:1}),t(Ie,_t)};x(E,Ie=>{e(pe)&&Ie(te)});var qe=w(E,2);let Ze;ye(Ie=>Ze=Wt(qe,1,"c-unstaged-changes-modal__stash-button-text svelte-9eyy34",null,Ze,Ie),[()=>({loading:e(pe)})],xe),t(d,W)},$$slots:{default:!0}});var s=w(R,2);kt(s,{variant:"solid",color:"neutral",get disabled(){return e(pe)},$$events:{click:q},children:(d,U)=>{var W=re("Abort");t(d,W)},$$slots:{default:!0}}),t(j,z)},children:(j,z)=>{var R=Ma(),a=o(R);Oe(a,{children:(U,W)=>{var E=xa(),te=w(_e(E));fn(te,{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}),t(U,E)},$$slots:{default:!0}});var s=w(a,2),d=U=>{Oe(U,{children:(W,E)=>{var te=re();ye(()=>Pe(te,e(T))),t(W,te)},$$slots:{default:!0}})};x(s,U=>{e(T)&&U(d)}),t(j,R)},$$slots:{footer:!0,default:!0}}),ot()}(w(oe,2),{applyAllChanges:mt,get showModal(){return e(Te)},set showModal(l){i(Te,l)},$$legacy:!0}),t(he,Y),ot(),ce()}var ts=v('<div class="file-explorer-contents svelte-5tfpo4"><!></div>'),is=v('<div class="diff-page svelte-5tfpo4"><div class="file-explorer-main svelte-5tfpo4"><!></div></div>');function ns(he,n){lt(n,!0);const[ee,ce]=Qt(),f=()=>zt(p,"$diffModel",ee);let m=V(n,"changedFiles",19,()=>[]),ne=V(n,"pendingFiles",19,()=>[]),P=V(n,"appliedFiles",19,()=>[]),Z=V(n,"agentLabel",19,()=>{}),we=V(n,"latestUserPrompt",19,()=>{}),G=V(n,"isAgentFromDifferentRepo",3,!1),ve=tt(Ai(new Set));const b=Ut(Kt.key),p=Ut(ai.key);let $=tt("summary");const ge=async(L,k,_)=>{const{success:H,hasConflicts:F}=await b.applyChanges(L,k,_);H&&F&&i(ve,new Set([...e(ve),L]),!0)},J=L=>b.openFile(L);(function(L){L.subscribe(k=>{if(k){const _=document.getElementById(bi(k));_&&_.scrollIntoView({behavior:"smooth",block:"center"})}})})(function(L=null){const k=fi(L);return mi(Ti,k),k}(null));var g=is(),h=o(g),A=o(h),D=L=>{var k=ts(),_=o(k);nn(_,()=>f().opts,H=>{var F=qt(),S=_e(F),r=se=>{pa(se,{get changedFiles(){return m()},onApplyChanges:ge,onOpenFile:J,get pendingFiles(){return ne()},get appliedFiles(){return P()}})},Me=se=>{const ze=ke(()=>{var Se,ue;return(ue=(Se=f())==null?void 0:Se.opts)==null?void 0:ue.preloadedExplanation});es(se,{get changedFiles(){return m()},onApplyChanges:ge,onOpenFile:J,get agentLabel(){return Z()},get latestUserPrompt(){return we()},onRenderBackup:()=>{i($,"changedFiles")},get preloadedExplanation(){return e(ze)},get isAgentFromDifferentRepo(){return G()},get conflictFiles(){return e(ve)}})};x(S,se=>{e($)==="changedFiles"?se(r):se(Me,!1)}),t(H,F)}),t(L,k)};x(A,L=>{m()&&L(D)}),t(he,g),ot(),ce()}var as=v('<div class="l-center svelte-ccste2"><!> <p>Loading diff view...</p></div>'),ss=v('<div class="l-main svelte-ccste2"><!></div>');Yi(function(he,n){lt(n,!0);const[ee,ce]=Qt();let f=new Qi(Ni),m=new ai(f);f.registerConsumer(m);let ne=new Kt(f);mi(Kt.key,ne),mi(ai.key,m);let P=ke(()=>zt(m,"$remoteAgentDiffModel",ee).opts);ni(()=>(m.onPanelLoaded(),()=>{f.dispose()}));var Z=qt();gi("message",Gi,function(...G){var ve;(ve=f.onMessageFromExtension)==null||ve.apply(this,G)});var we=_e(Z);Ji(we,()=>mn.Root,(G,ve)=>{ve(G,{children:(b,p)=>{var $=ss(),ge=o($),J=h=>{const A=ke(()=>ne.applyingFilePaths||[]),D=ke(()=>ne.appliedFilePaths||[]),L=ke(()=>e(P).isAgentFromDifferentRepo||!1);ns(h,{get changedFiles(){return e(P).changedFiles},get agentLabel(){return e(P).sessionSummary},get latestUserPrompt(){return e(P).userPrompt},get pendingFiles(){return e(A)},get appliedFiles(){return e(D)},get isAgentFromDifferentRepo(){return e(L)}})},g=h=>{var A=as(),D=o(A);ci(D,{size:1}),t(h,A)};x(ge,h=>{e(P)?h(J):h(g,!1)}),t(b,$)},$$slots:{default:!0}})}),t(he,Z),ot(),ce()},{target:document.getElementById("app")});
