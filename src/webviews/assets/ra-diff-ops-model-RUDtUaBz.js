var h=Object.defineProperty;var y=(o,e,a)=>e in o?h(o,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[e]=a;var i=(o,e,a)=>y(o,typeof e!="symbol"?e+"":e,a);import{w as p,W as n}from"./GuardedIcon-BFT2yJIo.js";class u{constructor(e){i(this,"_applyingFilePaths",p([]));i(this,"_appliedFilePaths",p([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(a=>{e=a})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(a=>{e=a})(),e}async getDiffExplanation(e,a,t=3e4){try{return(await this._asyncMsgSender.send({type:n.diffExplanationRequest,data:{changedFiles:e,apikey:a}},t)).data.explanation}catch(r){return console.error("Failed to get diff explanation:",r),[]}}async groupChanges(e,a=!1,t){try{return(await this._asyncMsgSender.send({type:n.diffGroupChangesRequest,data:{changedFiles:e,changesById:a,apikey:t}})).data.groupedChanges}catch(r){return console.error("Failed to group changes:",r),[]}}async getDescriptions(e,a){try{const t=await this._asyncMsgSender.send({type:n.diffDescriptionsRequest,data:{groupedChanges:e,apikey:a}},1e5);return{explanation:t.data.explanation,error:t.data.error}}catch(t){return console.error("Failed to get descriptions:",t),{explanation:[],error:`Failed to get descriptions: ${t instanceof Error?t.message:String(t)}`}}}async getDescriptionsFromRemoteAgentSummary(e,a,t=0,r=1e5){try{return(await this._asyncMsgSender.send({type:n.remoteAgentGenerateSummaryForDiffDescriptionsRequest,data:{agentId:e,startSequenceId:t,endSequenceId:r,groupedChanges:a}},1e5)).data}catch(s){return console.error("Failed to get descriptions from remote agent summary:",s),{explanation:[],error:`Failed to get descriptions: ${s instanceof Error?s.message:String(s)}`}}}async canApplyChanges(){try{return(await this._asyncMsgSender.send({type:n.canApplyChangesRequest},1e4)).data}catch(e){return console.error("Failed to check if can apply changes:",e),{canApply:!1,hasUnstagedChanges:!1,error:`Failed to check if can apply changes: ${e instanceof Error?e.message:String(e)}`}}}async applyChanges(e,a,t){this._applyingFilePaths.update(r=>[...r.filter(s=>s!==e),e]);try{const r=await this._asyncMsgSender.send({type:n.applyChangesRequest,data:{path:e,originalCode:a,newCode:t}},3e4),{success:s,hasConflicts:d,error:c}=r.data;return s?this._appliedFilePaths.update(l=>[...l.filter(g=>g!==e),e]):c&&console.error("Failed to apply changes:",c),{success:s,hasConflicts:d,error:c}}catch(r){return console.error("applyChanges error",r),{success:!1,error:`Error: ${r instanceof Error?r.message:String(r)}`}}finally{this._applyingFilePaths.update(r=>r.filter(s=>s!==e))}}async previewApplyChanges(e,a,t){try{return(await this._asyncMsgSender.send({type:n.previewApplyChangesRequest,data:{path:e,originalCode:a,newCode:t}},3e4)).data}catch(r){return console.error("previewApplyChanges error",r),{mergedContent:"",hasConflicts:!1,error:`Error: ${r instanceof Error?r.message:String(r)}`}}}async openFile(e){try{const a=await this._asyncMsgSender.send({type:n.openFileRequest,data:{path:e}},1e4);return a.data.success||console.error("Failed to open file:",a.data.error),a.data.success}catch(a){console.error("openFile error",a)}return!1}async stashUnstagedChanges(){try{const e=await this._asyncMsgSender.send({type:n.stashUnstagedChangesRequest},1e4);return e.data.success||console.error("Failed to stash unstaged changes:",e.data.error),e.data.success}catch(e){console.error("stashUnstagedChanges error",e)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:n.reportAgentChangesApplied})}}i(u,"key","remoteAgentsDiffOpsModel");export{u as R};
