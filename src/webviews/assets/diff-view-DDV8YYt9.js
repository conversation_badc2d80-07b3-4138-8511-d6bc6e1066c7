var Zi=Object.defineProperty;var Gi=(p,t,i)=>t in p?Zi(p,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):p[t]=i;var d=(p,t,i)=>Gi(p,typeof t!="symbol"?t+"":t,i);import{a2 as St,aR as Xi,w as qe,b1 as ui,W as ae,aU as ge,b2 as Wt,b3 as Ht,N as at,x as Le,y as Z,a9 as vi,ae as bi,C as Qe,a1 as Xt,P as l,u as oe,b as x,E as Ot,X as dt,at as Ji,H as ee,B as K,z as pe,aa as re,ag as Oe,as as yi,F as He,a6 as Nt,ac as F,ar as Lt,R as Se,a8 as Ci,aO as li,t as Ki,f as Qi,a as Yi,ax as ki,m as Fe,ai as wi,a3 as Ut,ab as Mi,a4 as en,a5 as tn,G as It,$ as xt,a7 as nn,A as qt,ad as hi,D as fi,aj as rn,ay as Bt,aM as on,ao as sn,aP as an}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{A as dn}from"./async-messaging-Bp70swAv.js";import{d as gi,T as cn,a as un}from"./CardAugment-DVbbQqkH.js";import{c as st,a as Ei,I as ln,h as hn}from"./IconButtonAugment-CR0fVrwD.js";import{F as fn,C as Si,S as Oi,a as gn}from"./folder-opened-b0Ugp2il.js";import{r as pn,a as pi}from"./monaco-render-utils-DfwV7QLY.js";import{M as Li}from"./message-broker-BygIEqPd.js";import{B as De}from"./ButtonAugment-CWDjQYWT.js";import{R as _i,K as _n,A as mn,P as vn}from"./Keybindings-TUnsm-bW.js";import{s as bn}from"./chat-model-context-39sqbIF3.js";import{a as yn,M as Cn,g as kn,C as wn}from"./index-CWns8XM2.js";import"./file-type-utils-Zb3vtfL9.js";import"./chat-types-BDRYChZT.js";import"./index-DON3DCoY.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./BaseTextInput-BaUpeUef.js";import"./types-CGlLNakm.js";import"./index-C_brRns6.js";import"./CalloutAugment-C9yL-4XM.js";import"./exclamation-triangle-uRBrTmWU.js";import"./Filespan-9fd1tyrF.js";import"./MaterialIcon-D9dP7dAZ.js";import"./TextCombo-CTaxEruE.js";import"./pen-to-square-Bn06n7i7.js";import"./augment-logo-CNPb11gr.js";var Zt={exports:{}};(function(p,t){var i="__lodash_hash_undefined__",a=1,r=2,s=9007199254740991,c="[object Arguments]",u="[object Array]",b="[object AsyncFunction]",m="[object Boolean]",f="[object Date]",C="[object Error]",L="[object Function]",w="[object GeneratorFunction]",N="[object Map]",W="[object Number]",g="[object Null]",k="[object Object]",z="[object Promise]",Q="[object Proxy]",V="[object RegExp]",U="[object Set]",Y="[object String]",ve="[object Symbol]",le="[object Undefined]",S="[object WeakMap]",P="[object ArrayBuffer]",M="[object DataView]",A=/^\[object .+?Constructor\]$/,E=/^(?:0|[1-9]\d*)$/,_={};_["[object Float32Array]"]=_["[object Float64Array]"]=_["[object Int8Array]"]=_["[object Int16Array]"]=_["[object Int32Array]"]=_["[object Uint8Array]"]=_["[object Uint8ClampedArray]"]=_["[object Uint16Array]"]=_["[object Uint32Array]"]=!0,_[c]=_[u]=_[P]=_[m]=_[M]=_[f]=_[C]=_[L]=_[N]=_[W]=_[k]=_[V]=_[U]=_[Y]=_[S]=!1;var T=typeof St=="object"&&St&&St.Object===Object&&St,de=typeof self=="object"&&self&&self.Object===Object&&self,$=T||de||Function("return this")(),se=t&&!t.nodeType&&t,q=se&&p&&!p.nodeType&&p,te=q&&q.exports===se,G=te&&T.process,v=function(){try{return G&&G.binding&&G.binding("util")}catch{}}(),_e=v&&v.isTypedArray;function ce(e,n){for(var o=-1,h=e==null?0:e.length;++o<h;)if(n(e[o],o,e))return!0;return!1}function be(e){var n=-1,o=Array(e.size);return e.forEach(function(h,j){o[++n]=[j,h]}),o}function he(e){var n=-1,o=Array(e.size);return e.forEach(function(h){o[++n]=h}),o}var ye,ct,ut,lt=Array.prototype,$t=Function.prototype,Ze=Object.prototype,Ye=$["__core-js_shared__"],D=$t.toString,B=Ze.hasOwnProperty,ie=(ye=/[^.]+$/.exec(Ye&&Ye.keys&&Ye.keys.IE_PROTO||""))?"Symbol(src)_1."+ye:"",Ie=Ze.toString,Ge=RegExp("^"+D.call(B).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Re=te?$.Buffer:void 0,xe=$.Symbol,Ne=$.Uint8Array,ht=Ze.propertyIsEnumerable,xi=lt.splice,ze=xe?xe.toStringTag:void 0,Jt=Object.getOwnPropertySymbols,Ni=Re?Re.isBuffer:void 0,$i=(ct=Object.keys,ut=Object,function(e){return ct(ut(e))}),jt=Xe($,"DataView"),et=Xe($,"Map"),At=Xe($,"Promise"),Vt=Xe($,"Set"),Ft=Xe($,"WeakMap"),tt=Xe(Object,"create"),ji=We(jt),Ai=We(et),Vi=We(At),Fi=We(Vt),Di=We(Ft),Kt=xe?xe.prototype:void 0,Dt=Kt?Kt.valueOf:void 0;function Pe(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function Ce(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function Te(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function ft(e){var n=-1,o=e==null?0:e.length;for(this.__data__=new Te;++n<o;)this.add(e[n])}function $e(e){var n=this.__data__=new Ce(e);this.size=n.size}function Ri(e,n){var o=_t(e),h=!o&&Ui(e),j=!o&&!h&&Rt(e),y=!o&&!h&&!j&&si(e),R=o||h||j||y,H=R?function(J,me){for(var ke=-1,ne=Array(J);++ke<J;)ne[ke]=me(ke);return ne}(e.length,String):[],fe=H.length;for(var X in e)!B.call(e,X)||R&&(X=="length"||j&&(X=="offset"||X=="parent")||y&&(X=="buffer"||X=="byteLength"||X=="byteOffset")||Wi(X,fe))||H.push(X);return H}function gt(e,n){for(var o=e.length;o--;)if(ii(e[o][0],n))return o;return-1}function it(e){return e==null?e===void 0?le:g:ze&&ze in Object(e)?function(n){var o=B.call(n,ze),h=n[ze];try{n[ze]=void 0;var j=!0}catch{}var y=Ie.call(n);return j&&(o?n[ze]=h:delete n[ze]),y}(e):function(n){return Ie.call(n)}(e)}function Qt(e){return nt(e)&&it(e)==c}function Yt(e,n,o,h,j){return e===n||(e==null||n==null||!nt(e)&&!nt(n)?e!=e&&n!=n:function(y,R,H,fe,X,J){var me=_t(y),ke=_t(R),ne=me?u:je(y),we=ke?u:je(R),Je=(ne=ne==c?k:ne)==k,mt=(we=we==c?k:we)==k,Ke=ne==we;if(Ke&&Rt(y)){if(!Rt(R))return!1;me=!0,Je=!1}if(Ke&&!Je)return J||(J=new $e),me||si(y)?ei(y,R,H,fe,X,J):function(I,O,vt,Ae,zt,ue,Me){switch(vt){case M:if(I.byteLength!=O.byteLength||I.byteOffset!=O.byteOffset)return!1;I=I.buffer,O=O.buffer;case P:return!(I.byteLength!=O.byteLength||!ue(new Ne(I),new Ne(O)));case m:case f:case W:return ii(+I,+O);case C:return I.name==O.name&&I.message==O.message;case V:case Y:return I==O+"";case N:var Ve=be;case U:var ot=Ae&a;if(Ve||(Ve=he),I.size!=O.size&&!ot)return!1;var bt=Me.get(I);if(bt)return bt==O;Ae|=r,Me.set(I,O);var Pt=ei(Ve(I),Ve(O),Ae,zt,ue,Me);return Me.delete(I),Pt;case ve:if(Dt)return Dt.call(I)==Dt.call(O)}return!1}(y,R,ne,H,fe,X,J);if(!(H&a)){var rt=Je&&B.call(y,"__wrapped__"),ai=mt&&B.call(R,"__wrapped__");if(rt||ai){var Bi=rt?y.value():y,Hi=ai?R.value():R;return J||(J=new $e),X(Bi,Hi,H,fe,J)}}return Ke?(J||(J=new $e),function(I,O,vt,Ae,zt,ue){var Me=vt&a,Ve=ti(I),ot=Ve.length,bt=ti(O),Pt=bt.length;if(ot!=Pt&&!Me)return!1;for(var yt=ot;yt--;){var Ue=Ve[yt];if(!(Me?Ue in O:B.call(O,Ue)))return!1}var di=ue.get(I);if(di&&ue.get(O))return di==O;var Ct=!0;ue.set(I,O),ue.set(O,I);for(var Tt=Me;++yt<ot;){var kt=I[Ue=Ve[yt]],wt=O[Ue];if(Ae)var ci=Me?Ae(wt,kt,Ue,O,I,ue):Ae(kt,wt,Ue,I,O,ue);if(!(ci===void 0?kt===wt||zt(kt,wt,vt,Ae,ue):ci)){Ct=!1;break}Tt||(Tt=Ue=="constructor")}if(Ct&&!Tt){var Mt=I.constructor,Et=O.constructor;Mt==Et||!("constructor"in I)||!("constructor"in O)||typeof Mt=="function"&&Mt instanceof Mt&&typeof Et=="function"&&Et instanceof Et||(Ct=!1)}return ue.delete(I),ue.delete(O),Ct}(y,R,H,fe,X,J)):!1}(e,n,o,h,Yt,j))}function zi(e){return!(!oi(e)||function(n){return!!ie&&ie in n}(e))&&(ni(e)?Ge:A).test(We(e))}function Pi(e){if(o=(n=e)&&n.constructor,h=typeof o=="function"&&o.prototype||Ze,n!==h)return $i(e);var n,o,h,j=[];for(var y in Object(e))B.call(e,y)&&y!="constructor"&&j.push(y);return j}function ei(e,n,o,h,j,y){var R=o&a,H=e.length,fe=n.length;if(H!=fe&&!(R&&fe>H))return!1;var X=y.get(e);if(X&&y.get(n))return X==n;var J=-1,me=!0,ke=o&r?new ft:void 0;for(y.set(e,n),y.set(n,e);++J<H;){var ne=e[J],we=n[J];if(h)var Je=R?h(we,ne,J,n,e,y):h(ne,we,J,e,n,y);if(Je!==void 0){if(Je)continue;me=!1;break}if(ke){if(!ce(n,function(mt,Ke){if(rt=Ke,!ke.has(rt)&&(ne===mt||j(ne,mt,o,h,y)))return ke.push(Ke);var rt})){me=!1;break}}else if(ne!==we&&!j(ne,we,o,h,y)){me=!1;break}}return y.delete(e),y.delete(n),me}function ti(e){return function(n,o,h){var j=o(n);return _t(n)?j:function(y,R){for(var H=-1,fe=R.length,X=y.length;++H<fe;)y[X+H]=R[H];return y}(j,h(n))}(e,qi,Ti)}function pt(e,n){var o,h,j=e.__data__;return((h=typeof(o=n))=="string"||h=="number"||h=="symbol"||h=="boolean"?o!=="__proto__":o===null)?j[typeof n=="string"?"string":"hash"]:j.map}function Xe(e,n){var o=function(h,j){return h==null?void 0:h[j]}(e,n);return zi(o)?o:void 0}Pe.prototype.clear=function(){this.__data__=tt?tt(null):{},this.size=0},Pe.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},Pe.prototype.get=function(e){var n=this.__data__;if(tt){var o=n[e];return o===i?void 0:o}return B.call(n,e)?n[e]:void 0},Pe.prototype.has=function(e){var n=this.__data__;return tt?n[e]!==void 0:B.call(n,e)},Pe.prototype.set=function(e,n){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=tt&&n===void 0?i:n,this},Ce.prototype.clear=function(){this.__data__=[],this.size=0},Ce.prototype.delete=function(e){var n=this.__data__,o=gt(n,e);return!(o<0)&&(o==n.length-1?n.pop():xi.call(n,o,1),--this.size,!0)},Ce.prototype.get=function(e){var n=this.__data__,o=gt(n,e);return o<0?void 0:n[o][1]},Ce.prototype.has=function(e){return gt(this.__data__,e)>-1},Ce.prototype.set=function(e,n){var o=this.__data__,h=gt(o,e);return h<0?(++this.size,o.push([e,n])):o[h][1]=n,this},Te.prototype.clear=function(){this.size=0,this.__data__={hash:new Pe,map:new(et||Ce),string:new Pe}},Te.prototype.delete=function(e){var n=pt(this,e).delete(e);return this.size-=n?1:0,n},Te.prototype.get=function(e){return pt(this,e).get(e)},Te.prototype.has=function(e){return pt(this,e).has(e)},Te.prototype.set=function(e,n){var o=pt(this,e),h=o.size;return o.set(e,n),this.size+=o.size==h?0:1,this},ft.prototype.add=ft.prototype.push=function(e){return this.__data__.set(e,i),this},ft.prototype.has=function(e){return this.__data__.has(e)},$e.prototype.clear=function(){this.__data__=new Ce,this.size=0},$e.prototype.delete=function(e){var n=this.__data__,o=n.delete(e);return this.size=n.size,o},$e.prototype.get=function(e){return this.__data__.get(e)},$e.prototype.has=function(e){return this.__data__.has(e)},$e.prototype.set=function(e,n){var o=this.__data__;if(o instanceof Ce){var h=o.__data__;if(!et||h.length<199)return h.push([e,n]),this.size=++o.size,this;o=this.__data__=new Te(h)}return o.set(e,n),this.size=o.size,this};var Ti=Jt?function(e){return e==null?[]:(e=Object(e),function(n,o){for(var h=-1,j=n==null?0:n.length,y=0,R=[];++h<j;){var H=n[h];o(H,h,n)&&(R[y++]=H)}return R}(Jt(e),function(n){return ht.call(e,n)}))}:function(){return[]},je=it;function Wi(e,n){return!!(n=n??s)&&(typeof e=="number"||E.test(e))&&e>-1&&e%1==0&&e<n}function We(e){if(e!=null){try{return D.call(e)}catch{}try{return e+""}catch{}}return""}function ii(e,n){return e===n||e!=e&&n!=n}(jt&&je(new jt(new ArrayBuffer(1)))!=M||et&&je(new et)!=N||At&&je(At.resolve())!=z||Vt&&je(new Vt)!=U||Ft&&je(new Ft)!=S)&&(je=function(e){var n=it(e),o=n==k?e.constructor:void 0,h=o?We(o):"";if(h)switch(h){case ji:return M;case Ai:return N;case Vi:return z;case Fi:return U;case Di:return S}return n});var Ui=Qt(function(){return arguments}())?Qt:function(e){return nt(e)&&B.call(e,"callee")&&!ht.call(e,"callee")},_t=Array.isArray,Rt=Ni||function(){return!1};function ni(e){if(!oi(e))return!1;var n=it(e);return n==L||n==w||n==b||n==Q}function ri(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=s}function oi(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function nt(e){return e!=null&&typeof e=="object"}var si=_e?function(e){return function(n){return e(n)}}(_e):function(e){return nt(e)&&ri(e.length)&&!!_[it(e)]};function qi(e){return(n=e)!=null&&ri(n.length)&&!ni(n)?Ri(e):Pi(e);var n}p.exports=function(e,n){return Yt(e,n)}})(Zt,Zt.exports);const Mn=Xi(Zt.exports);function Ii(p){return function(t){return"unitOfCodeWork"in t&&!function(i){return i.children.length>0&&"childIds"in i}(t)}(p)?[p]:p.children.flatMap(Ii)}var Ee=(p=>(p.edit="edit",p.instruction="instruction",p))(Ee||{}),Gt=(p=>(p[p.instructionDrawer=0]="instructionDrawer",p[p.chunkActionPanel=1]="chunkActionPanel",p))(Gt||{});class En{constructor(t,i,a){d(this,"_originalModel");d(this,"_modifiedModel");d(this,"_fullEdits",[]);d(this,"_currEdit");d(this,"_currOriginalEdit");d(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(i=>{this._modifiedModel.applyEdits([i])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});d(this,"finish",()=>this._completeCurrEdit());d(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);d(this,"_completeCurrEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};if(!t)return i;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const a=this._nextModifiedInsertPosition(),r=t.stagedEndLine-t.stagedStartLine,s={range:new this._monaco.Range(a.lineNumber,0,a.lineNumber+r,0),text:""};i.modified.push(s),this._modifiedModel.applyEdits([s]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return i});d(this,"_startNewEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},i.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),i});d(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const i=this._nextModifiedInsertPosition(),a={...this._currEdit,text:t.newText,range:new this._monaco.Range(i.lineNumber,i.column,i.lineNumber,i.column)};return this._modifiedModel.applyEdits([a]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[a]:[]}});d(this,"_nextModifiedInsertPosition",()=>{var i;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((i=this._currEdit.text)==null?void 0:i.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=i,this._monaco=a,this._originalModel=this._monaco.editor.createModel(i),this._modifiedModel=this._monaco.editor.createModel(i)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Sn{constructor(t,i,a){d(this,"_asyncMsgSender");d(this,"_editor");d(this,"_chatModel");d(this,"_focusModel",new fn);d(this,"_hasScrolledOnInit",!1);d(this,"_markHasScrolledOnInit",gi(()=>{this._hasScrolledOnInit=!0},200));d(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});d(this,"_subscribers",new Set);d(this,"_disposables",[]);d(this,"_rootChunk");d(this,"_keybindings",qe({}));d(this,"_requestId",qe(void 0));d(this,"requestId",this._requestId);d(this,"_disableResolution",qe(!1));d(this,"disableResolution",ui(this._disableResolution));d(this,"_disableApply",qe(!1));d(this,"disableApply",ui(this._disableApply));d(this,"_currStream");d(this,"_isLoadingDiffChunks",qe(!1));d(this,"_selectionLines",qe(void 0));d(this,"_mode",qe(Ee.edit));d(this,"initializeEditor",t=>{var i,a,r,s,c,u,b,m,f,C,L,w;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Si(new Li(st),st,new Oi),(a=(i=this._monaco.editor).registerCommand)==null||a.call(i,"acceptFocusedChunk",this.acceptFocusedChunk),(s=(r=this._monaco.editor).registerCommand)==null||s.call(r,"rejectFocusedChunk",this.rejectFocusedChunk),(u=(c=this._monaco.editor).registerCommand)==null||u.call(c,"acceptAllChunks",this.acceptAllChunks),(m=(b=this._monaco.editor).registerCommand)==null||m.call(b,"rejectAllChunks",this.rejectAllChunks),(C=(f=this._monaco.editor).registerCommand)==null||C.call(f,"focusNextChunk",this.focusNextChunk),(w=(L=this._monaco.editor).registerCommand)==null||w.call(L,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(N=>this.notifySubscribers())}),this.initialize()});d(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));d(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});d(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});d(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const i=this.leaves[0];this.revealChunk(i)}this.notifyDiffViewUpdated(),this.notifySubscribers()});d(this,"onMouseMoveModified",t=>{var r,s,c,u,b,m;if(((r=t.target.position)==null?void 0:r.lineNumber)===void 0||this.leaves===void 0)return;const i=this.editorOffset,a=(s=t.target.position)==null?void 0:s.lineNumber;for(let f=0;f<this.leaves.length;f++){const C=this.leaves[f],L=(c=C.unitOfCodeWork.lineChanges)==null?void 0:c.lineChanges[0].modifiedStart,w=(u=C.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedEnd,N=(b=C.unitOfCodeWork.lineChanges)==null?void 0:b.lineChanges[0].originalStart,W=(m=C.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0].originalEnd;if(L!==void 0&&w!==void 0&&N!==void 0&&W!==void 0){if(L!==w||a!==L){if(L<=a&&a<w){this.setCurrFocusedChunkIdx(f,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const g=this._editor.getOriginalEditor(),k=g.getOption(this._monaco.editor.EditorOption.lineHeight),z=g.getScrolledVisiblePosition({lineNumber:N,column:0}),Q=g.getScrolledVisiblePosition({lineNumber:W+1,column:0});if(z===null||Q===null)continue;const V=z.top-k/2+i,U=Q.top-k/2+i;if(t.event.posy>=V&&t.event.posy<=U){this.setCurrFocusedChunkIdx(f,!1);break}break}}}});d(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:ae.diffViewWindowFocusChange,data:t})});d(this,"setCurrFocusedChunkIdx",(t,i=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),i&&this.revealCurrFocusedChunk(),this.notifySubscribers())});d(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});d(this,"revealChunk",t=>{var r;const i=(r=t.unitOfCodeWork.lineChanges)==null?void 0:r.lineChanges[0],a=i==null?void 0:i.modifiedStart;a!==void 0&&this._editor.revealLineNearTop(a-1)});d(this,"renderCentralOverlayWidget",t=>{const i=()=>({editor:this._editor,id:"central-overlay-widget"}),a=pn(t,i(),{monaco:this._monaco});return{update:()=>{a.update(i())},destroy:a.destroy}});d(this,"renderInstructionsDrawerViewZone",(t,i)=>{let a=!1,r=i;const s=i.autoFocus??!0,c=f=>{s&&!a&&(this._editor.revealLineNearTop(f),a=!0)},u=f=>({...f,ordinal:Gt.instructionDrawer,editor:this._editor,afterLineNumber:f.line}),b=pi(t,u(i)),m=[];return s&&m.push(this._editor.onDidUpdateDiff(()=>{c(r.line)})),{update:f=>{const C={...r,...f};Mn(C,r)||(b.update(u(C)),r=C,c(C.line))},destroy:()=>{b.destroy(),m.forEach(f=>f.dispose())}}});d(this,"renderActionsViewZone",(t,i)=>{const a=s=>{var u;let c;return c=s.chunk?(u=s.chunk.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedStart:1,{...s,ordinal:Gt.chunkActionPanel,editor:this._editor,afterLineNumber:c?c-1:void 0}},r=pi(t,a(i));return{update:s=>{r.update(a(s))},destroy:r.destroy}});d(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});d(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});d(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});d(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});d(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});d(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});d(this,"initialize",async()=>{var b;const t=await this._asyncMsgSender.send({type:ae.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:i,instruction:a,keybindings:r,editable:s}=t.data;this._editor.updateOptions({readOnly:!s});const c=ge(this._keybindings);this._keybindings.set(r??c);const u=a==null?void 0:a.selection;u&&(u.start.line===u.end.line&&u.start.character===u.end.character&&this._mode.set(Ee.instruction),ge(this.selectionLines)===void 0&&this._selectionLines.set({start:u.start.line,end:u.end.line})),this.updateModels(i.originalCode??"",i.modifiedCode??"",{rootPath:i.repoRoot,relPath:i.pathName}),(b=this._currStream)==null||b.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});d(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:ae.disposeDiffView})});d(this,"_tryFetchStream",async()=>{var i,a,r;const t=this._asyncMsgSender.stream({type:ae.diffViewFetchPendingStream},15e3,6e4);for await(const s of t)switch(s.type){case ae.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(s.data.requestId);const c=this._editor.getOriginalEditor().getValue();this._currStream=new En(s.data.streamId,c,this._monaco),this._syncStreamToModels();break}case ae.diffViewDiffStreamEnded:if(((i=this._currStream)==null?void 0:i.id)!==s.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case ae.diffViewDiffStreamChunk:{if(((a=this._currStream)==null?void 0:a.id)!==s.data.streamId)return;const c=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!c)return this.setLoading(!1),void this._cleanupStream();const u=(r=this._currStream)==null?void 0:r.onReceiveChunk(s);u&&(this._applyDeltaDiff(u),ge(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});d(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case ae.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case ae.diffViewAcceptAllChunks:this.acceptAllChunks();break;case ae.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case ae.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case ae.diffViewFocusPrevChunk:this.focusPrevChunk();break;case ae.diffViewFocusNextChunk:this.focusNextChunk()}});d(this,"_applyDeltaDiff",t=>{const i=this._editor.getOriginalEditor().getModel(),a=this._editor.getModifiedEditor().getModel();i&&a&&(i.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(r=>{i.pushEditOperations([],[r],()=>[])}),t.modified.forEach(r=>{a.pushEditOperations([],[r],()=>[])}))});d(this,"_cleanupStream",()=>{var t;if(this._currStream){const i=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(i),this._currStream=void 0,this._resetScrollOnInit()}});d(this,"_syncStreamToModels",()=>{var a,r;const t=(a=this._currStream)==null?void 0:a.originalValue,i=(r=this._currStream)==null?void 0:r.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),i&&i!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(i)});d(this,"acceptChunk",async t=>{ge(this._disableApply)||this.acceptChunks([t])});d(this,"acceptChunks",async(t,i=!1)=>{ge(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Wt.accept,i),await Ht(),this.areModelsEqual()&&!ge(this.isLoading)&&this.disposeDiffViewPanel())});d(this,"areModelsEqual",()=>{var a,r;const t=(a=this._editor.getModel())==null?void 0:a.original,i=(r=this._editor.getModel())==null?void 0:r.modified;return(t==null?void 0:t.getValue())===(i==null?void 0:i.getValue())});d(this,"rejectChunk",async t=>{this.rejectChunks([t])});d(this,"rejectChunks",async(t,i=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Wt.reject,i),await Ht(),this.areModelsEqual()&&!ge(this.isLoading)&&this.disposeDiffViewPanel()});d(this,"notifyDiffViewUpdated",gi(()=>{this.notifyResolvedChunks([],Wt.accept)},1e3));d(this,"notifyResolvedChunks",async(t,i,a=!1)=>{var s;const r=(s=this._editor.getModel())==null?void 0:s.original.uri.path;r&&await this._asyncMsgSender.send({type:ae.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:r,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(c=>c.unitOfCodeWork),resolveType:i,shouldApplyToAll:a}},2e3)});d(this,"executeDiffChunks",(t,i)=>{var f,C,L;if(ge(this._disableResolution)||i&&ge(this._disableApply))return;const a=(f=this._editor.getModel())==null?void 0:f.original,r=(C=this._editor.getModel())==null?void 0:C.modified;if(!a||!r||this._currStream!==void 0)return;const s=[],c=[];for(const w of t){const N=(L=w.unitOfCodeWork.lineChanges)==null?void 0:L.lineChanges[0];if(!N||w.unitOfCodeWork.originalCode===void 0||w.unitOfCodeWork.modifiedCode===void 0)continue;let W={startLineNumber:N.originalStart,startColumn:1,endLineNumber:N.originalEnd,endColumn:1},g={startLineNumber:N.modifiedStart,startColumn:1,endLineNumber:N.modifiedEnd,endColumn:1};const k=i?w.unitOfCodeWork.modifiedCode:w.unitOfCodeWork.originalCode;k!==void 0&&(s.push({range:W,text:k}),c.push({range:g,text:k}))}a.pushEditOperations([],s,()=>[]),r.pushEditOperations([],c,()=>[]);const u=this._focusModel.nextIdx({nowrap:!0});if(u===void 0)return;const b=u===this._focusModel.focusedItemIdx?u-1:u,m=this._focusModel.items[b];m&&this.revealChunk(m)});d(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});d(this,"handleInstructionSubmit",t=>{const i=this._editor.getModifiedEditor(),a=this.getSelectedCodeDetails(i);if(!a)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,a)});d(this,"updateModels",(t,i,a)=>{var c,u;const r=(u=(c=this._editor.getModel())==null?void 0:c.original)==null?void 0:u.uri,s=(a&&this._monaco.Uri.file(a.relPath))??r;if(s)if((r==null?void 0:r.fsPath)!==s.fsPath||(r==null?void 0:r.authority)!==s.authority){const b=s.with({fragment:crypto.randomUUID()}),m=s.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,b),modified:this._monaco.editor.createModel(i??"",void 0,m)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==i&&this.getModifiedEditor().setValue(i??"");else console.warn("No URI found for diff view. Not updating models.")});d(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=a,this._asyncMsgSender=new dn(r=>st.postMessage(r)),this.initializeEditor(i)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Ii(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var s,c;const t=[],i=this._editor.getLineChanges(),a=(s=this._editor.getModel())==null?void 0:s.original,r=(c=this._editor.getModel())==null?void 0:c.modified;if(i&&a&&r){for(const u of i){const b=mi({startLineNumber:u.originalStartLineNumber,endLineNumber:u.originalEndLineNumber}),m=mi({startLineNumber:u.modifiedStartLineNumber,endLineNumber:u.modifiedEndLineNumber}),f=On(this._editor,b,m);t.push(f)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(u=>u.id)}}}getSelectedCodeDetails(t){const i=t.getModel();if(!i)return null;const a=i.getLanguageId(),r=1,s=1,c={lineNumber:i.getLineCount(),column:i.getLineMaxColumn(i.getLineCount())},u=ge(this._selectionLines);if(!u)throw new Error("No selection lines found");const b=Math.min(u.end+1,c.lineNumber),m=new this._monaco.Range(u.start+1,1,b,i.getLineMaxColumn(b));let f=i.getValueInRange(m);b<i.getLineCount()&&(f+=i.getEOL());const C=new this._monaco.Range(r,s,m.startLineNumber,m.startColumn),L=Math.min(m.endLineNumber+1,c.lineNumber),w=new this._monaco.Range(L,1,c.lineNumber,c.column);return{selectedCode:f,prefix:i.getValueInRange(C),suffix:i.getValueInRange(w),path:i.uri.path,language:a,prefixBegin:C.startLineNumber-1,suffixEnd:w.endLineNumber-1}}}function On(p,t,i){var s,c;const a=(s=p.getModel())==null?void 0:s.original,r=(c=p.getModel())==null?void 0:c.modified;if(!a||!r)throw new Error("No models found");return function(u,b,m,f){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:u,modifiedCode:b,lineChanges:{lineChanges:[{originalStart:m.startLineNumber,originalEnd:m.endLineNumber,modifiedStart:f.startLineNumber,modifiedEnd:f.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(a.getValueInRange(t),r.getValueInRange(i),t,i)}function mi(p){return p.endLineNumber===0?{startLineNumber:p.startLineNumber+1,startColumn:1,endLineNumber:p.startLineNumber+1,endColumn:1}:{startLineNumber:p.startLineNumber,startColumn:1,endLineNumber:p.endLineNumber+1,endColumn:1}}var Ln=Z('<span class="c-keyboard-shortcut-hint__icon svelte-1txw16l"> </span>'),In=Z("<span></span>");function Be(p,t){at(t,!0);let i=Le(t,"class",3,""),a=Le(t,"keybinding",19,()=>{}),r=Le(t,"icons",19,()=>{var c;return((c=a())==null?void 0:c.split("-"))??[]});var s=In();vi(s,21,r,bi,(c,u)=>{var b=Ln(),m=oe(b);Qe(()=>Xt(m,l(u))),x(c,b)}),Qe(()=>Ot(s,1,`c-keyboard-shortcut-hint ${i()}`,"svelte-1txw16l")),x(p,s),dt()}var xn=Z("<!> Accept",1),Nn=Z("<!> Reject",1),$n=Z("<div></div>  <div><div><!> <!></div></div>",1);function jn(p,t){at(t,!0);const[i,a]=Nt(),r=()=>re(b,"$keybindingsStore",i);let s=Le(t,"align",3,"right"),c=Le(t,"heightInPx",3,1),u=Le(t,"disableApply",3,!1);const b=t.diffViewModel.keybindings;let m=Oe(0);const f=S=>{F(m,S,!0)};let C,L=Oe(!1);function w(){C&&(clearTimeout(C),C=void 0),F(L,!1)}function N(S){S.target.closest(".c-button-container")?w():S.type==="mouseenter"||S.type==="mousemove"?(w(),C=setTimeout(()=>{F(L,!0)},400)):S.type==="mouseleave"&&w()}var W=$n(),g=ee(W);let k;Ei(g,(S,P)=>{var M,A;return(A=(M=t.diffViewModel).renderActionsViewZone)==null?void 0:A.call(M,S,P)},()=>({chunk:t.leaf,heightInPx:c(),onDomNodeTop:f}));var z=K(g,2);let Q;z.__mousemove=N;var V=oe(z);let U;var Y=oe(V),ve=S=>{De(S,{size:1,variant:"ghost",color:"success",$$events:{click(...P){var M;(M=t.onAccept)==null||M.apply(this,P)}},children:(P,M)=>{var A=xn();Be(ee(A),{get keybinding(){return r().acceptFocusedChunk}}),x(P,A)},$$slots:{default:!0}})};pe(Y,S=>{u()||S(ve)});var le=K(Y,2);De(le,{size:1,variant:"ghost",color:"error",$$events:{click(...S){var P;(P=t.onReject)==null||P.apply(this,S)}},children:(S,P)=>{var M=Nn();Be(ee(M),{get keybinding(){return r().rejectFocusedChunk}}),x(S,M)},$$slots:{default:!0}}),Qe((S,P,M)=>{k=Ot(g,1,"svelte-zm1705",null,k,S),Q=Ot(z,1,"c-chunk-action-panel-anchor svelte-zm1705",null,Q,P),yi(z,`top: ${l(m)??""}px;`),U=Ot(V,1,"c-button-container svelte-zm1705",null,U,M)},[()=>({"c-chunk-diff-border--focused":!!t.leaf&&t.isFocused}),()=>({"c-chunk-action-panel-anchor--left":s()==="left","c-chunk-action-panel-anchor--right":s()==="right","c-chunk-action-panel-anchor--focused":t.isFocused}),()=>({"c-button-container--focused":t.isFocused,"c-button-container--transparent":l(L)})]),He("mouseenter",z,N),He("mouseleave",z,N),x(p,W),dt(),a()}Ji(["mousemove"]);var An=Z('<span class="c-diff-page-counter svelte-1w94ymh">Generating changes <!></span>'),Vn=Z('<span class="c-diff-page-counter svelte-1w94ymh"> <!></span>'),Fn=Z('<span class="c-diff-page-counter svelte-1w94ymh">No changes</span>'),Dn=Z("<!> Back",1),Rn=Z("<!> Next",1),zn=Z("<!> Accept All",1),Pn=Z("<!> Reject All",1),Tn=Z("<!> <!>",1),Wn=Z("<!> <!> <!>",1),Un=Z('<div class="c-top-action-panel-anchor svelte-1w94ymh"><div class="c-button-container svelte-1w94ymh"><!> <!> <!></div></div>');function qn(p,t){at(t,!0);const[i,a]=Nt(),r=()=>re(t.diffViewModel,"$diffViewModel",i),s=()=>re(m,"$requestId",i),c=()=>re(l(N),"$isLoadingStore",i),u=()=>re(b,"$keybindingsStore",i),b=t.diffViewModel.keybindings,m=t.diffViewModel.requestId;let f=Se(()=>r().disableResolution),C=Se(()=>r().disableApply),L=Oe("x");Lt(()=>{r().currFocusedChunkIdx!==void 0?F(L,(r().currFocusedChunkIdx+1).toString(),!0):F(L,"x")});let w,N=Se(()=>r().isLoading),W=Se(()=>{var E;return!!((E=r().leaves)!=null&&E.length)}),g=Oe("Copy request ID"),k=Oe(()=>{});function z(E){E||(clearTimeout(w),w=void 0,F(g,"Copy request ID"))}async function Q(){s()&&(await navigator.clipboard.writeText(s()),F(g,"Copied!"),clearTimeout(w),w=setTimeout(l(k),1500))}var V=Un(),U=oe(V),Y=oe(U),ve=E=>{const _=Se(()=>[un.Hover]);cn(E,{onOpenChange:z,get content(){return l(g)},get triggerOn(){return l(_)},get requestClose(){return l(k)},set requestClose(T){F(k,T,!0)},children:(T,de)=>{ln(T,{variant:"ghost",color:"neutral",size:1,onclick:Q,children:($,se)=>{Ci($,{name:"clipboard",children:(q,te)=>{gn(q)},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}})};pe(Y,E=>{s()&&E(ve)});var le=K(Y,2),S=E=>{var _=An(),T=K(oe(_));li(T,{size:1,get loading(){return c()}}),x(E,_)},P=(E,_)=>{var T=$=>{var se=Vn(),q=oe(se),te=K(q);li(te,{size:1,get loading(){return c()}}),Qe(()=>{var G,v;return Xt(q,`${l(L)??""} of ${((v=(G=r())==null?void 0:G.leaves)==null?void 0:v.length)??""} `)}),x($,se)},de=$=>{var se=Fn();x($,se)};pe(E,$=>{l(W)?$(T):$(de,!1)},_)};pe(le,E=>{!l(W)&&c()?E(S):E(P,!1)});var M=K(le,2),A=E=>{var _=Wn(),T=ee(_);De(T,{size:1,variant:"ghost",color:"neutral",get onclick(){return t.diffViewModel.focusPrevChunk},children:(q,te)=>{var G=Dn();Be(ee(G),{get keybinding(){return u().focusPrevChunk}}),x(q,G)},$$slots:{default:!0}});var de=K(T,2);De(de,{size:1,variant:"ghost",color:"neutral",get onclick(){return t.diffViewModel.focusNextChunk},children:(q,te)=>{var G=Rn();Be(ee(G),{get keybinding(){return u().focusNextChunk}}),x(q,G)},$$slots:{default:!0}});var $=K(de,2),se=q=>{var te=Tn(),G=ee(te),v=ce=>{De(ce,{size:1,variant:"ghost",color:"success",get onclick(){return t.diffViewModel.acceptAllChunks},children:(be,he)=>{var ye=zn();Be(ee(ye),{get keybinding(){return u().acceptAllChunks}}),x(be,ye)},$$slots:{default:!0}})};pe(G,ce=>{re(l(C),"$disableApply",i)||ce(v)});var _e=K(G,2);De(_e,{size:1,variant:"ghost",color:"error",get onclick(){return t.diffViewModel.rejectAllChunks},children:(ce,be)=>{var he=Pn();Be(ee(he),{get keybinding(){return u().rejectAllChunks}}),x(ce,he)},$$slots:{default:!0}}),x(q,te)};pe($,q=>{re(l(f),"$disableResolution",i)||q(se)}),x(E,_)};pe(M,E=>{l(W)&&E(A)}),x(p,V),dt(),a()}var Bn=Qi("<svg><!></svg>"),Hn=Z("<!> <!> <!> <!>",1),Zn=Z("Close <!>",1),Gn=Z('<div></div> <div class="instruction-drawer-panel svelte-1cxscce"><div class="instruction-drawer-panel__contents svelte-1cxscce" tabindex="0" role="button"><div class="l-input-area__input svelte-1cxscce"><!></div> <div class="c-instruction-drawer-panel__btn-container svelte-1cxscce"><!> <!></div></div></div>',1);function Xn(p,t){at(t,!1);const[i,a]=Nt(),r=()=>re(ve,"$modeStore",i),s=()=>re(le,"$selectionLinesStore",i),c=()=>re(f(),"$diffViewModel",i),u=()=>re(l(m),"$isLoadingStore",i),b=Fe(),m=Fe();let f=Le(t,"diffViewModel",8),C=Le(t,"initialConversation",24,()=>{}),L=Le(t,"initialFlags",24,()=>{});const w=yn.getContext().monaco,N={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},W=new Li(st);let g=new Oi;W.registerConsumer(g);let k=new Si(W,st,g,{initialConversation:C(),initialFlags:L()});const z=k.currentConversationModel;let Q;W.registerConsumer(k),bn(k);let V,U=Fe(),Y=Fe("");const ve=f().mode,le=f().selectionLines;function S(){const v=f().getModifiedEditor(),_e=ge(w);if(!v||!_e||(V==null||V.clear(),!s()))return;const ce=s().start,be=s().end,he={range:new _e.Range(ce+1,1,be+1,1),options:N};V||(V=v.createDecorationsCollection()),V.set([he])}function P(){var v;return!!((v=l(Y))!=null&&v.trim())&&(f().handleInstructionSubmit(l(Y)),!0)}ki(async()=>{await Ht(),de(),F(M,f().editorOffset)}),wi(()=>{Q==null||Q.destroy(),V==null||V.clear()});let M=Fe(0),A=Fe(57),E=Fe(void 0),_=Fe(void 0);const T=()=>{var v;return(v=l(E))==null?void 0:v.requestFocus()},de=()=>{var v;return(v=l(E))==null?void 0:v.forceFocus()},$=v=>{z.saveDraftMentions(v.current)};function se(v){F(Y,v.rawText)}Ut(()=>(r(),Ee),()=>{F(b,(r()===Ee.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs")}),Ut(()=>c(),()=>{Mi(F(m,c().isLoading),"$isLoadingStore",i)}),Ut(()=>(l(U),s()),()=>{if(l(U)){if(s()==null)F(A,0);else{const v=l(U).scrollHeight;F(A,Math.min(40+v,108))}Q==null||Q.update({heightInPx:l(A)}),S()}}),en(),tn();var q=It(),te=ee(q),G=v=>{var _e=Gn(),ce=ee(_e);Ei(ce,D=>function(B){if(B){const ie=s()?s().start:1;Q=f().renderInstructionsDrawerViewZone(B,{line:ie,heightInPx:l(A),onDomNodeTop:Ie=>{F(M,f().editorOffset+Ie)},autoFocus:!0}),S()}}(D));var be=K(ce,2),he=oe(be),ye=oe(he),ct=oe(ye);xt(_i.Root(ct,{focusOnInit:!0,children:(D,B)=>{var ie=Hn(),Ie=ee(ie);_n(Ie,{shortcuts:{Enter:()=>P()}});var Ge=K(Ie,2);xt(mn(Ge,{requestEditorFocus:T,onMentionItemsUpdated:$,$$legacy:!0}),Ne=>F(_,Ne),()=>l(_));var Re=K(Ge,2);_i.Content(Re,{get content(){return l(Y)},onContentChanged:se});var xe=K(Re,2);vn(xe,{get placeholder(){return l(b)}}),x(D,ie)},$$slots:{default:!0},$$legacy:!0}),D=>F(E,D),()=>l(E)),xt(ye,D=>F(U,D),()=>l(U));var ut=K(ye,2),lt=oe(ut);De(lt,{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$events:{click(...D){var B;(B=f().disposeDiffViewPanel)==null||B.apply(this,D)}},children:(D,B)=>{var ie=Zn();Be(K(ee(ie)),{keybinding:"esc"}),x(D,ie)},$$slots:{default:!0}});var $t=K(lt,2);const Ze=fi(()=>(r(),hi(Ee),qt(()=>r()===Ee.instruction?"Instruct Augment":"Edit with Augment"))),Ye=fi(()=>(l(Y),u(),qt(()=>!l(Y).trim()||u())));De($t,{id:"send",size:1,variant:"solid",color:"accent",get title(){return l(Ze)},get disabled(){return l(Ye)},$$events:{click:P},children:(D,B)=>{var ie=nn();Qe(()=>Xt(ie,(r(),hi(Ee),qt(()=>r()===Ee.instruction?"Instruct":"Edit")))),x(D,ie)},$$slots:{default:!0,iconRight:(D,B)=>{Ci(D,{slot:"iconRight",name:"send-horizontal",children:(ie,Ie)=>{(function(Ge,Re){const xe=Ki(Re,["children","$$slots","$$events","$$legacy"]);var Ne=Bn();Yi(Ne,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...xe}));var ht=oe(Ne);hn(ht,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',!0),x(Ge,Ne)})(ie,{})},$$slots:{default:!0}})}}}),Qe(()=>yi(be,`top: ${l(M)??""}px; height: ${l(A)??""}px;`)),He("click",he,de),He("keydown",he,D=>{D.key==="Enter"&&(de(),D.stopPropagation(),D.preventDefault())}),x(v,_e)};pe(te,v=>{s()&&v(G)}),x(p,q),dt(),a()}var Jn=Z('<div class="sticky-top svelte-453n6i"><!></div> <!>',1),Kn=Z('<div class="diff-view-container svelte-453n6i"><!> <div class="editor-container svelte-453n6i"><div class="editor svelte-453n6i"></div> <!></div></div>');an(function(p,t){at(t,!0);const[i,a]=Nt(),r=()=>re(on,"$themeStore",i),s=()=>re(l(c),"$diffViewModel",i);let c=Oe(void 0),u=Oe(void 0),b=Se(()=>{var g;return(g=l(c))==null?void 0:g.disableApply}),m=Se(()=>{var g;return(g=l(c))==null?void 0:g.disableResolution});function f(g){const k=sn.dark;return kn((g==null?void 0:g.category)||k,g==null?void 0:g.intensity)??wn.get(k)}Lt(()=>{var k;const g=r();l(c)&&((k=l(c))==null||k.updateTheme(f(g)))});let C=Oe(void 0);Lt(()=>{l(C)&&l(u)&&!l(c)&&Mi(F(c,new Sn(l(u),f(r()),l(C)),!0),"$diffViewModel",i)}),ki(async()=>{F(C,await window.augmentDeps.monaco,!0),l(C)||console.error("Monaco not loaded. Diff view cannot be initialized.")}),wi(()=>{var g;(g=l(c))==null||g.dispose()});let L=Se(()=>{var g;return(g=s())==null?void 0:g.leaves}),w=Oe(!1);Lt(()=>{var g;(g=l(c))==null||g.updateIsWebviewFocused(l(w))});var N=It();He("message",Bt,function(...g){var k,z;(z=(k=l(c))==null?void 0:k.handleMessageFromExtension)==null||z.apply(this,g)}),He("focus",Bt,()=>F(w,!0)),He("blur",Bt,()=>F(w,!1));var W=ee(N);rn(W,()=>Cn.Root,(g,k)=>{k(g,{children:(z,Q)=>{var V=Kn(),U=oe(V),Y=M=>{var A=Jn(),E=ee(A);qn(oe(E),{get diffViewModel(){return s()}}),Xn(K(E,2),{get diffViewModel(){return s()}}),x(M,A)};pe(U,M=>{s()&&M(Y)});var ve=K(U,2),le=oe(ve);xt(le,M=>F(u,M),()=>l(u));var S=K(le,2),P=M=>{var A=It(),E=ee(A);vi(E,17,()=>l(L),bi,(_,T,de)=>{var $=It(),se=ee($),q=te=>{const G=Se(()=>{var v;return((v=s())==null?void 0:v.currFocusedChunkIdx)===de});jn(te,{get isFocused(){return l(G)},onAccept:()=>{var v;return(v=s())==null?void 0:v.acceptChunk(l(T))},onReject:()=>{var v;return(v=s())==null?void 0:v.rejectChunk(l(T))},get diffViewModel(){return s()},get leaf(){return l(T)},align:"right",get disableApply(){return re(l(b),"$disableApply",i)}})};pe(se,te=>{l(T).unitOfCodeWork.modifiedCode!==l(T).unitOfCodeWork.originalCode&&te(q)}),x(_,$)}),x(M,A)};pe(S,M=>{var A;s()&&((A=l(L))!=null&&A.length)&&!re(l(m),"$disableResolution",i)&&M(P)}),x(z,V)},$$slots:{default:!0}})}),x(p,N),dt(),a()},{target:document.getElementById("app")});
