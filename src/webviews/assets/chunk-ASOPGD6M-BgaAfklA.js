import{_ as n,n as o,j as c}from"./AugmentMessage-cgapx9im.js";var l=n((a,t)=>{const r=a.append("rect");if(r.attr("x",t.x),r.attr("y",t.y),r.attr("fill",t.fill),r.attr("stroke",t.stroke),r.attr("width",t.width),r.attr("height",t.height),t.name&&r.attr("name",t.name),t.rx&&r.attr("rx",t.rx),t.ry&&r.attr("ry",t.ry),t.attrs!==void 0)for(const s in t.attrs)r.attr(s,t.attrs[s]);return t.class&&r.attr("class",t.class),r},"drawRect"),d=n((a,t)=>{const r={x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,stroke:t.stroke,class:"rect"};l(a,r).lower()},"drawBackgroundRect"),h=n((a,t)=>{const r=t.text.replace(o," "),s=a.append("text");s.attr("x",t.x),s.attr("y",t.y),s.attr("class","legend"),s.style("text-anchor",t.anchor),t.class&&s.attr("class",t.class);const e=s.append("tspan");return e.attr("x",t.x+2*t.textMargin),e.text(r),s},"drawText"),y=n((a,t,r,s)=>{const e=a.append("image");e.attr("x",t),e.attr("y",r);const x=c(s);e.attr("xlink:href",x)},"drawImage"),p=n((a,t,r,s)=>{const e=a.append("use");e.attr("x",t),e.attr("y",r);const x=c(s);e.attr("xlink:href",`#${x}`)},"drawEmbeddedImage"),g=n(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),f=n(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj");export{d as a,f as b,p as c,l as d,y as e,h as f,g};
