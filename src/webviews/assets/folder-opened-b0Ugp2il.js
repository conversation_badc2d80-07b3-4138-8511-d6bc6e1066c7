var Os=Object.defineProperty;var We=o=>{throw TypeError(o)};var Rs=(o,t,e)=>t in o?Os(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var l=(o,t,e)=>Rs(o,typeof t!="symbol"?t+"":t,e),ve=(o,t,e)=>t.has(o)||We("Cannot "+e);var a=(o,t,e)=>(ve(o,t,"read from private field"),e?e.call(o):t.get(o)),T=(o,t,e)=>t.has(o)?We("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,e),C=(o,t,e,s)=>(ve(o,t,"write to private field"),s?s.call(o,e):t.set(o,e),e),y=(o,t,e)=>(ve(o,t,"access private method"),e);var ee=(o,t,e,s)=>({set _(i){C(o,t,i,e)},get _(){return a(o,t,s)}});import{aU as le,be as Us,w as ct,t as vs,f as Xt,a as Cs,u as Ss,b as Jt,aQ as Ns,bf as Ps,bg as zs,bh as $s,bi as Gs,bj as js,W as wt,ak as Ws}from"./GuardedIcon-BFT2yJIo.js";import{M as Bs}from"./message-broker-BygIEqPd.js";import{f as Vs,i as bs}from"./file-type-utils-Zb3vtfL9.js";import{R as Ft,C as H,b as Ce,I as se,a as V,i as U,A as qt,E as k,h as Zs,c as Ht,S as dt,d as Et,P as $t,e as xs,f as Ot,g as Vt,j as Qs,s as Se}from"./chat-types-BDRYChZT.js";import{g as ws,h as Ks,b as Gt,c as ie,i as Xs,j as _t,F as mt,a as re,k as Te,l as Js,m as Me,n as Be,o as Ys,C as ti,p as Is,q as ei,r as si,s as ii,R as ri,E as ni,t as oi,u as Ve,v as ai,w as Ze,x as Qe,y as li,z as Ke,B as hi,G as Xe,H as ci,I as Je,J as be}from"./index-DON3DCoY.js";import{C as di}from"./types-CGlLNakm.js";import{T as D,a as Ye,b as xe}from"./chat-model-context-39sqbIF3.js";import{h as Es}from"./IconButtonAugment-CR0fVrwD.js";import"./index-C_brRns6.js";import{d as ui}from"./CardAugment-DVbbQqkH.js";class mi{constructor(t){l(this,"trackedExperiments",new Set);this.dependencies=t}_getChatMessagePayload(){const t=le(this.dependencies.chatModeType),e=le(this.dependencies.agentExecutionMode),s={chatMode:t,agentExecutionMode:t==="localAgent"?e:void 0};return t==="localAgent"&&(s.sendMode=le(this.dependencies.currentSendMode)),s}trackEvent(t,e){const s={...this._getChatMessagePayload(),...e};this.trackSimpleEvent(t,s)}trackSimpleEvent(t,e){this.dependencies.extensionClient.trackEventWithTypes(t,e)}trackExperimentViewed(t,e,s){if(e===Us.OFF)return;const i=`${t}:${e}`;this.trackedExperiments.has(i)||(this.trackedExperiments.add(i),this.dependencies.extensionClient.trackExperimentViewed(t,e,s))}}class gi{constructor(t=[]){l(this,"_items",[]);l(this,"_focusedItemIdx");l(this,"_subscribers",new Set);l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});l(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const e=t?this._items.indexOf(t):-1;e===-1?this.setFocusIdx(void 0):this.setFocusIdx(e)});l(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const e=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-e)%this._items.length,this.notifySubscribers()});l(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));l(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});l(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});l(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}class ne{constructor(){this._disposables=[]}add(t){if(t===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(t),t}addAll(...t){t.forEach(e=>this.add(e))}adopt(t){this._disposables.push(...t._disposables),t._disposables.length=0}dispose(){for(const t of this._disposables)t.dispose();this._disposables.length=0}}class _i{constructor(t=new ne,e=new ne){this._disposables=new ne,this._priorityDisposables=new ne,this._disposables.adopt(t),this._priorityDisposables.adopt(e)}addDisposable(t,e=!1){return e?this._priorityDisposables.add(t):this._disposables.add(t)}addDisposables(...t){this._disposables.addAll(...t)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}const fi=[".md",".mdc"],ts=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class ze extends _i{constructor(){super(),this._logger=ws()}async loadRules({includeGuidelines:t=!1,query:e,maxResults:s,contextRules:i}={}){this._logger.debug(`Loading rules with includeGuidelines=${t}, query=${e}, maxResults=${s}`);let r=[];const n=await this.loadRuleFromFile(Ks);if(n&&r.push({...n,type:Ft.ALWAYS_ATTACHED}),t){const c=await this.loadRuleFromFile();this._logger.debug("Loaded guidelines rules"),c&&r.push(c)}this._logger.debug("Using file system approach to load rules");const h=await this.loadDirectory((void 0)(Gt,ie));if(this._logger.debug(`Loaded ${h.length} rules from directory`),r.push(...h),e&&e.trim()){const c=e.toLowerCase().trim();r=r.filter(u=>{const d=u.path.toLowerCase().includes(c),m=u.content.toLowerCase().includes(c);return d||m}),this._logger.debug(`Filtered to ${r.length} rules matching query: ${e}`)}return s&&s>0&&(r=r.slice(0,s),this._logger.debug(`Limited to ${r.length} rules`)),this._logger.debug(`Returning ${r.length} total rules`),i!==void 0&&i.length>0&&(r=ze.filterRulesByContext(r,i),this._logger.debug(`Filtered to ${r.length} rules based on context`)),r}static filterRulesByContext(t,e){return[...t.filter(s=>s.type!==Ft.MANUAL),...t.filter(s=>s.type===Ft.MANUAL&&e.some(i=>i.path===s.path))]}static calculateRulesAndGuidelinesCharacterCount(t){const{rules:e,workspaceGuidelinesContent:s,contextRules:i=[]}=t,r=e.filter(u=>u.type===Ft.ALWAYS_ATTACHED).reduce((u,d)=>u+d.content.length+d.path.length,0),n=e.filter(u=>u.type===Ft.AGENT_REQUESTED).reduce((u,d)=>{var m;return u+100+(((m=d.description)==null?void 0:m.length)??0)+d.path.length},0),h=r+e.filter(u=>u.type===Ft.MANUAL).filter(u=>i.some(d=>d.path===u.path)).reduce((u,d)=>u+d.content.length+d.path.length,0)+n+s.length,c=t.rulesAndGuidelinesLimit&&h>t.rulesAndGuidelinesLimit;return{totalCharacterCount:h,isOverLimit:c,warningMessage:c&&t.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${h} chars)
        exceeds the limit of ${t.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}async loadRuleFromFile(t=Xs){const e=_t();if(!e)return void this._logger.warn("Client workspaces not initialized");const s=await e.getWorkspaceRoot();if(!s)return;const i=(void 0)(s,t),r=await e.getPathInfo(i);if(r.exists&&r.type===mt.File)try{const n=(await e.readFile(i)).contents;if(!n)return void this._logger.warn(`${t} is empty: ${i}`);const h=re.parseRuleFile(n,t);return this._logger.debug(`Loaded rule from ${t}`),{path:t,content:h.content,type:h.type,description:h.description}}catch(n){this._logger.error(`Error loading guidelines file ${i}: ${String(n)}`)}}async loadDirectory(t){const e=[];try{const s=_t();if(!s)return this._logger.warn("Client workspaces not initialized"),e;const i=await s.getWorkspaceRoot();if(!i)return this._logger.warn("No workspace root found"),e;const r=(void 0)(i,t);this._logger.debug(`Looking for rules in: ${r}`);const n=await s.getPathInfo(r);return this._logger.debug(`Path info for ${r}: ${JSON.stringify(n)}`),n.exists&&n.type===mt.Directory?(this._logger.debug(`Rules folder exists at ${r}`),await this.processRuleDirectory(s,r,e,""),this._logger.debug(`Loaded ${e.length} rules from ${r}`),e):(this._logger.debug(`Rules folder not found at ${r}`),e)}catch(s){return this._logger.error(`Error loading rules: ${String(s)}`),e}}async loadDirectoryFromPath(t){const e=[];try{const s=_t();if(!s)return this._logger.warn("Client workspaces not initialized"),e;let i;if(!(void 0)(t)){const n=await s.getWorkspaceRoot();if(!n)return this._logger.warn("No workspace root found"),e;i=(void 0)(n,t),this._logger.debug(`Loading rules from workspace-relative path: ${i}`)}const r=await s.getPathInfo(i);return r.exists&&r.type===mt.Directory?(this._logger.debug(`Rules folder exists at ${i}`),await this.processRuleDirectory(s,i,e,""),this._logger.debug(`Loaded ${e.length} rules from ${i}`),e):(this._logger.debug(`Rules folder not found at ${i}`),e)}catch(s){return this._logger.error(`Error loading rules from path: ${String(s)}`),e}}async processRuleDirectory(t,e,s,i){const r=await t.listDirectory(e,1,!1);if(r.errorMessage)this._logger.error(`Error listing directory ${e}: ${r.errorMessage}`);else{this._logger.debug(`Processing directory: ${e}, found ${r.entries.length} entries`);for(const n of r.entries){const h=(void 0)(e,n),c=(void 0)(i,n),u=await t.getPathInfo(h);if(u.exists)if(u.type===mt.Directory)this._logger.debug(`Processing subdirectory: ${n}`),await this.processRuleDirectory(t,h,s,c);else if(u.type===mt.File&&fi.some(d=>n.endsWith(d))){this._logger.debug(`Processing rule file: ${n}`);try{const d=(await t.readFile(h)).contents||"",m=re.parseRuleFile(d,n);s.push({path:c,content:m.content,type:m.type,description:m.description}),this._logger.debug(`Successfully loaded rule: ${c}`)}catch(d){this._logger.error(`Error loading rule file ${h}: ${String(d)}`)}}else u.type===mt.File&&this._logger.debug(`Skipping non-markdown file: ${n}`)}}}async createRule(t,e=!1){const s=_t();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r=(void 0)(i,Gt,ie);e&&(r=(void 0)(r,"imported"));const n=t.path.endsWith(".md")?t.path:`${t.path}.md`,h=(void 0)(r,n),c=await s.getQualifiedPathName(h);if(!c)throw new Error(`Unable to get qualified path for: ${h}`);if((await s.getPathInfo(h)).exists)throw new Error(`Rule file already exists: ${n}`);const u=re.formatRuleFileForMarkdown(t);return await s.writeFile(c,u),{...t,path:n}}async deleteRule(t){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const e=_t();if(!e)throw new Error("Client workspaces not initialized");const s=await e.getWorkspaceRoot();if(!s)throw new Error("No workspace root found");let i;if((void 0)(t)||(i=(void 0)(s,Gt,ie,t)),(await e.getPathInfo(i)).exists){const r=await e.getQualifiedPathName(i);r&&(await e.deleteFile(r),this._logger.debug(`Deleted rule file: ${i}`)),this._logger.debug(`Deleted rule file: ${i}`)}}async updateRuleFile(t,e){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const s=_t();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r;(void 0)(t)||(r=t.startsWith(Gt)?(void 0)(i,t):(void 0)(i,Gt,ie,t));const n=await s.getQualifiedPathName(r);if(!n)throw new Error(`Unable to get qualified path for: ${r}`);await s.writeFile(n,e),this._logger.debug(`Updated rule file: ${r}`)}async importFile(t,e){const s=_t();if(!s)throw new Error("Client workspaces not initialized");let i,r;if(!(void 0)(t)){const h=await s.getWorkspaceRoot();if(!h)throw new Error("No workspace root found");i=(void 0)(h,t),r=t,this._logger.debug(`Importing file from workspace-relative path: ${i}`)}const n=await s.getPathInfo(i);if(!n.exists||n.type!==mt.File)return this._logger.error(`File not found: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const h=(await s.readFile(i)).contents;if(!h)return this._logger.error(`File is empty: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};const c=re.parseRuleFile(h,r),u=(void 0)(r).name.replace(".","");return await this.createRule({path:u,content:c.content,type:c.type},e),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(h){return this._logger.error(`Error importing file ${t}: ${String(h)}`),{successfulImports:0,duplicates:String(h).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(t,e){try{const s=await this.loadDirectoryFromPath(t);if(s.length===0)return this._logger.debug(`No rules found in directory: ${t}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${s.length} existing rules from ${t}`);let i=0,r=0;const n=s.length;for(const h of s)try{const c=(void 0)(h.path).name,u=(void 0)(h.path),d=u==="."?c:(void 0)(u,c);await this.createRule({path:d,content:h.content,type:h.type},e),i++,this._logger.debug(`Successfully imported rule: ${h.path} -> ${d}`)}catch(c){this._logger.warn(`Failed to import rule ${h.path}: ${String(c)}`),String(c).includes("already exists")&&r++}return this._logger.info(`Imported ${i} rules from ${t}, ${r} duplicates skipped`),{successfulImports:i,duplicates:r,totalAttempted:n}}catch(s){return this._logger.error(`Error importing directory: ${String(s)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const t=_t();if(!t)return this._logger.warn("No workspace available for auto-import detection"),[];const e=await t.getWorkspaceRoot();if(!e)return this._logger.warn("No workspace root found for auto-import detection"),[];const s=[];for(const{directory:i,file:r,name:n}of ts){let h=!1,c=!1;if(i)try{const u=(void 0)(e,i),d=await t.getPathInfo(u);h=d.exists===!0&&d.type===mt.Directory}catch(u){this._logger.debug(`Error checking directory ${i}: ${String(u)}`)}if(r)try{const u=(void 0)(e,r),d=await t.getPathInfo(u);c=d.exists===!0&&d.type===mt.File}catch(u){this._logger.debug(`Error checking file ${r}: ${String(u)}`)}h&&c?s.push({label:n,description:`Import existing rules from ${i} and ${r}`,directory:i,file:r}):h?s.push({label:n,description:`Import existing rules from ${i}`,directory:i}):c&&s.push({label:n,description:`Import existing rules from ${r}`,file:r})}return s}async processAutoImportSelection(t){const e=ts.find(c=>c.name===t);if(!e)throw new Error(`Unknown auto-import option: ${t}`);const s=[];e.directory&&s.push(this.importDirectory(e.directory,!0)),e.file&&s.push(this.importFile(e.file,!0));const i=await Promise.all(s),r=i.reduce((c,u)=>c+u.successfulImports,0),n=i.reduce((c,u)=>c+u.duplicates,0),h=i.reduce((c,u)=>c+u.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${t}, imported: ${r}, duplicates: ${n}, total attempted: ${h}`),{importedRulesCount:r,duplicatesCount:n,totalAttempted:h,source:t}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}function pi(o,t,e=1e3){let s=null,i=0;const r=ct(t),n=()=>{const h=(()=>{const c=Date.now();if(s!==null&&c-i<e)return s;const u=o();return s=u,i=c,u})();r.set(h)};return{subscribe:r.subscribe,resetCache:()=>{s=null,n()},updateStore:n}}var Ts=(o=>(o[o.unset=0]="unset",o[o.positive=1]="positive",o[o.negative=2]="negative",o))(Ts||{});const we=new Map,yi=()=>{let o=Promise.resolve();const t=new Map,e=new Map,s=crypto.randomUUID(),i={end:r=>{const n=t.get(r);return console.debug("END LINK: ",r,s),n==null||n(),i},start:async(r,n)=>{const{promise:h,unlock:c,reject:u}=(m=>{let v=()=>{},g=()=>{},f=(S,w)=>()=>{w&&clearTimeout(w),S()};return{promise:new Promise((S,w)=>{let p,x=()=>{w("Chain was reset")};m&&m>0&&(p=setTimeout(x,m)),g=f(x,p),v=f(S,p)}),unlock:v,reject:g}})(n),d=o;return o=h.finally(()=>{t.delete(r),e.delete(r)}),t.set(r,()=>{c(),t.delete(r)}),e.set(r,()=>{u(),e.delete(r)}),await d,console.debug("START LINK: ",r,s),i},rejectAll:()=>{o=Promise.resolve();try{e.forEach(r=>{r(new Error("Chain was reset"))})}finally{t.clear(),e.clear()}},unlockAll:()=>{o=Promise.resolve();try{t.forEach(r=>{r()})}finally{t.clear(),e.clear()}}};return i};function es(o,t){return function(e,s){if(e.length<=s||e.length===0)return{truncatedText:e};const i=e.split(`
`),r="... additional lines truncated ..."+(i[0].endsWith("\r")?"\r":"");let n,h="";if(i.length<2||i[0].length+i[i.length-1].length+r.length>s){const c=Math.floor(s/2);h=[e.slice(0,c),"<...>",e.slice(-c)].join(""),n=[1,1,i.length,i.length]}else{const c=[],u=[];let d=r.length+1;for(let m=0;m<Math.floor(i.length/2);m++){const v=i[m],g=i[i.length-1-m],f=v.length+g.length+2;if(d+f>s)break;d+=f,c.push(v),u.push(g)}n=[1,c.length,i.length-u.length+1,i.length],c.push(r),c.push(...u.reverse()),h=c.join(`
`)}return{truncatedText:h,shownRangeWhenTruncated:n}}(o,t).truncatedText}function vi(o){var e;if(!o)return se.IMAGE_FORMAT_UNSPECIFIED;switch((e=o.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return se.JPEG;case"png":return se.PNG;default:return se.IMAGE_FORMAT_UNSPECIFIED}}function Ci(o,t,e){var i,r;if(o.phase!==D.cancelled&&o.phase!==D.completed&&o.phase!==D.error)return;let s;return(i=o.result)!=null&&i.contentNodes?(s=function(n,h){return n.map(c=>c.type===Te.ContentText?{type:Ce.CONTENT_TEXT,text_content:c.text_content}:c.type===Te.ContentImage&&c.image_content&&h?{type:Ce.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:vi(c.image_content.media_type)}}:{type:Ce.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(o.result.contentNodes,e),{content:"",is_error:o.result.isError,request_id:o.result.requestId,tool_use_id:t,content_nodes:s}):((r=o.result)==null?void 0:r.text)!==void 0?{content:o.result.text,is_error:o.result.isError,request_id:o.result.requestId,tool_use_id:t}:void 0}function Si(o=[]){let t;for(const e of o){if(e.type===H.TOOL_USE)return e;e.type===H.TOOL_USE_START&&(t=e)}return t}function bi(o,t,e,s){if(!o||!t)return[];let i=!1;return t.filter(r=>{var h;const n=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):e.getToolUseState(r.requestId??o,(h=r.tool_use)==null?void 0:h.tool_use_id);return i===!1&&n.phase!==D.new&&n.phase!==D.unknown&&n.phase!==D.checkingSafety&&r.tool_use!==void 0||(n.phase===D.runnable&&(i=!0),!1)})}function Or(o,t){if(o.contentNodes&&o.contentNodes.length>0){const e=o.contentNodes.map(s=>{if(s.type===Te.ContentText){let i="";return s.text_content&&(i=es(s.text_content,t/o.contentNodes.length)),{...s,text_content:i}}return s});return{...o,contentNodes:e}}return{...o,text:es(o.text,t)}}const Lt="__NEW_AGENT__",Rr=o=>o.chatItemType===void 0,Ur=(o,t)=>{var r;const e=o.chatHistory.at(-1);if(!e||!U(e))return qt.notRunning;if(!(e.status===k.success||e.status===k.failed||e.status===k.cancelled))return qt.running;const s=((r=e.structured_output_nodes)==null?void 0:r.filter(n=>n.type===H.TOOL_USE&&!!n.tool_use))??[];let i;if(t.enableParallelTools?(i=bi(e.request_id,s,o).at(-1),!i&&s.length>0&&(i=s.at(-1))):i=s.at(-1),!i||!i.tool_use)return qt.notRunning;switch(o.getToolUseState(e.request_id,i.tool_use.tool_use_id).phase){case D.runnable:return qt.awaitingUserAction;case D.cancelled:return qt.notRunning;default:return qt.running}},Ae=o=>U(o)&&!!o.request_message,xi=o=>o.chatHistory.findLast(t=>Ae(t)),Nr=(o,t)=>{const e=xi(o);return e!=null&&e.request_id?o.historyFrom(e.request_id,!0).filter(s=>U(s)&&(!t||t(s))):[]},Pr=o=>{var s;const t=o.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!U(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===H.TOOL_USE))??[];for(const i of e)if(i.tool_use&&o.getToolUseState(t.request_id,i.tool_use.tool_use_id).phase===D.runnable)return o.updateToolUseState({requestId:t.request_id,toolUseId:i.tool_use.tool_use_id,phase:D.cancelled}),!0;return!1},N={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point.",abridgedHistoryParams:{totalCharsLimit:1e4,userMessageCharsLimit:1e3,agentResponseCharsLimit:2e3,actionCharsLimit:200,numFilesModifiedLimit:10,numFilesCreatedLimit:10,numFilesDeletedLimit:10,numFilesViewedLimit:10,numTerminalCommandsLimit:10}};function Fe(o,t,e=.5,s=.5){if(o.length<=t||t<=0)return o;if(e+s>1)throw new Error("startRatio + endRatio cannot exceed 1.0");const i="...",r=t-3;if(r<=0)return i.substring(0,t);const n=Math.floor(r*e),h=Math.floor(r*s);return o.substring(0,n)+i+o.substring(o.length-h)}const B=(o,t)=>o!==void 0?o:t;function wi(o){for(const t of o.filesModified)o.filesViewed.delete(t)}function Ii(o,t){try{const e=JSON.parse(o.input_json);switch(o.tool_name){case"str-replace-editor":e.path&&t.filesModified.add(e.path);break;case"save-file":e.path&&t.filesCreated.add(e.path);break;case"remove-files":if(e.file_paths&&Array.isArray(e.file_paths))for(const s of e.file_paths)t.filesDeleted.add(s);break;case"view":e.path&&t.filesViewed.add(e.path);break;case"launch-process":e.command&&t.terminalCommands.add(e.command)}}catch(e){console.warn("Failed to parse tool use input:",e)}}function jt(o,t,e,s="files"){if(o.size===0)return null;const i=Array.from(o).sort((c,u)=>c.localeCompare(u)),r=c=>Fe(c,e);if(i.length<=t)return i.map(r);const n=i.slice(0,t).map(r),h=i.length-t;return n.push(`... ${h} more ${s}`),n}function Ei(o,t){let e=o.userMessage,s=o.agentFinalResponse;e.length>t.userMessageCharsLimit&&(e=Fe(e,t.userMessageCharsLimit)),s.length>t.agentResponseCharsLimit&&(s=Fe(s,t.agentResponseCharsLimit));const i=o.agentActionsSummary.filesModified.size>0||o.agentActionsSummary.filesCreated.size>0||o.agentActionsSummary.filesDeleted.size>0||o.agentActionsSummary.filesViewed.size>0||o.agentActionsSummary.terminalCommands.size>0,r={userMessage:e,agentResponse:s&&s.trim()!==""?s:null,hasActions:i,filesModified:jt(o.agentActionsSummary.filesModified,t.numFilesModifiedLimit,t.actionCharsLimit),filesCreated:jt(o.agentActionsSummary.filesCreated,t.numFilesCreatedLimit,t.actionCharsLimit),filesDeleted:jt(o.agentActionsSummary.filesDeleted,t.numFilesDeletedLimit,t.actionCharsLimit),filesViewed:jt(o.agentActionsSummary.filesViewed,t.numFilesViewedLimit,t.actionCharsLimit),terminalCommands:jt(o.agentActionsSummary.terminalCommands,t.numTerminalCommandsLimit,t.actionCharsLimit,"commands"),wasInterrupted:o.wasInterrupted,continues:o.continues};return Js(r)}function Ti(o){var e,s;let t=o.request_message||"";return(e=o.structured_request_nodes)!=null&&e.some(i=>i.image_node||i.image_id_node)&&(t+=`
[User attached image]`),(s=o.structured_request_nodes)!=null&&s.some(i=>i.file_node||i.file_id_node)&&(t+=`
[User attached document]`),t}function ss(o){var t,e,s,i,r,n,h,c,u;try{if(!o)return console.log("historySummaryParams is empty. Using default params"),N;const d=JSON.parse(o),m={triggerOnHistorySizeChars:B(d.trigger_on_history_size_chars,N.triggerOnHistorySizeChars),historyTailSizeCharsToExclude:B(d.history_tail_size_chars_to_exclude,N.historyTailSizeCharsToExclude),triggerOnHistorySizeCharsWhenCacheExpiring:B(d.trigger_on_history_size_chars_when_cache_expiring,N.triggerOnHistorySizeCharsWhenCacheExpiring),prompt:B(d.prompt,N.prompt),cacheTTLMs:B(d.cache_ttl_ms,N.cacheTTLMs),bufferTimeBeforeCacheExpirationMs:B(d.buffer_time_before_cache_expiration_ms,N.bufferTimeBeforeCacheExpirationMs),summaryNodeRequestMessageTemplate:B(d.summary_node_request_message_template,N.summaryNodeRequestMessageTemplate),summaryNodeResponseMessage:B(d.summary_node_response_message,N.summaryNodeResponseMessage),abridgedHistoryParams:{totalCharsLimit:B((t=d.abridged_history_params)==null?void 0:t.total_chars_limit,N.abridgedHistoryParams.totalCharsLimit),userMessageCharsLimit:B((e=d.abridged_history_params)==null?void 0:e.user_message_chars_limit,N.abridgedHistoryParams.userMessageCharsLimit),agentResponseCharsLimit:B((s=d.abridged_history_params)==null?void 0:s.agent_response_chars_limit,N.abridgedHistoryParams.agentResponseCharsLimit),actionCharsLimit:B((i=d.abridged_history_params)==null?void 0:i.action_chars_limit,N.abridgedHistoryParams.actionCharsLimit),numFilesModifiedLimit:B((r=d.abridged_history_params)==null?void 0:r.num_files_modified_limit,N.abridgedHistoryParams.numFilesModifiedLimit),numFilesCreatedLimit:B((n=d.abridged_history_params)==null?void 0:n.num_files_created_limit,N.abridgedHistoryParams.numFilesCreatedLimit),numFilesDeletedLimit:B((h=d.abridged_history_params)==null?void 0:h.num_files_deleted_limit,N.abridgedHistoryParams.numFilesDeletedLimit),numFilesViewedLimit:B((c=d.abridged_history_params)==null?void 0:c.num_files_viewed_limit,N.abridgedHistoryParams.numFilesViewedLimit),numTerminalCommandsLimit:B((u=d.abridged_history_params)==null?void 0:u.num_terminal_commands_limit,N.abridgedHistoryParams.numTerminalCommandsLimit)}};m.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),m.summaryNodeRequestMessageTemplate=N.summaryNodeRequestMessageTemplate);const v={...m,prompt:m.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",v),m}catch(d){return console.error("Failed to parse history_summary_params:",d),N}}function Ie(o){return o.reduce((t,e)=>t+Ms(e),0)}function Ms(o){let t=0;return o.request_nodes?t+=JSON.stringify(o.request_nodes).length:t+=(o.request_message||"").length,o.response_nodes?t+=JSON.stringify(o.response_nodes).length:t+=(o.response_text||"").length,t}class Mi{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,e){const s=new AbortController,i=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(i)},e);this._controllers.add(s),this._timeoutIds.add(i)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}var Ai=Object.getOwnPropertyNames,Fi=Object.getOwnPropertySymbols,qi=Object.prototype.hasOwnProperty;function is(o,t){return function(e,s,i){return o(e,s,i)&&t(e,s,i)}}function oe(o){return function(t,e,s){if(!t||!e||typeof t!="object"||typeof e!="object")return o(t,e,s);var i=s.cache,r=i.get(t),n=i.get(e);if(r&&n)return r===e&&n===t;i.set(t,e),i.set(e,t);var h=o(t,e,s);return i.delete(t),i.delete(e),h}}function rs(o){return Ai(o).concat(Fi(o))}var ki=Object.hasOwn||function(o,t){return qi.call(o,t)};function At(o,t){return o===t||!o&&!t&&o!=o&&t!=t}var Di="__v",Li="__o",Hi="_owner",ns=Object.getOwnPropertyDescriptor,os=Object.keys;function Oi(o,t,e){var s=o.length;if(t.length!==s)return!1;for(;s-- >0;)if(!e.equals(o[s],t[s],s,s,o,t,e))return!1;return!0}function Ri(o,t){return At(o.getTime(),t.getTime())}function Ui(o,t){return o.name===t.name&&o.message===t.message&&o.cause===t.cause&&o.stack===t.stack}function Ni(o,t){return o===t}function as(o,t,e){var s=o.size;if(s!==t.size)return!1;if(!s)return!0;for(var i,r,n=new Array(s),h=o.entries(),c=0;(i=h.next())&&!i.done;){for(var u=t.entries(),d=!1,m=0;(r=u.next())&&!r.done;)if(n[m])m++;else{var v=i.value,g=r.value;if(e.equals(v[0],g[0],c,m,o,t,e)&&e.equals(v[1],g[1],v[0],g[0],o,t,e)){d=n[m]=!0;break}m++}if(!d)return!1;c++}return!0}var Pi=At;function zi(o,t,e){var s=os(o),i=s.length;if(os(t).length!==i)return!1;for(;i-- >0;)if(!As(o,t,e,s[i]))return!1;return!0}function Wt(o,t,e){var s,i,r,n=rs(o),h=n.length;if(rs(t).length!==h)return!1;for(;h-- >0;)if(!As(o,t,e,s=n[h])||(i=ns(o,s),r=ns(t,s),(i||r)&&(!i||!r||i.configurable!==r.configurable||i.enumerable!==r.enumerable||i.writable!==r.writable)))return!1;return!0}function $i(o,t){return At(o.valueOf(),t.valueOf())}function Gi(o,t){return o.source===t.source&&o.flags===t.flags}function ls(o,t,e){var s=o.size;if(s!==t.size)return!1;if(!s)return!0;for(var i,r,n=new Array(s),h=o.values();(i=h.next())&&!i.done;){for(var c=t.values(),u=!1,d=0;(r=c.next())&&!r.done;){if(!n[d]&&e.equals(i.value,r.value,i.value,r.value,o,t,e)){u=n[d]=!0;break}d++}if(!u)return!1}return!0}function ji(o,t){var e=o.length;if(t.length!==e)return!1;for(;e-- >0;)if(o[e]!==t[e])return!1;return!0}function Wi(o,t){return o.hostname===t.hostname&&o.pathname===t.pathname&&o.protocol===t.protocol&&o.port===t.port&&o.hash===t.hash&&o.username===t.username&&o.password===t.password}function As(o,t,e,s){return!(s!==Hi&&s!==Li&&s!==Di||!o.$$typeof&&!t.$$typeof)||ki(t,s)&&e.equals(o[s],t[s],s,s,o,t,e)}var Bi="[object Arguments]",Vi="[object Boolean]",Zi="[object Date]",Qi="[object Error]",Ki="[object Map]",Xi="[object Number]",Ji="[object Object]",Yi="[object RegExp]",tr="[object Set]",er="[object String]",sr="[object URL]",ir=Array.isArray,hs=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,cs=Object.assign,rr=Object.prototype.toString.call.bind(Object.prototype.toString),zr=bt();bt({strict:!0}),bt({circular:!0}),bt({circular:!0,strict:!0});var ds=bt({createInternalComparator:function(){return At}});function bt(o){o===void 0&&(o={});var t,e=o.circular,s=e!==void 0&&e,i=o.createInternalComparator,r=o.createState,n=o.strict,h=n!==void 0&&n,c=function(d){var m=d.circular,v=d.createCustomConfig,g=d.strict,f={areArraysEqual:g?Wt:Oi,areDatesEqual:Ri,areErrorsEqual:Ui,areFunctionsEqual:Ni,areMapsEqual:g?is(as,Wt):as,areNumbersEqual:Pi,areObjectsEqual:g?Wt:zi,arePrimitiveWrappersEqual:$i,areRegExpsEqual:Gi,areSetsEqual:g?is(ls,Wt):ls,areTypedArraysEqual:g?Wt:ji,areUrlsEqual:Wi};if(v&&(f=cs({},f,v(f))),m){var S=oe(f.areArraysEqual),w=oe(f.areMapsEqual),p=oe(f.areObjectsEqual),x=oe(f.areSetsEqual);f=cs({},f,{areArraysEqual:S,areMapsEqual:w,areObjectsEqual:p,areSetsEqual:x})}return f}(o),u=function(d){var m=d.areArraysEqual,v=d.areDatesEqual,g=d.areErrorsEqual,f=d.areFunctionsEqual,S=d.areMapsEqual,w=d.areNumbersEqual,p=d.areObjectsEqual,x=d.arePrimitiveWrappersEqual,X=d.areRegExpsEqual,L=d.areSetsEqual,O=d.areTypedArraysEqual,R=d.areUrlsEqual;return function(I,E,M){if(I===E)return!0;if(I==null||E==null)return!1;var xt=typeof I;if(xt!==typeof E)return!1;if(xt!=="object")return xt==="number"?w(I,E,M):xt==="function"&&f(I,E,M);var ut=I.constructor;if(ut!==E.constructor)return!1;if(ut===Object)return p(I,E,M);if(ir(I))return m(I,E,M);if(hs!=null&&hs(I))return O(I,E,M);if(ut===Date)return v(I,E,M);if(ut===RegExp)return X(I,E,M);if(ut===Map)return S(I,E,M);if(ut===Set)return L(I,E,M);var A=rr(I);return A===Zi?v(I,E,M):A===Yi?X(I,E,M):A===Ki?S(I,E,M):A===tr?L(I,E,M):A===Ji?typeof I.then!="function"&&typeof E.then!="function"&&p(I,E,M):A===sr?R(I,E,M):A===Qi?g(I,E,M):A===Bi?p(I,E,M):(A===Vi||A===Xi||A===er)&&x(I,E,M)}}(c);return function(d){var m=d.circular,v=d.comparator,g=d.createState,f=d.equals,S=d.strict;if(g)return function(p,x){var X=g(),L=X.cache,O=L===void 0?m?new WeakMap:void 0:L,R=X.meta;return v(p,x,{cache:O,equals:f,meta:R,strict:S})};if(m)return function(p,x){return v(p,x,{cache:new WeakMap,equals:f,meta:void 0,strict:S})};var w={cache:void 0,equals:f,meta:void 0,strict:S};return function(p,x){return v(p,x,w)}}({circular:s,comparator:u,createState:r,equals:i?i(u):(t=u,function(d,m,v,g,f,S,w){return t(d,m,w)}),strict:h})}bt({strict:!0,createInternalComparator:function(){return At}}),bt({circular:!0,createInternalComparator:function(){return At}}),bt({circular:!0,createInternalComparator:function(){return At},strict:!0});class nr{constructor(t,e,s){l(this,"historySummaryVersion",3);l(this,"_callbacksManager",new Mi);l(this,"_params");this._conversationModel=t,this._extensionClient=e,this._chatFlagModel=s,this._params=ss(s.historySummaryParams),s.subscribe(i=>{this._params=ss(i.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}generateAbridgedHistoryText(t){let e=this._conversationModel.chatHistory;if(t){const n=e.findIndex(h=>h.request_id===t);n>=0?e=e.slice(0,n):console.warn(`generateAbridgedHistoryText. Request id ${t} not found in history.`)}const s=function(n){const h=[];let c=null;for(const u of n){if(!U(u))continue;const d=u;if(Zs(d)||(c&&(c.agentFinalResponse.trim()===""&&(c.wasInterrupted=!0),h.push(c)),c={userMessage:Ti(d),agentActionsSummary:{filesModified:new Set,filesCreated:new Set,filesDeleted:new Set,filesViewed:new Set,terminalCommands:new Set},agentFinalResponse:"",wasInterrupted:!1,continues:!1}),!c)continue;let m=!1;if(d.structured_output_nodes)for(const v of d.structured_output_nodes)v.type===H.TOOL_USE&&v.tool_use&&(m=!0,Ii(v.tool_use,c.agentActionsSummary));!m&&d.response_text&&(c.agentFinalResponse=d.response_text)}c&&(c.agentFinalResponse.trim()===""&&(c.continues=!0),h.push(c));for(const u of h)wi(u.agentActionsSummary);return h}(e);let i=0;const r=[];for(let n=s.length-1;n>=0;n--){const h=Ei(s[n],this._params.abridgedHistoryParams);if(i+h.length>this._params.abridgedHistoryParams.totalCharsLimit)break;r.push(h),i+=h.length}return r.reverse(),r.join(`
`)}clearStaleHistorySummaryNodes(t){const e=t.filter(s=>!Ht(s)||s.summaryVersion===this.historySummaryVersion);return ds(e,t)?t:e}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const e=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;e>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},e)}preprocessChatHistory(t){let e=t.concat();const s=e.findLastIndex(i=>Ht(i)&&i.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(s>0&&(console.info(`Using history summary node found at index ${s} with requestId: ${e[s].request_id}`),e=e.slice(s)),e=e.filter(i=>!Ht(i)||i.summaryVersion===this.historySummaryVersion)):e=e.filter(i=>!Ht(i)),ds(e,t)?t:e}async maybeAddHistorySummaryNode(t=!1,e){var E,M,xt,ut;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),i=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",i),i<=0)return!1;const{head:r,tail:n,headSizeChars:h,tailSizeChars:c}=function(A,_e,Ls,Hs){if(A.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const fe=[],zt=[];let Yt=0,Ge=0,je=0;for(let pe=A.length-1;pe>=0;pe--){const ye=A[pe],te=Ms(ye);Yt+te<_e||zt.length<Hs?(zt.push(ye),je+=te):(fe.push(ye),Ge+=te),Yt+=te}return Yt<Ls?(zt.push(...fe),{head:[],tail:zt.reverse(),headSizeChars:0,tailSizeChars:Yt}):{head:fe.reverse(),tail:zt.reverse(),headSizeChars:Ge,tailSizeChars:je}}(s,this._params.historyTailSizeCharsToExclude,i,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",h," tailSizeChars: ",c),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const u=Ie(s),d=Ie(r),m=Ie(n),v={totalHistoryCharCount:u,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:r.length,headLastRequestId:((E=r.at(-1))==null?void 0:E.request_id)??"",tailCharCount:m,tailExchangeCount:n.length,tailLastRequestId:((M=n.at(-1))==null?void 0:M.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let g=((xt=r.at(-1))==null?void 0:xt.response_nodes)??[],f=g.filter(A=>A.type===H.TOOL_USE);f.length>0&&(r.at(-1).response_nodes=g.filter(A=>A.type!==H.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const S=(ut=n[0])==null?void 0:ut.request_id,w=this.generateAbridgedHistoryText(S);console.info("Abridged history text size: %d characters.",w.length);const p=Date.now(),x=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),X=Date.now();if(console.info("Summary text size: %d characters.",x.responseText.length),v.summaryCharCount=x.responseText.length,v.summarizationDurationMs=X-p,v.isAborted=!!(e!=null&&e.aborted),this._extensionClient.reportAgentRequestEvent({eventName:Me.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:x.requestId??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:v}}),e==null?void 0:e.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!x.requestId||x.responseText.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;let L=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",`<summary>
${x.responseText}
</summary>`);L.includes("{abridged_history}")&&(L=L.replace("{abridged_history}",`<abridged_history>
${w}
</abridged_history>`));const O=this._params.summaryNodeResponseMessage,R={chatItemType:Et.historySummary,summaryVersion:this.historySummaryVersion,request_id:x.requestId,request_message:L,response_text:O,structured_output_nodes:[{id:f.map(A=>A.id).reduce((A,_e)=>Math.max(A,_e),-1)+1,type:H.RAW_RESPONSE,content:O},...f],status:k.success,seen_state:dt.seen,timestamp:new Date().toISOString()},I=this._conversationModel.chatHistory.findLastIndex(A=>A.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",I),this._conversationModel.insertChatItem(I,R),!0}}const qe="POP_ABORTED";class or{constructor(t=void 0){this.maxQueueSize=t,this.queue=new Be,this.waiters=new Be}push(t){this.maxQueueSize&&this.queue.length>=this.maxQueueSize&&this.waiters.length===0&&this.queue.shift(),this.waiters.length>0?this.waiters.shift()(t):this.queue.push(t)}async pop(t){if(t!=null&&t.aborted)throw new Error(qe);return this.queue.length>0?this.queue.shift():new Promise((e,s)=>{const i=()=>{const r=this.waiters.toArray().indexOf(e);r!==-1&&this.waiters.removeOne(r)};t==null||t.addEventListener("abort",()=>{i(),s(new Error(qe))},{once:!0}),this.waiters.push(e)})}get size(){return this.queue.length}clear(){this.queue.clear()}}const Ct=class Ct{constructor(){l(this,"_queues",new Map);l(this,"_activeConsumers",new Map)}static getInstance(){return Ct._instance||(Ct._instance=new Ct),Ct._instance}_getQueueForType(t){const e=this._queues.get(t);if(e)return e;const s=new or(500);return this._queues.set(t,s),s}addNotification(t){this._getQueueForType(t.type).push(t)}getStream(t){var i;this._activeConsumers.has(t)&&(console.error(`Consumer already exists for type ${t}. Only one consumer per type is allowed at any given time. Cancelling previous consumer.`),(i=this._activeConsumers.get(t))==null||i());const e=new AbortController,s=()=>{e.abort(),this._activeConsumers.delete(t)};return this._activeConsumers.set(t,s),{stream:(async function*(){try{const r=this._getQueueForType(t);for(;!e.signal.aborted;)try{const n=r.pop(e.signal),h=await n;if(e.signal.aborted)return;yield h}catch(n){if((n==null?void 0:n.message)!==qe)throw n}}catch(r){if(console.error("Error in stream for",t,r),!e.signal.aborted)throw r}finally{this._activeConsumers.delete(t)}}).call(this),cancel:s}}dispose(){this._activeConsumers.forEach(t=>t()),this._activeConsumers.clear(),this._queues.forEach(t=>t.clear()),this._queues.clear(),Ct._instance=void 0}};l(Ct,"_instance");let me=Ct;function kt(o){var t;return((t=o.extraData)==null?void 0:t.isAgentConversation)===!0}var G=(o=>(o[o.active=0]="active",o[o.inactive=1]="inactive",o))(G||{});const Ee="temp-fe";class z{constructor(t,e,s,i,r,n,h,c){l(this,"_state");l(this,"_subscribers",new Set);l(this,"_focusModel",new gi);l(this,"_onSendExchangeListeners",[]);l(this,"_onNewConversationListeners",[]);l(this,"_onHistoryDeleteListeners",[]);l(this,"_onBeforeChangeConversationListeners",[]);l(this,"_eventTracker");l(this,"_totalCharactersCacheThrottleMs",1e3);l(this,"_totalCharactersStore");l(this,"_chatHistorySummarizationModel");l(this,"_disposers",[]);l(this,"_subscribeToConversationUpdates",()=>{const t=me.getInstance(),{stream:e,cancel:s}=t.getStream("conversation_updated"),{stream:i,cancel:r}=t.getStream("exchange_updated");this._disposers.push(()=>{s(),r()}),(async()=>{for await(const n of e)this._handleConversationUpdated(n)})(),(async()=>{for await(const n of i){const{exchange:h}=n.data;h.request_id&&(this._maybeAppendExchange(h),this.updateExchangeById(h,h.request_id))}})()});l(this,"_handleConversationUpdated",t=>{if(t.data.conversationId!==this._state.id)return;const{exchanges:e}=t.data;e.forEach(this._maybeAppendExchange);const s=new Set(e.map(r=>r.request_id)),i=new Set;this._state.chatHistory.forEach(r=>{const n=r.request_id;n&&!s.has(n)&&i.add(n)}),e.forEach(r=>{r.request_id&&this.updateExchangeById(r,r.request_id)}),this._removeTurnsByRequestId(i)});l(this,"_maybeAppendExchange",t=>{t.request_id&&(this.exchangeWithRequestId(t.request_id)||this.addExchange({...t,request_message:t.request_message??"",status:k.success}))});l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([n,h])=>{if(h.requestId&&h.toolUseId){const{requestId:c,toolUseId:u}=Ye(n);return c===h.requestId&&u===h.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",n,"but object has ",xe(h)),[n,h]}return[n,{...h,...Ye(n)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=z.isEmpty(t);if(i&&r){const n=this._state.draftExchange;n&&(t.draftExchange=n)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(U)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(n=>n(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(n=>n())),!0});l(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});l(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});l(this,"setName",t=>{this.update({name:t})});l(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});l(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});l(this,"updateToolUseState",t=>{var s;const e=xe(t);this.update({toolUseStates:{...this._state.toolUseStates,[e]:t}}),(s=this._markToolUseDirty)==null||s.call(this,e)});l(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:D.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[xe({requestId:t,toolUseId:e})]||{phase:D.new});l(this,"getLastToolUseId",()=>{var s,i;const t=this.lastExchange;if(!t)return;const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===H.TOOL_USE))??[]).at(-1);return e?(i=e.tool_use)==null?void 0:i.tool_use_id:void 0});l(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:D.unknown};const e=function(i=[]){let r;for(const n of i){if(n.type===H.TOOL_USE)return n;n.type===H.TOOL_USE_START&&(r=n)}return r}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:D.unknown}});l(this,"addExchange",(t,e)=>{const s=this._state.chatHistory;let i,r;i=e===void 0?[...s,t]:e===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,e),t,...s.slice(e)],U(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Ts.unset,feedbackNote:""}}:void 0,this._markExchangeDirty&&t.request_id&&this._markExchangeDirty(t.request_id)),this.update({chatHistory:i,...r?{feedbackStates:r}:{},lastUrl:void 0})});l(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});l(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});l(this,"updateExchangeById",(t,e,s=!1)=>{var u;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;if(s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s)t.structured_output_nodes=function(d=[]){const m=Si(d);return m&&m.type===H.TOOL_USE?d.filter(v=>v.type!==H.TOOL_USE_START):d}([...i.structured_output_nodes??[],...t.structured_output_nodes??[]]);else if(r=i,n=t,Object.keys(n).every(d=>r[d]===n[d]))return!0;var r,n;t.stop_reason!==i.stop_reason&&i.stop_reason&&t.stop_reason===di.REASON_UNSPECIFIED&&(t.stop_reason=i.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const h=(u=(t.structured_output_nodes||[]).find(d=>d.type===H.MAIN_TEXT_FINISHED))==null?void 0:u.content;h&&h!==t.response_text&&(t.response_text=h);let c=this._state.isShareable||Ot({...i,...t});if(this.update({chatHistory:this.chatHistory.map(d=>d.request_id===e?{...d,...t}:d),isShareable:c}),this._markExchangeDirty){const d=t.request_id||e;this._markExchangeDirty(d)}return!0});l(this,"_clearMetadataAndDirtyTracking",(t,e)=>{this._extensionClient.clearMetadataFor({requestIds:t,toolUseIds:e})});l(this,"clearMessagesFromHistory",t=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._clearMetadataAndDirtyTracking(Array.from(t),e)});l(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});l(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(n=>n.request_id).filter(n=>n!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!e)}),this._clearMetadataAndDirtyTracking(i,r),s.forEach(n=>{this._onHistoryDeleteListeners.forEach(h=>h(n))})});l(this,"clearMessageFromHistory",t=>{const e=this.chatHistory.find(i=>i.request_id===t),s=e?this._collectToolUseIdsFromMessages([e]):[];this.update({chatHistory:this.chatHistory.filter(i=>i.request_id!==t)}),this._clearMetadataAndDirtyTracking([t],s)});l(this,"_collectToolUseIdsFromMessages",t=>{var s;const e=[];for(const i of t)if(U(i)&&i.structured_output_nodes)for(const r of i.structured_output_nodes)r.type===H.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&e.push(r.tool_use.tool_use_id);return e});l(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});l(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});l(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});l(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:k.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));l(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});l(this,"_removeTurnsByRequestId",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!!e.request_id&&!t.has(e.request_id))})});l(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);l(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});l(this,"markSeen",async t=>{t.request_id&&this.updateExchangeById({seen_state:t.seen_state||dt.unseen},t.request_id,!1)});l(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));l(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});l(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});l(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});l(this,"saveDraftExchange",(t,e)=>{var n,h,c;const s=t!==((n=this.draftExchange)==null?void 0:n.request_message),i=e!==((h=this.draftExchange)==null?void 0:h.rich_text_json_repr);if(!s&&!i)return;const r=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:r,status:k.draft}})});l(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});l(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!kt(this)){const i=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&i&&this.updateConversationTitle()}}).finally(()=>{var s;kt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Me.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});l(this,"cancelMessage",async()=>{var e;if(!this.canCancelMessage||!((e=this.lastExchange)!=null&&e.request_id))return;const t=this.lastExchange.request_id;this.updateExchangeById({status:k.cancelled},t),await this._extensionClient.cancelChatStream(t)});l(this,"sendInstructionExchange",async(t,e)=>{let s=`${Ee}-${crypto.randomUUID()}`;const i={status:k.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:dt.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const r of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});l(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});l(this,"checkAndGenerateAgentTitle",()=>{var e;if(!(!kt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Ae(s))).length===1&&!((e=this.extraData)!=null&&e.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});l(this,"sendSummaryExchange",()=>{const t={status:k.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Et.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});l(this,"generateCommitMessage",async()=>{let t=`${Ee}-${crypto.randomUUID()}`;const e={status:k.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:dt.unseen,chatItemType:Et.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});l(this,"sendExchange",async(t,e=!1,s)=>{var m,v,g;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let i=`${Ee}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(z.isNew(this._state)){const f=crypto.randomUUID(),S=this._state.id;try{await this._extensionClient.migrateConversationId(S,f)}catch(w){console.error("Failed to migrate conversation checkpoints:",w)}this._state={...this._state,id:f},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(f),this._subscribers.forEach(w=>w(this))}t=ms(t);let n={status:k.sent,request_id:i,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:r,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:dt.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(n),this._loadContextFromExchange(n),this._onSendExchangeListeners.forEach(f=>f(n)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),n=await this._addIdeStateNode(n),this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},i,!1);const h=Date.now();let c=!1,u=0;for await(const f of this.sendUserMessage(i,n,e,s)){if(((m=this.exchangeWithRequestId(i))==null?void 0:m.status)!==k.sent||!this.updateExchangeById(f,i,!0))return;if(i=f.request_id||i,!c&&kt(this)){const S=Date.now();u=S-h,this._extensionClient.reportAgentRequestEvent({eventName:Me.firstTokenReceived,conversationId:this.id,requestId:i,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:h,firstTokenReceivedTimestampMs:S,timeToFirstTokenMs:u}}}),c=!0}}const d=Date.now()-h;(g=this._eventTracker)==null||g.trackEvent(Ys.MESSAGE_SEND_TIMING,{requestId:i,timeToFirstTokenMs:u,timeToLastTokenMs:d,responseLength:(v=t==null?void 0:t.response_text)==null?void 0:v.length,chatHistoryLength:this.chatHistory.length,modelId:n.model_id}),this._chatHistorySummarizationModel.maybeScheduleSummarization(d)});l(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:k.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(ti.chatUseSuggestedQuestion)});l(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});l(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==k.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(r=>r.type===H.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},e);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,e,!0))return;e=r.request_id||e}});l(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{U(e)&&this._loadContextFromExchange(e)})});l(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});l(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{U(e)&&this._unloadContextFromExchange(e)})});l(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});l(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});l(this,"_jsonToStructuredRequest",t=>{const e=[],s=r=>{var h;const n=e.at(-1);if((n==null?void 0:n.type)===V.TEXT){const c=((h=n.text_node)==null?void 0:h.content)??"",u={...n,text_node:{content:c+r}};e[e.length-1]=u}else e.push({id:e.length,type:V.TEXT,text_node:{content:r}})},i=r=>{var n,h,c,u,d;if(r.type==="doc"||r.type==="paragraph")for(const m of r.content??[])i(m);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((n=r.attrs)==null?void 0:n.src)!="string")return void console.error("File source is not a string: ",(h=r.attrs)==null?void 0:h.src);if(r.attrs.isLoading)return;const m=(c=r.attrs)==null?void 0:c.title,v=Vs(m);bs(m)?e.push({id:e.length,type:V.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:v}}):e.push({id:e.length,type:V.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:m}})}else if(r.type==="mention"){const m=(u=r.attrs)==null?void 0:u.data;m&&Is(m)?e.push({id:e.length,type:V.TEXT,text_node:{content:ei(this._chatFlagModel,m.personality.type)}}):m&&si(m)?e.push({id:e.length,type:V.TEXT,text_node:{content:ii.getTaskOrchestratorPrompt(m.task)}}):s(`@\`${(m==null?void 0:m.name)??(m==null?void 0:m.id)}\``)}else if(r.type==="askMode"){const m=(d=r.attrs)==null?void 0:d.prompt;m&&e.push({id:e.length,type:V.TEXT,text_node:{content:m}})}};return i(t),e});l(this,"dispose",()=>{this._disposers.forEach(t=>t()),this._disposers=[]});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._rulesModel=r,this._markExchangeDirty=n,this._markToolUseDirty=h,this._state={...z.create(c!=null&&c.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new nr(this,t,e),this._subscribeToConversationUpdates()}get conversationId(){return this._state.id}insertChatItem(t,e){const s=[...this._state.chatHistory];s.splice(t,0,e),this.update({chatHistory:s})}_createTotalCharactersStore(){return pi(()=>{let t=0;const e=this._state.chatHistory;return this.convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}setEventTracker(t){this._eventTracker=t}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?$t.PROTOTYPER:$t.DEFAULT}catch(e){return console.error("Error determining persona type:",e),$t.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:$t.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(U);return e&&e.request_message?z.toSentenceCase(e.request_message):kt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===Lt}static isEmpty(t){var i;const e=t.chatHistory.filter(r=>U(r)),s=t.chatHistory.filter(r=>xs(r));return e.length===0&&s.length===0&&!((i=t.draftExchange)!=null&&i.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?z.lastMessageTimestamp(t):e==="lastInteractedAt"?z.lastInteractedAt(t):z.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(U))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!z.isEmpty(t)||z.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const r=i(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return z.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??$t.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return z.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return z.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){if(this.flags.enableModelRegistry&&kt(this._state))return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="file"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(U)??null}get lastExchange(){return this.chatHistory.findLast(U)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>U(t)&&t.status===k.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Ot(t)||Vt(t)||Ht(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const e=[];for(const s of t)if(Ot(s))e.push(us(s));else if(Ht(s))e.push(us(s));else if(Vt(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=ar(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(r)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===k.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const r=await this._addIdeStateNode(ms({...t,request_id:e,status:k.sent,timestamp:new Date().toISOString()}));for await(const n of this.sendUserMessage(e,r,!0))n.response_text&&(i+=n.response_text),n.request_id&&(s=n.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}_resolveUnresolvedToolUses(t,e,s){var d,m,v;if(t.length===0)return[t,e];const i=t[t.length-1],r=((d=i.response_nodes)==null?void 0:d.filter(g=>g.type===H.TOOL_USE))??[];if(r.length===0)return[t,e];const n=new Set;(m=e.structured_request_nodes)==null||m.forEach(g=>{var f;g.type===V.TOOL_RESULT&&((f=g.tool_result_node)!=null&&f.tool_use_id)&&n.add(g.tool_result_node.tool_use_id)});const h=r.filter(g=>{var S;const f=(S=g.tool_use)==null?void 0:S.tool_use_id;return f&&!n.has(f)});if(h.length===0)return[t,e];const c=h.map((g,f)=>{const S=g.tool_use.tool_use_id;return function(w,p,x,X){const L=Ci(p,w,X);let O;if(L!==void 0)O=L;else{let R;switch(p.phase){case D.runnable:R="Tool was cancelled before running.";break;case D.new:R="Cancelled by user.";break;case D.checkingSafety:R="Tool was cancelled during safety check.";break;case D.running:R="Tool was cancelled while running.";break;case D.cancelling:R="Tool cancellation was interrupted.";break;case D.cancelled:R="Cancelled by user.";break;case D.error:R="Tool execution failed.";break;case D.completed:R="Tool completed but result was unavailable.";break;case D.unknown:default:R="Cancelled by user.",p.phase!==D.unknown&&console.error(`Unexpected tool state phase: ${p.phase}`)}O={tool_use_id:w,content:R,is_error:!0}}return{id:x,type:V.TOOL_RESULT,tool_result_node:O}}(S,this.getToolUseState(i.request_id,S),ke(e.structured_request_nodes??[])+f+1,this._chatFlagModel.enableDebugFeatures)});if((v=e.structured_request_nodes)==null?void 0:v.some(g=>g.type===V.TOOL_RESULT))return[t,{...e,structured_request_nodes:[...e.structured_request_nodes??[],...c]}];{const g={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:k.success,hidden:!0};return s||this.addExchangeBeforeLast(g),[t.concat(this.convertHistoryToExchanges([g])),e]}}async*sendUserMessage(t,e,s,i){const r=this._chatFlagModel.enableParallelTools,n=await(r?((h,c,u)=>{const d=we.get(h)??yi();return we.has(h)||we.set(h,d),d.start(c,u)})("sendMessage",t):Promise.resolve({end:()=>{}}));try{for await(const h of this._sendUserMessage(t,e,s,i))yield h}finally{n.end(t)}}async*_sendUserMessage(t,e,s,i){var v;const r=this._specialContextInputModel.chatActiveContext;let n;if(e.chatHistory!==void 0)n=e.chatHistory;else{let g=this.successfulMessages;if(e.chatItemType===Et.summaryTitle){const f=g.findIndex(S=>S.chatItemType!==Et.agentOnboarding&&Ae(S));f!==-1&&(g=g.slice(f))}n=this.convertHistoryToExchanges(g)}this._chatFlagModel.enableParallelTools&&([n,e]=this._resolveUnresolvedToolUses(n,e,s));let h=this.personaType;if(e.structured_request_nodes){const g=e.structured_request_nodes.find(f=>f.type===V.CHANGE_PERSONALITY);g&&g.change_personality_node&&(h=g.change_personality_node.personality_type)}let c=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const g=le(this._rulesModel.getCachedRules());c=ze.filterRulesByContext(g,r.ruleFiles||[])}const u={text:e.request_message,chatHistory:n,silent:s,modelId:e.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(v=r.externalSources)==null?void 0:v.map(g=>g.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:h,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:i,rules:c},d=this._createStreamStateHandlers(t,u,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,u,{flags:this._chatFlagModel});for await(const g of m){let f=g;t=g.request_id||t;for(const S of d)f=S.handleChunk(f)??f;yield f}for(const g of d)yield*g.handleComplete();this.updateExchangeById({structured_request_nodes:e.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){let s=!1;const i=this.chatHistory.map(r=>r.request_id!==t||s?r:(s=!0,{...r,...e}));return s&&this._markExchangeDirty&&this._markExchangeDirty(t),this.update({chatHistory:i}),s}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(i=>i.type!==V.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(i){console.error("Failed to add IDE state to exchange:",i)}return e?(s=[...s,{id:ke(s)+1,type:V.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}}function ar(o,t){const e=(Vt(o),o.fromTimestamp),s=(Vt(o),o.toTimestamp),i=Vt(o)&&o.revertTarget!==void 0;return{id:t,type:V.CHECKPOINT_REF,checkpoint_ref_node:{request_id:o.request_id||"",from_timestamp:e,to_timestamp:s,source:i?Qs.CHECKPOINT_REVERT:void 0}}}function us(o){const t=(o.structured_output_nodes??[]).filter(e=>e.type===H.RAW_RESPONSE||e.type===H.TOOL_USE||e.type===H.TOOL_USE_START||e.type===H.THINKING).map(e=>e.type===H.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:H.TOOL_USE}:e);return{request_message:o.request_message,response_text:o.response_text??"",request_id:o.request_id||"",request_nodes:o.structured_request_nodes??[],response_nodes:t}}function ke(o){return o.length>0?Math.max(...o.map(t=>t.id)):0}function ms(o){var t;if(o.request_message.length>0&&!((t=o.structured_request_nodes)!=null&&t.some(e=>e.type===V.TEXT))){let e=o.structured_request_nodes??[];return e=[...e,{id:ke(e)+1,type:V.TEXT,text_node:{content:o.request_message}}],{...o,structured_request_nodes:e}}return o}class lr{constructor(t=!0,e=setTimeout){l(this,"_notify",new Set);l(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});l(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});l(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});l(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const r={timeout:t,notify:e,once:s,date:i};return this._notify.add(r),this._schedule(r),()=>{this._clearTimeout(r),this._notify.delete(r)}}}class hr{constructor(t=0,e=0,s=new lr,i=ct("busy"),r=ct(!1)){l(this,"unsubNotify");l(this,"unsubMessage");l(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});l(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=r,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var cr=Xt("<svg><!></svg>"),dr=Xt("<svg><!></svg>"),he=(o=>(o.send="send",o.addTask="addTask",o))(he||{});const ur={id:"send",label:"Send to Agent",icon:function(o,t){const e=vs(t,["children","$$slots","$$events","$$legacy"]);var s=cr();Cs(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=Ss(s);Es(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3L266 249.3c3.4.4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6c-9.4 1.2-17.6 6.9-22 15.3l-63 121.2c-17.4 33.5 17 70.2 51.6 55.1l435.2-190.9c25.5-11.2 25.5-47.4 0-58.6z"/>',!0),Jt(o,s)},description:"Send message to agent"},mr={id:"addTask",label:"Add Task",icon:function(o,t){const e=vs(t,["children","$$slots","$$events","$$legacy"]);var s=dr();Cs(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=Ss(s);Es(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-24-168v-64h-64c-13.3 0-24-10.7-24-24s10.7-24 24-24h64v-64c0-13.3 10.7-24 24-24s24 10.7 24 24v64h64c13.3 0 24 10.7 24 24s-10.7 24-24 24h-64v64c0 13.3-10.7 24-24 24s-24-10.7-24-24"/>',!0),Jt(o,s)},description:"Add task with the message content"},$r=[ur,mr];class gr{constructor(){l(this,"_mode",ct(he.send));l(this,"_currentMode",he.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(he).includes(t)&&this._mode.set(t)}}class Zt{constructor(){l(this,"_dirtyItems",new Set);l(this,"markDirty",t=>{this._dirtyItems.add(t)});l(this,"markClean",t=>{this._dirtyItems.delete(t)});l(this,"isDirty",t=>this._dirtyItems.has(t));l(this,"getDirtyItems",t=>{const e=Array.from(this._dirtyItems);return t?e.slice(0,t):e});l(this,"cleanup",t=>{const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this._dirtyItems.delete(s)});l(this,"clear",()=>{this._dirtyItems.clear()});l(this,"getAllDirtyItems",()=>Array.from(this._dirtyItems));l(this,"getStats",()=>({dirtyCount:this._dirtyItems.size}))}}class _r{constructor(t,e,s=new Zt){this.historyController=t,this.toolStateController=e,this.dirtyCache=s}async dehydrateState(t){const[e,s]=await Promise.all([this.historyController.dehydrateState(t),this.toolStateController.dehydrateState(t)]);return{...t,chatHistory:e.chatHistory,toolUseStates:s.toolUseStates}}async hydrateState(t){return await Promise.all([this.historyController.hydrateState(t),this.toolStateController.hydrateState(t)]),t}get dirty(){return this.dirtyCache}}class fr{constructor(t,e){l(this,"exchangeDirtyCache",new Zt);l(this,"historyDirtyCache",new Zt);this.extensionClient=t,this.flags=e}get dirty(){return this.exchangeDirtyCache}get historyDirty(){return this.historyDirtyCache}exchangeToStoredExchange(t,e){var i;const s=t.request_id||(((i=crypto==null?void 0:crypto.randomUUID)==null?void 0:i.call(crypto))??`fallback-${Date.now()}`);return{uuid:s,conversationId:e,request_message:t.request_message,response_text:t.response_text||"",request_id:s,request_nodes:t.structured_request_nodes,response_nodes:t.structured_output_nodes,status:t.status===k.success?"success":t.status===k.failed?"failed":"sent",timestamp:t.timestamp||new Date().toISOString(),seen_state:t.seen_state===dt.seen?"seen":"unseen"}}exchangeToPointer(t){var s;const e=t.request_id||(((s=crypto==null?void 0:crypto.randomUUID)==null?void 0:s.call(crypto))??`fallback-${Date.now()}`);return{chatItemType:Et.exchangePointer,exchangeUuid:e,timestamp:t.timestamp,request_message:t.request_message,status:t.status,seen_state:t.seen_state}}storedExchangeToExchange(t){return{request_message:t.request_message,response_text:t.response_text,request_id:t.request_id,structured_request_nodes:t.request_nodes,structured_output_nodes:t.response_nodes,timestamp:t.timestamp,status:t.status==="success"?k.success:t.status==="failed"?k.failed:k.sent,seen_state:t.seen_state==="seen"?dt.seen:dt.unseen}}pointerToFallbackExchange(t){return{request_message:t.request_message||"",response_text:"",request_id:t.exchangeUuid,timestamp:t.timestamp,status:t.status||k.sent,seen_state:t.seen_state||dt.unseen}}async dehydrateState(t){if(!this.flags.enableExchangeStorage&&!this.flags.enableEnhancedDehydrationMode)return t;const e=!t.chatHistory||t.chatHistory.length===0,s=t.chatHistory.filter(n=>!!(U(n)&&n.request_id&&this.exchangeDirtyCache.isDirty(n.request_id))),i=!e&&(this.historyDirty.isDirty(t.id)||s.length>0),r={...t};if(this.historyDirty.markClean(t.id),s.forEach(n=>{this.exchangeDirtyCache.markClean(n.request_id)}),this.flags.enableExchangeStorage){if(i&&s.length>0){const n=s.map(h=>this.exchangeToStoredExchange(h,t.id));await this.extensionClient.saveExchanges(t.id,n)}r.chatHistory=t.chatHistory.map(n=>U(n)?this.exchangeToPointer(n):n)}return this.flags.enableEnhancedDehydrationMode&&(i&&await this.extensionClient.saveConversationHistory(t.id,t.chatHistory),r.chatHistory=[]),r}async hydrateState(t){let e=[];e=t.chatHistory&&t.chatHistory.length>0?t.chatHistory:await this.extensionClient.loadConversationHistory(t.id);const s=[];e.forEach((r,n)=>{xs(r)&&s.push({uuid:r.exchangeUuid,index:n})});let i=new Map;if(s.length>0){const r=s.map(h=>h.uuid),n=await this.extensionClient.loadExchanges(t.id,r);i=new Map(n.map(h=>[h.uuid,h]))}for(const{uuid:r,index:n}of s){const h=i.get(r);e[n]=h?this.storedExchangeToExchange(h):this.pointerToFallbackExchange(e[n])}return t.chatHistory=e,{...t,chatHistory:e}}}class pr{constructor(t,e){l(this,"_dirtyCache",new Zt);this.extensionClient=t,this.flags=e}get dirty(){return this._dirtyCache}async dehydrateState(t){if(!this.flags.enableToolUseStateStorage)return t;const e=Object.keys(t.toolUseStates||{}),s=new Set(e.filter(r=>this._dirtyCache.isDirty(r)));if(s.size===0)return{...t,toolUseStates:{}};const i=Object.fromEntries(Object.entries(t.toolUseStates||{}).filter(([r])=>s.has(r)));return s.forEach(this._dirtyCache.markClean),await this.extensionClient.saveToolUseStates(t.id,i),{...t,toolUseStates:{}}}async hydrateState(t){try{const e=await this.extensionClient.loadConversationToolUseStates(t.id);t.toolUseStates={...t.toolUseStates,...e}}catch(e){console.error("Failed to hydrate tool use states:",e)}return t}}class yr{constructor(t,e,s,i,r=5){this.host=t,this.conversationController=e,this.historyController=s,this.toolStateController=i,this.maxConversationsPerIteration=r}getState(){const t=this.host.getState();if(t)for(const e of Object.values(t.conversations))this.markDirtyDeep(e);return t}markDirtyDeep(t){const e=this.conversationController;if(t.chatHistory.length>0||Object.keys(t.toolUseStates||{}).length>0){e.dirty.markDirty(t.id),t.chatHistory.length>0&&this.historyController.historyDirty.markDirty(t.id);for(const s of t.chatHistory)U(s)&&s.request_id&&this.historyController.dirty.markDirty(s.request_id);for(const s of Object.keys(t.toolUseStates||{}))this.toolStateController.dirty.markDirty(s)}}async setState(t){const e=await this.dehydrateState(t);return this.host.setState(e),t}async dehydrateState(t){const e=Object.entries(t.conversations),s=this.selectConversationsForDehydration(e,void 0,this.maxConversationsPerIteration),i={};for(const[r,n]of e)try{const h=await this.conversationController.dehydrateState(n);s.includes(r)&&this.conversationController.dirty.markClean(r),i[r]=h}catch(h){console.error("Failed to dehydrate conversation:",h),i[r]=n}return{...t,conversations:i}}async hydrateState(t,e){if(!(e!=null&&e.targetIds)||e.targetIds.length===0)return t;for(const s of e.targetIds){const i=t.conversations[s];i&&await this.conversationController.hydrateState(i)}return t}selectConversationsForDehydration(t,e,s){const i=s??this.maxConversationsPerIteration,r=[],n=[];for(const[h]of t){if(r.length+n.length>=i)break;this.conversationController.dirty.isDirty(h)&&(h===e?r.unshift(h):n.push(h))}return[...r,...n]}get conversationPersistenceController(){return this.conversationController}}class vr{static create(t,e,s){const i=new fr(t,e),r=new pr(t,e),n=new _r(i,r,new Zt);return new yr(s,n,i,r)}}class Cr{constructor(t,e){l(this,"_debouncedSetState");this.chatController=t,this._debouncedSetState=ui(async s=>{try{await this.chatController.setState(s)}catch(i){const r=ws();r==null||r.warn("Debounced setState failed:",i)}},(e==null?void 0:e.wait)??5e3,{maxWait:(e==null?void 0:e.maxWait)??3e4})}get historyController(){return this.chatController.historyController}get toolStateController(){return this.chatController.toolStateController}getState(){return this.chatController.getState()}async setState(t,e){const s=this._mergePartialState(t,e==null?void 0:e.currentConversationId);return e!=null&&e.immediate?(this._debouncedSetState(s),this._debouncedSetState.flush(),s):(this._debouncedSetState(s),s)}async loadConversation(t,e){return await this.chatController.hydrateState(t,{targetIds:[e.conversationId]})}get dirty(){return{exchanges:this.historyController.dirty,toolUses:this.toolStateController.dirty,conversations:this.chatController.conversationController.dirty}}_mergePartialState(t,e){return{currentConversationId:e,agentExecutionMode:Fs.manual,isPanelCollapsed:!1,displayedAnnouncements:[],...t,conversations:t.conversations??{}}}}const ae=ct("idle");var Fs=(o=>(o.manual="manual",o.auto="auto",o))(Fs||{});class De{constructor(t,e,s,i={}){l(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});l(this,"extensionClient");l(this,"_chatFlagsModel");l(this,"_currConversationModel");l(this,"_chatModeModel");l(this,"_sendModeModel");l(this,"_flagsLoaded",ct(!1));l(this,"_eventTracker");l(this,"_rulesModel");l(this,"_persistenceController");l(this,"_messageBroker");l(this,"_disposers",[]);l(this,"subscribers",new Set);l(this,"idleMessageModel",new hr);l(this,"isPanelCollapsed");l(this,"agentExecutionMode");l(this,"sortConversationsBy");l(this,"displayedAnnouncements");l(this,"bulkDeletePromptDismissed");l(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??js,bigSyncThreshold:t.bigSyncThreshold??Gs,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??$s,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??zs.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},agentChatModel:t.agentChatModel??"",enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,enableEnhancedDehydrationMode:t.enableEnhancedDehydrationMode??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{},modelInfoRegistry:t.modelInfoRegistry??{},subscriptionBannerDismissibility:t.subscriptionBannerDismissibility??Ps.OFF,showThinkingSummary:t.showThinkingSummary??!1}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"initializeSync",t=>{if(this._state={...this._state,...this._persistenceController.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Se&&this.currentConversationId!==Se||(delete this._state.conversations[Se],this.setCurrentConversationToWelcome())),this._disposers.push(this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs})),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity())});l(this,"_subscribeToChatNotifications",()=>{const t=me.getInstance(),{stream:e,cancel:s}=t.getStream("conversation_id_changed");this._disposers.push(()=>{s()}),(async()=>{for await(const i of e){const r=i.data.conversationId;this._state.conversations[r]!==void 0||(this._state.conversations[r]={...z.create({id:r})}),await this.setCurrentConversation(r)}})()});l(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(r=>Ot(r));t[e]={...s,isShareable:i}}this._state.conversations=t});l(this,"save",()=>{this._persistenceController.setState(this._state,{currentConversationId:this.currentConversationId})});l(this,"notifyAndSave",()=>{this.notifySubscribers(),this.save()});l(this,"saveImmediate",()=>{this._persistenceController.setState(this._state,{currentConversationId:this.currentConversationId,immediate:!0})});l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});l(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));l(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Et.educateFeatures,request_id:crypto.randomUUID(),seen_state:dt.seen})});l(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});l(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let i;t===void 0&&(t=Lt);const r=this._state.conversations[t];r?i=(await this._persistenceController.loadConversation({conversations:{[t]:r},currentConversationId:this.currentConversationId,agentExecutionMode:this._state.agentExecutionMode,isPanelCollapsed:this._state.isPanelCollapsed,displayedAnnouncements:this._state.displayedAnnouncements},{conversationId:t})).conversations[t]:i=z.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===Lt&&(i.id=Lt),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid);const n=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!n,e),this._state.currentConversationId=i.id,this._state.conversations[i.id]=i,this.notifyAndSave(),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});l(this,"saveConversation",async(t,e)=>{this._persistenceController.dirty.conversations.markDirty(t.id),this._state.conversations[t.id]=t,e&&delete this._state.conversations[Lt],this.notifyAndSave(),this._state.currentConversationId=t.id,this.notifyAndSave()});l(this,"markExchangeDirty",t=>{this._persistenceController.dirty.exchanges.markDirty(t)});l(this,"markToolUseDirty",t=>{this._persistenceController.dirty.toolUses.markDirty(t)});l(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});l(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.notifyAndSave()});l(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;ae.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((h,c)=>(Ot(c)&&h.push({request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}),h),[]);if(i.length===0)throw new Error("No chat history to share");const r=z.getDisplayName(e),n=await this.extensionClient.saveChat(t,i,r);if(n.data){let h=n.data.url;return this._state={...this._state,conversations:{...this._state.conversations,[t]:{...e,lastUrl:h}}},this.notifyAndSave(),h}throw new Error("Failed to create URL")});l(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void ae.set("idle");navigator.clipboard.writeText(e),ae.set("copied")}catch{ae.set("failed")}});l(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const r=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${r>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const n=new Set(t);await this.deleteConversationIds(n)}if(s.length>0&&i)for(const n of s)try{await i.deleteAgent(n,!0)}catch(h){console.error(`Failed to delete remote agent ${n}:`,h)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});l(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});l(this,"deleteConversationIds",async t=>{var i,r,n;const e=[],s=[];for(const h of t){const c=((i=this._state.conversations[h])==null?void 0:i.requestIds)??[];e.push(...c);const u=((r=this._state.conversations[h])==null?void 0:r.toolUseStates)??{};for(const m of Object.keys(u)){const{toolUseId:v}=u[m];v&&s.push(v)}const d=this._state.conversations[h];if(d){for(const m of d.chatHistory)if(U(m)&&m.structured_output_nodes)for(const v of m.structured_output_nodes)v.type===H.TOOL_USE&&((n=v.tool_use)!=null&&n.tool_use_id)&&s.push(v.tool_use.tool_use_id)}}for(const h of Object.values(this._state.conversations))if(t.has(h.id)){for(const u of h.chatHistory)U(u)&&this.deleteImagesInExchange(u);const c=h.draftExchange;c&&this.deleteImagesInExchange(c)}for(const h of t){try{await this.extensionClient.deleteConversationExchanges(h)}catch(c){console.error(`Failed to delete exchanges for conversation ${h}:`,c)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(h)}catch(c){console.error(`Failed to delete tool use states for conversation ${h}:`,c)}}this._persistenceController.dirty.conversations.cleanup(t),this._state={...this._state,conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([h])=>!t.has(h)))},this.notifyAndSave(),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});l(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});l(this,"findImagesInJson",t=>{const e=[],s=i=>{var r,n;if(i.type==="file"&&((r=i.attrs)!=null&&r.src)){const h=(n=i.attrs)==null?void 0:n.src;bs(h)&&e.push(i.attrs.src)}else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const h of i.content)s(h)};return s(t),e});l(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===V.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));l(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this._state={...this._state,conversations:{...this._state.conversations,[t]:s}},this.notifyAndSave(),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});l(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this._state={...this._state,conversations:{...this._state.conversations,[t]:s}},this.notifyAndSave(),t===this.currentConversationId&&this._currConversationModel.setName(e)});l(this,"smartPaste",(t,e,s,i)=>{const r=this._currConversationModel.historyTo(t,!0).filter(n=>Ot(n)).map(n=>({request_message:n.request_message,response_text:n.response_text||"",request_id:n.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:r,targetFile:s??void 0,options:i})});l(this,"saveImage",async t=>await this.extensionClient.saveImage(t));l(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));l(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));l(this,"renderImage",async t=>await this.extensionClient.loadImage(t));l(this,"dispose",()=>{this._disposers.forEach(t=>t()),this._disposers=[]});this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new Ns(i.initialFlags),this._messageBroker=new Bs(this._host),this._rulesModel=new ri(this._messageBroker,!1),this.extensionClient=new ni(this._host,this._asyncMsgSender,this._chatFlagsModel),this._messageBroker.registerConsumer(this._rulesModel),this._currConversationModel=new z(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation,this._rulesModel,this.markExchangeDirty,this.markToolUseDirty,{forceAgentConversation:i.forceAgentConversation}),this._disposers.push(this._currConversationModel.dispose),this._sendModeModel=new gr,this._persistenceController=function(n,h,c,u){const d=vr.create(n,h,c);return new Cr(d,u)}(this.extensionClient,this._chatFlagsModel,this._host,i.debounceConfig),this.initializeSync(i.initialConversation);const r=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ct(r),this.agentExecutionMode=ct(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ct(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ct(this._state.displayedAnnouncements??[]),this.bulkDeletePromptDismissed=ct(this._state.bulkDeletePromptDismissed),this.bulkDeletePromptDismissed.subscribe(n=>{this._state.bulkDeletePromptDismissed!==n&&(this._state.bulkDeletePromptDismissed=n,this.save())}),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t;const e={extensionClient:this.extensionClient,chatModeType:t.chatModeType,currentSendMode:this._sendModeModel.mode,agentExecutionMode:this.agentExecutionMode};this._eventTracker=new mi(e),this._currConversationModel.setEventTracker(this._eventTracker)}get flagsLoaded(){return this._flagsLoaded}get eventTracker(){return this._eventTracker}get rulesModel(){return this._rulesModel}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;if(e){const s=(t==null?void 0:t.id)||this.currentConversationId,i=await this._persistenceController.loadConversation({conversations:this._state.conversations,currentConversationId:s,agentExecutionMode:this._state.agentExecutionMode,isPanelCollapsed:this._state.isPanelCollapsed,displayedAnnouncements:this._state.displayedAnnouncements},{conversationId:e});this._state.conversations=i.conversations}if(this._subscribeToChatNotifications(),t)await this.setCurrentConversation(t.id);else if(this.currentConversationId){const s=this.conversations[this.currentConversationId];s&&z.isValid(s)?await this.setCurrentConversation(this.currentConversationId):(s&&delete this._state.conversations[this.currentConversationId],await this.setCurrentConversation(void 0))}else await this.setCurrentConversation(void 0)}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let r=Object.values(this._state.conversations);return s&&(r=r.filter(s)),r.sort((n,h)=>{const c=z.getTime(n,i).getTime(),u=z.getTime(h,i).getTime();return e==="asc"?c-u:u-c})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===wt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}l(De,"key","chatModel"),l(De,"NEW_AGENT_KEY",Lt);function Gr(){return Ws(De.key).currentConversationModel}const Dt=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,gs=new Set,Le=typeof process=="object"&&process?process:{},qs=(o,t,e,s)=>{typeof Le.emitWarning=="function"?Le.emitWarning(o,t,e,s):console.error(`[${e}] ${t}: ${o}`)};let ge=globalThis.AbortController,_s=globalThis.AbortSignal;var fs;if(ge===void 0){_s=class{constructor(){l(this,"onabort");l(this,"_onabort",[]);l(this,"reason");l(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},ge=class{constructor(){l(this,"signal",new _s);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const r of this.signal._onabort)r(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let o=((fs=Le.env)==null?void 0:fs.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{o&&(o=!1,qs("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ft=o=>o&&o===Math.floor(o)&&o>0&&isFinite(o),ks=o=>ft(o)?o<=Math.pow(2,8)?Uint8Array:o<=Math.pow(2,16)?Uint16Array:o<=Math.pow(2,32)?Uint32Array:o<=Number.MAX_SAFE_INTEGER?ce:null:null;class ce extends Array{constructor(t){super(t),this.fill(0)}}var Rt;const It=class It{constructor(t,e){l(this,"heap");l(this,"length");if(!a(It,Rt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ks(t);if(!e)return[];C(It,Rt,!0);const s=new It(t,e);return C(It,Rt,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};Rt=new WeakMap,T(It,Rt,!1);let He=It;var ps,ys,st,J,it,rt,Ut,Nt,$,nt,P,q,b,Q,Y,Z,j,ot,W,at,lt,tt,ht,St,K,_,Re,Tt,gt,Qt,et,Ds,Mt,Pt,Kt,pt,yt,Ue,de,ue,F,Ne,Bt,vt,Pe;const $e=class $e{constructor(t){T(this,_);T(this,st);T(this,J);T(this,it);T(this,rt);T(this,Ut);T(this,Nt);l(this,"ttl");l(this,"ttlResolution");l(this,"ttlAutopurge");l(this,"updateAgeOnGet");l(this,"updateAgeOnHas");l(this,"allowStale");l(this,"noDisposeOnSet");l(this,"noUpdateTTL");l(this,"maxEntrySize");l(this,"sizeCalculation");l(this,"noDeleteOnFetchRejection");l(this,"noDeleteOnStaleGet");l(this,"allowStaleOnFetchAbort");l(this,"allowStaleOnFetchRejection");l(this,"ignoreFetchAbort");T(this,$);T(this,nt);T(this,P);T(this,q);T(this,b);T(this,Q);T(this,Y);T(this,Z);T(this,j);T(this,ot);T(this,W);T(this,at);T(this,lt);T(this,tt);T(this,ht);T(this,St);T(this,K);T(this,Tt,()=>{});T(this,gt,()=>{});T(this,Qt,()=>{});T(this,et,()=>!1);T(this,Mt,t=>{});T(this,Pt,(t,e,s)=>{});T(this,Kt,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});l(this,ps,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:r,updateAgeOnGet:n,updateAgeOnHas:h,allowStale:c,dispose:u,disposeAfter:d,noDisposeOnSet:m,noUpdateTTL:v,maxSize:g=0,maxEntrySize:f=0,sizeCalculation:S,fetchMethod:w,memoMethod:p,noDeleteOnFetchRejection:x,noDeleteOnStaleGet:X,allowStaleOnFetchRejection:L,allowStaleOnFetchAbort:O,ignoreFetchAbort:R}=t;if(e!==0&&!ft(e))throw new TypeError("max option must be a nonnegative integer");const I=e?ks(e):Array;if(!I)throw new Error("invalid max value: "+e);if(C(this,st,e),C(this,J,g),this.maxEntrySize=f||a(this,J),this.sizeCalculation=S,this.sizeCalculation){if(!a(this,J)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(p!==void 0&&typeof p!="function")throw new TypeError("memoMethod must be a function if defined");if(C(this,Nt,p),w!==void 0&&typeof w!="function")throw new TypeError("fetchMethod must be a function if specified");if(C(this,Ut,w),C(this,St,!!w),C(this,P,new Map),C(this,q,new Array(e).fill(void 0)),C(this,b,new Array(e).fill(void 0)),C(this,Q,new I(e)),C(this,Y,new I(e)),C(this,Z,0),C(this,j,0),C(this,ot,He.create(e)),C(this,$,0),C(this,nt,0),typeof u=="function"&&C(this,it,u),typeof d=="function"?(C(this,rt,d),C(this,W,[])):(C(this,rt,void 0),C(this,W,void 0)),C(this,ht,!!a(this,it)),C(this,K,!!a(this,rt)),this.noDisposeOnSet=!!m,this.noUpdateTTL=!!v,this.noDeleteOnFetchRejection=!!x,this.allowStaleOnFetchRejection=!!L,this.allowStaleOnFetchAbort=!!O,this.ignoreFetchAbort=!!R,this.maxEntrySize!==0){if(a(this,J)!==0&&!ft(a(this,J)))throw new TypeError("maxSize must be a positive integer if specified");if(!ft(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");y(this,_,Ds).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!X,this.updateAgeOnGet=!!n,this.updateAgeOnHas=!!h,this.ttlResolution=ft(i)||i===0?i:1,this.ttlAutopurge=!!r,this.ttl=s||0,this.ttl){if(!ft(this.ttl))throw new TypeError("ttl must be a positive integer if specified");y(this,_,Re).call(this)}if(a(this,st)===0&&this.ttl===0&&a(this,J)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!a(this,st)&&!a(this,J)){const E="LRU_CACHE_UNBOUNDED";(M=>!gs.has(M))(E)&&(gs.add(E),qs("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",E,$e))}}static unsafeExposeInternals(t){return{starts:a(t,lt),ttls:a(t,tt),sizes:a(t,at),keyMap:a(t,P),keyList:a(t,q),valList:a(t,b),next:a(t,Q),prev:a(t,Y),get head(){return a(t,Z)},get tail(){return a(t,j)},free:a(t,ot),isBackgroundFetch:e=>{var s;return y(s=t,_,F).call(s,e)},backgroundFetch:(e,s,i,r)=>{var n;return y(n=t,_,ue).call(n,e,s,i,r)},moveToTail:e=>{var s;return y(s=t,_,Bt).call(s,e)},indexes:e=>{var s;return y(s=t,_,pt).call(s,e)},rindexes:e=>{var s;return y(s=t,_,yt).call(s,e)},isStale:e=>{var s;return a(s=t,et).call(s,e)}}}get max(){return a(this,st)}get maxSize(){return a(this,J)}get calculatedSize(){return a(this,nt)}get size(){return a(this,$)}get fetchMethod(){return a(this,Ut)}get memoMethod(){return a(this,Nt)}get dispose(){return a(this,it)}get disposeAfter(){return a(this,rt)}getRemainingTTL(t){return a(this,P).has(t)?1/0:0}*entries(){for(const t of y(this,_,pt).call(this))a(this,b)[t]===void 0||a(this,q)[t]===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield[a(this,q)[t],a(this,b)[t]])}*rentries(){for(const t of y(this,_,yt).call(this))a(this,b)[t]===void 0||a(this,q)[t]===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield[a(this,q)[t],a(this,b)[t]])}*keys(){for(const t of y(this,_,pt).call(this)){const e=a(this,q)[t];e===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield e)}}*rkeys(){for(const t of y(this,_,yt).call(this)){const e=a(this,q)[t];e===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield e)}}*values(){for(const t of y(this,_,pt).call(this))a(this,b)[t]===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield a(this,b)[t])}*rvalues(){for(const t of y(this,_,yt).call(this))a(this,b)[t]===void 0||y(this,_,F).call(this,a(this,b)[t])||(yield a(this,b)[t])}[(ys=Symbol.iterator,ps=Symbol.toStringTag,ys)](){return this.entries()}find(t,e={}){for(const s of y(this,_,pt).call(this)){const i=a(this,b)[s],r=y(this,_,F).call(this,i)?i.__staleWhileFetching:i;if(r!==void 0&&t(r,a(this,q)[s],this))return this.get(a(this,q)[s],e)}}forEach(t,e=this){for(const s of y(this,_,pt).call(this)){const i=a(this,b)[s],r=y(this,_,F).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,a(this,q)[s],this)}}rforEach(t,e=this){for(const s of y(this,_,yt).call(this)){const i=a(this,b)[s],r=y(this,_,F).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,a(this,q)[s],this)}}purgeStale(){let t=!1;for(const e of y(this,_,yt).call(this,{allowStale:!0}))a(this,et).call(this,e)&&(y(this,_,vt).call(this,a(this,q)[e],"expire"),t=!0);return t}info(t){const e=a(this,P).get(t);if(e===void 0)return;const s=a(this,b)[e],i=y(this,_,F).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const r={value:i};if(a(this,tt)&&a(this,lt)){const n=a(this,tt)[e],h=a(this,lt)[e];if(n&&h){const c=n-(Dt.now()-h);r.ttl=c,r.start=Date.now()}}return a(this,at)&&(r.size=a(this,at)[e]),r}dump(){const t=[];for(const e of y(this,_,pt).call(this,{allowStale:!0})){const s=a(this,q)[e],i=a(this,b)[e],r=y(this,_,F).call(this,i)?i.__staleWhileFetching:i;if(r===void 0||s===void 0)continue;const n={value:r};if(a(this,tt)&&a(this,lt)){n.ttl=a(this,tt)[e];const h=Dt.now()-a(this,lt)[e];n.start=Math.floor(Date.now()-h)}a(this,at)&&(n.size=a(this,at)[e]),t.unshift([s,n])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=Dt.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var v,g,f,S,w;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:r,noDisposeOnSet:n=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:c}=s;let{noUpdateTTL:u=this.noUpdateTTL}=s;const d=a(this,Kt).call(this,t,e,s.size||0,h);if(this.maxEntrySize&&d>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),y(this,_,vt).call(this,t,"set"),this;let m=a(this,$)===0?void 0:a(this,P).get(t);if(m===void 0)m=a(this,$)===0?a(this,j):a(this,ot).length!==0?a(this,ot).pop():a(this,$)===a(this,st)?y(this,_,de).call(this,!1):a(this,$),a(this,q)[m]=t,a(this,b)[m]=e,a(this,P).set(t,m),a(this,Q)[a(this,j)]=m,a(this,Y)[m]=a(this,j),C(this,j,m),ee(this,$)._++,a(this,Pt).call(this,m,d,c),c&&(c.set="add"),u=!1;else{y(this,_,Bt).call(this,m);const p=a(this,b)[m];if(e!==p){if(a(this,St)&&y(this,_,F).call(this,p)){p.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:x}=p;x===void 0||n||(a(this,ht)&&((v=a(this,it))==null||v.call(this,x,t,"set")),a(this,K)&&((g=a(this,W))==null||g.push([x,t,"set"])))}else n||(a(this,ht)&&((f=a(this,it))==null||f.call(this,p,t,"set")),a(this,K)&&((S=a(this,W))==null||S.push([p,t,"set"])));if(a(this,Mt).call(this,m),a(this,Pt).call(this,m,d,c),a(this,b)[m]=e,c){c.set="replace";const x=p&&y(this,_,F).call(this,p)?p.__staleWhileFetching:p;x!==void 0&&(c.oldValue=x)}}else c&&(c.set="update")}if(i===0||a(this,tt)||y(this,_,Re).call(this),a(this,tt)&&(u||a(this,Qt).call(this,m,i,r),c&&a(this,gt).call(this,c,m)),!n&&a(this,K)&&a(this,W)){const p=a(this,W);let x;for(;x=p==null?void 0:p.shift();)(w=a(this,rt))==null||w.call(this,...x)}return this}pop(){var t;try{for(;a(this,$);){const e=a(this,b)[a(this,Z)];if(y(this,_,de).call(this,!0),y(this,_,F).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(a(this,K)&&a(this,W)){const e=a(this,W);let s;for(;s=e==null?void 0:e.shift();)(t=a(this,rt))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,r=a(this,P).get(t);if(r!==void 0){const n=a(this,b)[r];if(y(this,_,F).call(this,n)&&n.__staleWhileFetching===void 0)return!1;if(!a(this,et).call(this,r))return s&&a(this,Tt).call(this,r),i&&(i.has="hit",a(this,gt).call(this,i,r)),!0;i&&(i.has="stale",a(this,gt).call(this,i,r))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=a(this,P).get(t);if(i===void 0||!s&&a(this,et).call(this,i))return;const r=a(this,b)[i];return y(this,_,F).call(this,r)?r.__staleWhileFetching:r}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,ttl:n=this.ttl,noDisposeOnSet:h=this.noDisposeOnSet,size:c=0,sizeCalculation:u=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:m=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:v=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:f=this.allowStaleOnFetchAbort,context:S,forceRefresh:w=!1,status:p,signal:x}=e;if(!a(this,St))return p&&(p.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,status:p});const X={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,ttl:n,noDisposeOnSet:h,size:c,sizeCalculation:u,noUpdateTTL:d,noDeleteOnFetchRejection:m,allowStaleOnFetchRejection:v,allowStaleOnFetchAbort:f,ignoreFetchAbort:g,status:p,signal:x};let L=a(this,P).get(t);if(L===void 0){p&&(p.fetch="miss");const O=y(this,_,ue).call(this,t,L,X,S);return O.__returned=O}{const O=a(this,b)[L];if(y(this,_,F).call(this,O)){const M=s&&O.__staleWhileFetching!==void 0;return p&&(p.fetch="inflight",M&&(p.returnedStale=!0)),M?O.__staleWhileFetching:O.__returned=O}const R=a(this,et).call(this,L);if(!w&&!R)return p&&(p.fetch="hit"),y(this,_,Bt).call(this,L),i&&a(this,Tt).call(this,L),p&&a(this,gt).call(this,p,L),O;const I=y(this,_,ue).call(this,t,L,X,S),E=I.__staleWhileFetching!==void 0&&s;return p&&(p.fetch=R?"stale":"refresh",E&&R&&(p.returnedStale=!0)),E?I.__staleWhileFetching:I.__returned=I}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=a(this,Nt);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:r,...n}=e,h=this.get(t,n);if(!r&&h!==void 0)return h;const c=s(t,h,{options:n,context:i});return this.set(t,c,n),c}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,status:n}=e,h=a(this,P).get(t);if(h!==void 0){const c=a(this,b)[h],u=y(this,_,F).call(this,c);return n&&a(this,gt).call(this,n,h),a(this,et).call(this,h)?(n&&(n.get="stale"),u?(n&&s&&c.__staleWhileFetching!==void 0&&(n.returnedStale=!0),s?c.__staleWhileFetching:void 0):(r||y(this,_,vt).call(this,t,"expire"),n&&s&&(n.returnedStale=!0),s?c:void 0)):(n&&(n.get="hit"),u?c.__staleWhileFetching:(y(this,_,Bt).call(this,h),i&&a(this,Tt).call(this,h),c))}n&&(n.get="miss")}delete(t){return y(this,_,vt).call(this,t,"delete")}clear(){return y(this,_,Pe).call(this,"delete")}};st=new WeakMap,J=new WeakMap,it=new WeakMap,rt=new WeakMap,Ut=new WeakMap,Nt=new WeakMap,$=new WeakMap,nt=new WeakMap,P=new WeakMap,q=new WeakMap,b=new WeakMap,Q=new WeakMap,Y=new WeakMap,Z=new WeakMap,j=new WeakMap,ot=new WeakMap,W=new WeakMap,at=new WeakMap,lt=new WeakMap,tt=new WeakMap,ht=new WeakMap,St=new WeakMap,K=new WeakMap,_=new WeakSet,Re=function(){const t=new ce(a(this,st)),e=new ce(a(this,st));C(this,tt,t),C(this,lt,e),C(this,Qt,(r,n,h=Dt.now())=>{if(e[r]=n!==0?h:0,t[r]=n,n!==0&&this.ttlAutopurge){const c=setTimeout(()=>{a(this,et).call(this,r)&&y(this,_,vt).call(this,a(this,q)[r],"expire")},n+1);c.unref&&c.unref()}}),C(this,Tt,r=>{e[r]=t[r]!==0?Dt.now():0}),C(this,gt,(r,n)=>{if(t[n]){const h=t[n],c=e[n];if(!h||!c)return;r.ttl=h,r.start=c,r.now=s||i();const u=r.now-c;r.remainingTTL=h-u}});let s=0;const i=()=>{const r=Dt.now();if(this.ttlResolution>0){s=r;const n=setTimeout(()=>s=0,this.ttlResolution);n.unref&&n.unref()}return r};this.getRemainingTTL=r=>{const n=a(this,P).get(r);if(n===void 0)return 0;const h=t[n],c=e[n];return!h||!c?1/0:h-((s||i())-c)},C(this,et,r=>{const n=e[r],h=t[r];return!!h&&!!n&&(s||i())-n>h})},Tt=new WeakMap,gt=new WeakMap,Qt=new WeakMap,et=new WeakMap,Ds=function(){const t=new ce(a(this,st));C(this,nt,0),C(this,at,t),C(this,Mt,e=>{C(this,nt,a(this,nt)-t[e]),t[e]=0}),C(this,Kt,(e,s,i,r)=>{if(y(this,_,F).call(this,s))return 0;if(!ft(i)){if(!r)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof r!="function")throw new TypeError("sizeCalculation must be a function");if(i=r(s,e),!ft(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),C(this,Pt,(e,s,i)=>{if(t[e]=s,a(this,J)){const r=a(this,J)-t[e];for(;a(this,nt)>r;)y(this,_,de).call(this,!0)}C(this,nt,a(this,nt)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=a(this,nt))})},Mt=new WeakMap,Pt=new WeakMap,Kt=new WeakMap,pt=function*({allowStale:t=this.allowStale}={}){if(a(this,$))for(let e=a(this,j);y(this,_,Ue).call(this,e)&&(!t&&a(this,et).call(this,e)||(yield e),e!==a(this,Z));)e=a(this,Y)[e]},yt=function*({allowStale:t=this.allowStale}={}){if(a(this,$))for(let e=a(this,Z);y(this,_,Ue).call(this,e)&&(!t&&a(this,et).call(this,e)||(yield e),e!==a(this,j));)e=a(this,Q)[e]},Ue=function(t){return t!==void 0&&a(this,P).get(a(this,q)[t])===t},de=function(t){var r,n;const e=a(this,Z),s=a(this,q)[e],i=a(this,b)[e];return a(this,St)&&y(this,_,F).call(this,i)?i.__abortController.abort(new Error("evicted")):(a(this,ht)||a(this,K))&&(a(this,ht)&&((r=a(this,it))==null||r.call(this,i,s,"evict")),a(this,K)&&((n=a(this,W))==null||n.push([i,s,"evict"]))),a(this,Mt).call(this,e),t&&(a(this,q)[e]=void 0,a(this,b)[e]=void 0,a(this,ot).push(e)),a(this,$)===1?(C(this,Z,C(this,j,0)),a(this,ot).length=0):C(this,Z,a(this,Q)[e]),a(this,P).delete(s),ee(this,$)._--,e},ue=function(t,e,s,i){const r=e===void 0?void 0:a(this,b)[e];if(y(this,_,F).call(this,r))return r;const n=new ge,{signal:h}=s;h==null||h.addEventListener("abort",()=>n.abort(h.reason),{signal:n.signal});const c={signal:n.signal,options:s,context:i},u=(g,f=!1)=>{const{aborted:S}=n.signal,w=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(S&&!f?(s.status.fetchAborted=!0,s.status.fetchError=n.signal.reason,w&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),S&&!w&&!f)return d(n.signal.reason);const p=m;return a(this,b)[e]===m&&(g===void 0?p.__staleWhileFetching?a(this,b)[e]=p.__staleWhileFetching:y(this,_,vt).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,c.options))),g},d=g=>{const{aborted:f}=n.signal,S=f&&s.allowStaleOnFetchAbort,w=S||s.allowStaleOnFetchRejection,p=w||s.noDeleteOnFetchRejection,x=m;if(a(this,b)[e]===m&&(!p||x.__staleWhileFetching===void 0?y(this,_,vt).call(this,t,"fetch"):S||(a(this,b)[e]=x.__staleWhileFetching)),w)return s.status&&x.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),x.__staleWhileFetching;if(x.__returned===x)throw g};s.status&&(s.status.fetchDispatched=!0);const m=new Promise((g,f)=>{var w;const S=(w=a(this,Ut))==null?void 0:w.call(this,t,r,c);S&&S instanceof Promise&&S.then(p=>g(p===void 0?void 0:p),f),n.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=p=>u(p,!0)))})}).then(u,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),d(g))),v=Object.assign(m,{__abortController:n,__staleWhileFetching:r,__returned:void 0});return e===void 0?(this.set(t,v,{...c.options,status:void 0}),e=a(this,P).get(t)):a(this,b)[e]=v,v},F=function(t){if(!a(this,St))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof ge},Ne=function(t,e){a(this,Y)[e]=t,a(this,Q)[t]=e},Bt=function(t){t!==a(this,j)&&(t===a(this,Z)?C(this,Z,a(this,Q)[t]):y(this,_,Ne).call(this,a(this,Y)[t],a(this,Q)[t]),y(this,_,Ne).call(this,a(this,j),t),C(this,j,t))},vt=function(t,e){var i,r,n,h;let s=!1;if(a(this,$)!==0){const c=a(this,P).get(t);if(c!==void 0)if(s=!0,a(this,$)===1)y(this,_,Pe).call(this,e);else{a(this,Mt).call(this,c);const u=a(this,b)[c];if(y(this,_,F).call(this,u)?u.__abortController.abort(new Error("deleted")):(a(this,ht)||a(this,K))&&(a(this,ht)&&((i=a(this,it))==null||i.call(this,u,t,e)),a(this,K)&&((r=a(this,W))==null||r.push([u,t,e]))),a(this,P).delete(t),a(this,q)[c]=void 0,a(this,b)[c]=void 0,c===a(this,j))C(this,j,a(this,Y)[c]);else if(c===a(this,Z))C(this,Z,a(this,Q)[c]);else{const d=a(this,Y)[c];a(this,Q)[d]=a(this,Q)[c];const m=a(this,Q)[c];a(this,Y)[m]=a(this,Y)[c]}ee(this,$)._--,a(this,ot).push(c)}}if(a(this,K)&&((n=a(this,W))!=null&&n.length)){const c=a(this,W);let u;for(;u=c==null?void 0:c.shift();)(h=a(this,rt))==null||h.call(this,...u)}return s},Pe=function(t){var e,s,i;for(const r of y(this,_,yt).call(this,{allowStale:!0})){const n=a(this,b)[r];if(y(this,_,F).call(this,n))n.__abortController.abort(new Error("deleted"));else{const h=a(this,q)[r];a(this,ht)&&((e=a(this,it))==null||e.call(this,n,h,t)),a(this,K)&&((s=a(this,W))==null||s.push([n,h,t]))}}if(a(this,P).clear(),a(this,b).fill(void 0),a(this,q).fill(void 0),a(this,tt)&&a(this,lt)&&(a(this,tt).fill(0),a(this,lt).fill(0)),a(this,at)&&a(this,at).fill(0),C(this,Z,0),C(this,j,0),a(this,ot).length=0,C(this,nt,0),C(this,$,0),a(this,K)&&a(this,W)){const r=a(this,W);let n;for(;n=r==null?void 0:r.shift();)(i=a(this,rt))==null||i.call(this,...n)}};let Oe=$e;class jr{constructor(){l(this,"_syncStatus",{status:oi.done,foldersProgress:[]});l(this,"_syncEnabledState",Ve.initializing);l(this,"_workspaceGuidelines",[]);l(this,"_openUserGuidelinesInput",!1);l(this,"_userGuidelines");l(this,"_contextStore",new Sr);l(this,"_prevOpenFiles",[]);l(this,"_disableContext",!1);l(this,"_enableAgentMemories",!1);l(this,"subscribers",new Set);l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case wt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case wt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case wt.fileRangesSelected:this.updateSelections(e.data);break;case wt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case wt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case wt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});l(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:G.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});l(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});l(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});l(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});l(this,"addFile",t=>{this.addFiles([t])});l(this,"addFiles",t=>{this.updateFiles(t,[])});l(this,"removeFile",t=>{this.removeFiles([t])});l(this,"removeFiles",t=>{this.updateFiles([],t)});l(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});l(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});l(this,"updateFiles",(t,e)=>{const s=n=>({file:n,...be(n)}),i=t.map(s),r=e.map(s);this._contextStore.update(i,r,n=>n.id),this.notifySubscribers()});l(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});l(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});l(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...be(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.recentFile&&(r.file=r.recentFile,delete r.recentFile)}),e.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.file&&(r.recentFile=r.file,delete r.file)}),this.notifySubscribers()});l(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});l(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,i=t.overLimit||((e==null?void 0:e.overLimit)??!1),r={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:G.active,referenceCount:1,showWarning:i,rulesAndGuidelinesState:e};this._contextStore.update([r],s,n=>{var h;return n.id+String((h=n.userGuidelines)==null?void 0:h.overLimit)}),this.notifySubscribers()});l(this,"onGuidelinesStateUpdate",t=>{var i;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const r=e||{overLimit:!1,contents:"",lengthLimit:((i=t.rulesAndGuidelines)==null?void 0:i.lengthLimit)??2e3};this.updateUserGuidelines(r,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(r=>r.sourceFolder))});l(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));l(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});l(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});l(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});l(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Qe),s=t.map(i=>({selection:i,...be(i)}));this._contextStore.update([],e,i=>i.id),this._contextStore.update(s,[],i=>i.id),this.notifySubscribers()});l(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});l(this,"markInactive",t=>{this.markItemsInactive([t])});l(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,G.inactive)}),this.notifySubscribers()});l(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});l(this,"markActive",t=>{this.markItemsActive([t])});l(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,G.active)}),this.notifySubscribers()});l(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});l(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});l(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>ai(t)&&!Ze(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Ze)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Qe)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(li)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Ke)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(hi)}get userGuidelines(){return this._contextStore.values.filter(Xe)}get workspaceGuidelines(){return this._workspaceGuidelines}get agentMemories(){return[{...ci,status:this._enableAgentMemories?G.active:G.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Je(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===G.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===G.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===G.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===G.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===G.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===G.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var c;if(this.syncEnabledState===Ve.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(u=>u.progress!==void 0);if(t.length===0)return;const e=t.reduce((u,d)=>{var m;return u+(((m=d==null?void 0:d.progress)==null?void 0:m.trackedFiles)??0)},0),s=t.reduce((u,d)=>{var m;return u+(((m=d==null?void 0:d.progress)==null?void 0:m.backlogSize)??0)},0),i=Math.max(e,0),r=Math.min(Math.max(s,0),i),n=i-r,h=[];for(const u of t)(c=u==null?void 0:u.progress)!=null&&c.newlyTracked&&h.push(u.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:n,backlogSize:r,newlyTrackedFolders:h}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Ke(t)||Xe(t)||Is(t)||Je(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===G.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===G.inactive)}get isContextDisabled(){return this._disableContext}}class Sr{constructor(){l(this,"_cache",new Oe({max:1e3}));l(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));l(this,"clear",()=>{this._cache.clear()});l(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});l(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});l(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,r=this._cache.get(s),n=t.status??(r==null?void 0:r.status)??G.active;r?(r.referenceCount+=i,r.status=n,r.pinned=t.pinned??r.pinned,r.showWarning=t.showWarning??r.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in r&&(r.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in r&&(r.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:n})});l(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});l(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});l(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});l(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});l(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});l(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===G.active?G.inactive:G.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}var br=Xt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z" fill="currentColor"></path></svg>');function Wr(o){var t=br();Jt(o,t)}var xr=Xt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z" fill="currentColor"></path></svg>');function Br(o){var t=xr();Jt(o,t)}var wr=Xt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z" fill="currentColor"></path></svg>');function Vr(o){var t=wr();Jt(o,t)}export{Fs as A,De as C,gi as F,Lt as N,jr as S,Ee as T,Wr as a,Br as b,Vr as c,z as d,Ur as e,us as f,xi as g,Ci as h,kt as i,Or as j,Pr as k,he as l,ur as m,mr as n,$r as o,G as p,Gr as q,me as r,Rr as s,Si as t,Nr as u,bi as v,zr as w,Ts as x};
