import{v as U,t as w,N as V,aq as W,x as c,a3 as A,a4 as Z,a5 as F,y as v,E as tt,z as m,A as k,B as q,u as h,H as at,a as st,P as n,m as z,$ as lt,F as l,b as r,I as y,X as it,C as et,a0 as ct,ac as I,ad as E,Y as nt}from"./GuardedIcon-BFT2yJIo.js";import{B as ot,b as ut}from"./BaseTextInput-BaUpeUef.js";import{b as i}from"./IconButtonAugment-CR0fVrwD.js";var rt=v('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),vt=v('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),ft=v('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),dt=v("<!> <input/> <!>",1),ht=v("<div><!> <!></div>");function gt(H,a){const o=U(a),M=w(a,["children","$$slots","$$events","$$legacy"]),p=w(M,["variant","size","color","textInput","value","id","outline"]);V(a,!1);const x=z(),b=z(),g=z(),N=W();let P=c(a,"variant",8,"surface"),S=c(a,"size",8,2),T=c(a,"color",24,()=>{}),L=c(a,"textInput",28,()=>{}),X=c(a,"value",12,""),R=c(a,"id",24,()=>{}),Y=c(a,"outline",8,!0);const j=`text-field-${Math.random().toString(36).substring(2,11)}`;function D(e){N("change",e)}A(()=>E(R()),()=>{I(x,R()||j)}),A(()=>(n(b),n(g),E(p)),()=>{I(b,p.class),I(g,nt(p,["class"]))}),Z(),F();var _=ht();tt(_,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":o.iconLeft!==void 0,"c-text-field--has-right-icon":o.iconRight!==void 0});var B=h(_),G=e=>{var f=rt(),d=h(f);y(d,a,"label",{},null),et(()=>ct(f,"for",n(x))),r(e,f)};m(B,e=>{k(()=>o.label)&&e(G)});var J=q(B,2);ot(J,{get variant(){return P()},get size(){return S()},get color(){return T()},truncate:!0,get outline(){return Y()},children:(e,f)=>{var d=dt(),C=at(d),K=t=>{var u=vt(),$=h(u);y($,a,"iconLeft",{},null),r(t,u)};m(C,t=>{k(()=>o.iconLeft)&&t(K)});var s=q(C,2);st(s,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${n(b)}`,id:n(x),...n(g)}),void 0,"svelte-vuqlvc"),lt(s,t=>L(t),()=>L());var O=q(s,2),Q=t=>{var u=ft(),$=h(u);y($,a,"iconRight",{},null),r(t,u)};m(O,t=>{k(()=>o.iconRight)&&t(Q)}),ut(s,X),l("change",s,D),l("click",s,function(t){i.call(this,a,t)}),l("keydown",s,function(t){i.call(this,a,t)}),l("input",s,function(t){i.call(this,a,t)}),l("blur",s,function(t){i.call(this,a,t)}),l("dblclick",s,function(t){i.call(this,a,t)}),l("focus",s,function(t){i.call(this,a,t)}),l("mouseup",s,function(t){i.call(this,a,t)}),l("selectionchange",s,function(t){i.call(this,a,t)}),r(e,d)},$$slots:{default:!0}}),r(H,_),it()}export{gt as T};
