import{v as M,t as z,x as i,ap as N,y as u,z as g,A as h,B as w,u as c,C as O,E as Q,b as o,I as b,T as S,G as U,H as V,P as x,D as y}from"./GuardedIcon-BFT2yJIo.js";import{B as W,b as s}from"./IconButtonAugment-CR0fVrwD.js";var X=u('<div class="c-button--icon svelte-pblv7r"><!></div>'),Y=u('<div class="c-button--text svelte-pblv7r"><!></div>'),Z=u('<div class="c-button--icon svelte-pblv7r"><!></div>'),_=u("<div><!> <!> <!></div>");function st(k,t){const d=M(t),B=z(t,["children","$$slots","$$events","$$legacy"]),I=z(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let r=i(t,"size",8,2),m=i(t,"variant",8,"solid"),L=i(t,"color",8,"neutral"),R=i(t,"highContrast",8,!1),A=i(t,"disabled",8,!1),D=i(t,"radius",8,"medium"),E=i(t,"loading",8,!1),G=i(t,"alignment",8,"center");W(k,N({get size(){return r()},get variant(){return m()},get color(){return L()},get highContrast(){return R()},get disabled(){return A()},get loading(){return E()},get alignment(){return G()},get radius(){return D()}},()=>I,{$$events:{click(e){s.call(this,t,e)},keyup(e){s.call(this,t,e)},keydown(e){s.call(this,t,e)},mousedown(e){s.call(this,t,e)},mouseover(e){s.call(this,t,e)},focus(e){s.call(this,t,e)},mouseleave(e){s.call(this,t,e)},blur(e){s.call(this,t,e)},contextmenu(e){s.call(this,t,e)}},children:(e,C)=>{var v=_(),$=c(v),H=a=>{var l=X(),n=c(l);b(n,t,"iconLeft",{},null),o(a,l)};g($,a=>{h(()=>d.iconLeft)&&a(H)});var f=w($,2),j=a=>{var l=Y(),n=c(l);const q=y(()=>r()===.5?1:r()),F=y(()=>m()==="ghost"||r()===.5?"regular":"medium");S(n,{get size(){return x(q)},get weight(){return x(F)},children:(J,tt)=>{var p=U(),K=V(p);b(K,t,"default",{},null),o(J,p)},$$slots:{default:!0}}),o(a,l)};g(f,a=>{h(()=>d.default)&&a(j)});var P=w(f,2),T=a=>{var l=Z(),n=c(l);b(n,t,"iconRight",{},null),o(a,l)};g(P,a=>{h(()=>d.iconRight)&&a(T)}),O(()=>Q(v,1,`c-button--content c-button--size-${r()}`,"svelte-pblv7r")),o(e,v)},$$slots:{default:!0}}))}export{st as B};
