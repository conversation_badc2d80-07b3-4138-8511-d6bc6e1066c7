import{N as L,x as t,T as M,G as x,H as v,af as O,b as l,X as Q,a as U,y as d,z as c,B as f,C as z,a1 as I,P as i,R as r,u as p,S as $,V as k,E as W}from"./GuardedIcon-BFT2yJIo.js";import{g as Y,a as Z,n as w}from"./file-paths-BcSg4gks.js";var aa=d('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),sa=d('<span class="right-icons svelte-9pfhnp"><!></span>'),ea=d('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function ia(C,a){L(a,!0);let G=t(a,"class",3,""),y=t(a,"size",3,1),B=t(a,"nopath",3,!1),E=t(a,"growname",3,!0),o=t(a,"onClick",19,()=>{}),h=r(()=>w(a.filepath)),F=r(()=>Y(i(h))),H=r(()=>Z(i(h))),N=r(()=>o()?"button":"div");M(C,{get size(){return y()},children:(P,na)=>{var u=x(),j=v(u);O(j,()=>i(N),!1,(R,S)=>{U(R,()=>({class:`c-filespan ${G()}`,role:o()?"button":"",tabindex:"0",onclick:o()}),void 0,"svelte-9pfhnp");var g=ea(),m=v(g),T=s=>{var e=x(),n=v(e);$(n,()=>a.leftIcon??k),l(s,e)};c(m,s=>{a.leftIcon&&s(T)});var _=f(m,2),V=p(_),b=f(_,2),X=s=>{var e=aa();let n;var D=p(e),J=p(D);z(K=>{n=W(e,1,"c-filespan__dir svelte-9pfhnp",null,n,K),I(J,i(H))},[()=>({growname:E()})]),l(s,e)};c(b,s=>{B()||s(X)});var q=f(b,2),A=s=>{var e=sa(),n=p(e);$(n,()=>a.rightIcon??k),l(s,e)};c(q,s=>{a.rightIcon&&s(A)}),z(()=>I(V,i(F))),l(S,g)}),l(P,u)},$$slots:{default:!0}}),Q()}export{ia as F};
