import{i as $,av as b,A as x,aw as g,N as z,x as r,y as S,a as B,P as w,Q as E,R as V,T as j,G as A,H as C,S as G,V as H,b as f,u as M,X as N,O as h}from"./GuardedIcon-BFT2yJIo.js";import"./IconButtonAugment-CR0fVrwD.js";function T(e,a,u=a){var l=$();b(e,"input",n=>{var t=n?e.defaultValue:e.value;if(t=s(e)?d(t):t,u(t),l&&t!==(t=a())){var i=e.selectionStart,c=e.selectionEnd;e.value=t??"",c!==null&&(e.selectionStart=i,e.selectionEnd=Math.min(c,e.value.length))}}),x(a)==null&&e.value&&u(s(e)?d(e.value):e.value),g(()=>{var n=a();s(e)&&n===d(e.value)||(e.type!=="date"||n||e.value)&&n!==e.value&&(e.value=n??"")})}function X(e,a,u=a){b(e,"change",l=>{var n=l?e.defaultChecked:e.checked;u(n)}),x(a)==null&&u(e.checked),g(()=>{var l=a();e.checked=!!l})}function s(e){var a=e.type;return a==="number"||a==="range"}function d(e){return e===""?null:+e}var O=S("<div><!></div>");function q(e,a){z(a,!0);let u=r(a,"variant",3,"surface"),l=r(a,"size",3,2),n=r(a,"type",3,"default"),t=r(a,"color",19,()=>{}),i=r(a,"truncate",3,!1),c=r(a,"outline",3,!0),y=V(()=>t()?h(t()):h("accent"));var v=O();B(v,o=>({...w(y),class:`c-base-text-input c-base-text-input--${u()} c-base-text-input--size-${l()}`,[E]:o}),[()=>({"c-base-text-input--truncate":i(),"c-base-text-input--has-color":t()!==void 0,"c-base-text-input--outline":c()})],"svelte-1pnwvrg");var k=M(v);j(k,{get type(){return n()},get size(){return l()},children:(o,P)=>{var p=A(),m=C(p);G(m,()=>a.children??H),f(o,p)},$$slots:{default:!0}}),f(e,v),N()}export{q as B,X as a,T as b};
