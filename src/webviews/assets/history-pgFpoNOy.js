var ln=Object.defineProperty;var dn=(n,e,t)=>e in n?ln(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var $e=(n,e,t)=>dn(n,typeof e!="symbol"?e+"":e,t);import{N as qt,x as X,ai as Je,P as r,m as Qt,ac as S,a3 as We,ad as be,a4 as mn,a5 as Ne,y as x,$ as ze,u as f,B as N,a9 as Ct,G as Me,H as pt,I as hn,T as fn,a7 as we,C as ot,a1 as st,A as ye,b as y,D as Kt,ae as ee,E as ke,X as $t,bd as he,z as B,W as ct,at as Ze,ag as mt,ah as Yt,ar as Ve,F as Ke,ay as tn,R as P,aj as gn,aP as vn}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{B as pn,c as Wt,I as Ae}from"./IconButtonAugment-CR0fVrwD.js";import{M as Re}from"./MaterialIcon-D9dP7dAZ.js";import{o as Ee}from"./keypress-DD1aQVr0.js";import{B as xe}from"./ButtonAugment-CWDjQYWT.js";import{C as bn}from"./CardAugment-DVbbQqkH.js";import{c as wn,S as le,M as yn}from"./index-CWns8XM2.js";import{T as kn}from"./TextAreaAugment-DIwZ8JEM.js";import{C as je}from"./next-edit-types-904A5ehg.js";import{t as bt,d as xn,m as en,a as Oe,b as Fe,F as vt}from"./differenceInCalendarDays-BtetrLlb.js";import{e as _n,R as Ut}from"./toggleHighContrast-C7wSWUJK.js";import{C as Nn}from"./CopyButton-DSjqZJL1.js";import"./BaseTextInput-BaUpeUef.js";import"./preload-helper-Dv6uf1Os.js";import"./copy-oYDqgVJ5.js";import"./SuccessfulButton-Btt1yYx9.js";function zt(n,e){return n instanceof Date?new n.constructor(e):new Date(e)}let Mn={};function me(){return Mn}function ne(n,e){var m,c,h,u;const t=me(),i=(e==null?void 0:e.weekStartsOn)??((c=(m=e==null?void 0:e.locale)==null?void 0:m.options)==null?void 0:c.weekStartsOn)??t.weekStartsOn??((u=(h=t.locale)==null?void 0:h.options)==null?void 0:u.weekStartsOn)??0,a=bt(n),o=a.getDay(),p=(o<i?7:0)+o-i;return a.setDate(a.getDate()-p),a.setHours(0,0,0,0),a}function de(n){return ne(n,{weekStartsOn:1})}function nn(n){const e=bt(n),t=e.getFullYear(),i=zt(n,0);i.setFullYear(t+1,0,4),i.setHours(0,0,0,0);const a=de(i),o=zt(n,0);o.setFullYear(t,0,4),o.setHours(0,0,0,0);const p=de(o);return e.getTime()>=a.getTime()?t+1:e.getTime()>=p.getTime()?t:t-1}function Tn(n){if(e=n,!(e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"||typeof n=="number"))return!1;var e;const t=bt(n);return!isNaN(Number(t))}const Pn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function fe(n){return(e={})=>{const t=e.width?String(e.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const Dn={date:fe({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:fe({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:fe({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Sn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Xt(n){return(e,t)=>{let i;if((t!=null&&t.context?String(t.context):"standalone")==="formatting"&&n.formattingValues){const a=n.defaultFormattingWidth||n.defaultWidth,o=t!=null&&t.width?String(t.width):a;i=n.formattingValues[o]||n.formattingValues[a]}else{const a=n.defaultWidth,o=t!=null&&t.width?String(t.width):n.defaultWidth;i=n.values[o]||n.values[a]}return i[n.argumentCallback?n.argumentCallback(e):e]}}const qn={ordinalNumber:(n,e)=>{const t=Number(n),i=t%100;if(i>20||i<10)switch(i%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},era:Xt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Xt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:n=>n-1}),month:Xt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Xt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Xt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function Jt(n){return(e,t={})=>{const i=t.width,a=i&&n.matchPatterns[i]||n.matchPatterns[n.defaultMatchWidth],o=e.match(a);if(!o)return null;const p=o[0],m=i&&n.parsePatterns[i]||n.parsePatterns[n.defaultParseWidth],c=Array.isArray(m)?function(u,v){for(let b=0;b<u.length;b++)if(v(u[b]))return b}(m,u=>u.test(p)):function(u,v){for(const b in u)if(Object.prototype.hasOwnProperty.call(u,b)&&v(u[b]))return b}(m,u=>u.test(p));let h;return h=n.valueCallback?n.valueCallback(c):c,h=t.valueCallback?t.valueCallback(h):h,{value:h,rest:e.slice(p.length)}}}const Cn={ordinalNumber:(Zt={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:n=>parseInt(n,10)},(n,e={})=>{const t=n.match(Zt.matchPattern);if(!t)return null;const i=t[0],a=n.match(Zt.parsePattern);if(!a)return null;let o=Zt.valueCallback?Zt.valueCallback(a[0]):a[0];return o=e.valueCallback?e.valueCallback(o):o,{value:o,rest:n.slice(i.length)}}),era:Jt({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Jt({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:n=>n+1}),month:Jt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Jt({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Jt({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var Zt;const $n={code:"en-US",formatDistance:(n,e,t)=>{let i;const a=Pn[n];return i=typeof a=="string"?a:e===1?a.one:a.other.replace("{{count}}",e.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+i:i+" ago":i},formatLong:Dn,formatRelative:(n,e,t,i)=>Sn[n],localize:qn,match:Cn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Wn(n){const e=bt(n);return xn(e,function(i){const a=bt(i),o=zt(i,0);return o.setFullYear(a.getFullYear(),0,1),o.setHours(0,0,0,0),o}(e))+1}function zn(n){const e=bt(n),t=+de(e)-+function(i){const a=nn(i),o=zt(i,0);return o.setFullYear(a,0,4),o.setHours(0,0,0,0),de(o)}(e);return Math.round(t/en)+1}function rn(n,e){var u,v,b,k;const t=bt(n),i=t.getFullYear(),a=me(),o=(e==null?void 0:e.firstWeekContainsDate)??((v=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:v.firstWeekContainsDate)??a.firstWeekContainsDate??((k=(b=a.locale)==null?void 0:b.options)==null?void 0:k.firstWeekContainsDate)??1,p=zt(n,0);p.setFullYear(i+1,0,o),p.setHours(0,0,0,0);const m=ne(p,e),c=zt(n,0);c.setFullYear(i,0,o),c.setHours(0,0,0,0);const h=ne(c,e);return t.getTime()>=m.getTime()?i+1:t.getTime()>=h.getTime()?i:i-1}function An(n,e){const t=bt(n),i=+ne(t,e)-+function(a,o){var u,v,b,k;const p=me(),m=(o==null?void 0:o.firstWeekContainsDate)??((v=(u=o==null?void 0:o.locale)==null?void 0:u.options)==null?void 0:v.firstWeekContainsDate)??p.firstWeekContainsDate??((k=(b=p.locale)==null?void 0:b.options)==null?void 0:k.firstWeekContainsDate)??1,c=rn(a,o),h=zt(a,0);return h.setFullYear(c,0,m),h.setHours(0,0,0,0),ne(h,o)}(t,e);return Math.round(i/en)+1}function M(n,e){return(n<0?"-":"")+Math.abs(n).toString().padStart(e,"0")}const Mt={y(n,e){const t=n.getFullYear(),i=t>0?t:1-t;return M(e==="yy"?i%100:i,e.length)},M(n,e){const t=n.getMonth();return e==="M"?String(t+1):M(t+1,2)},d:(n,e)=>M(n.getDate(),e.length),a(n,e){const t=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];default:return t==="am"?"a.m.":"p.m."}},h:(n,e)=>M(n.getHours()%12||12,e.length),H:(n,e)=>M(n.getHours(),e.length),m:(n,e)=>M(n.getMinutes(),e.length),s:(n,e)=>M(n.getSeconds(),e.length),S(n,e){const t=e.length,i=n.getMilliseconds();return M(Math.trunc(i*Math.pow(10,t-3)),e.length)}},Rn="midnight",En="noon",jn="morning",On="afternoon",Fn="evening",In="night",Ie={G:function(n,e,t){const i=n.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(i,{width:"abbreviated"});case"GGGGG":return t.era(i,{width:"narrow"});default:return t.era(i,{width:"wide"})}},y:function(n,e,t){if(e==="yo"){const i=n.getFullYear(),a=i>0?i:1-i;return t.ordinalNumber(a,{unit:"year"})}return Mt.y(n,e)},Y:function(n,e,t,i){const a=rn(n,i),o=a>0?a:1-a;return e==="YY"?M(o%100,2):e==="Yo"?t.ordinalNumber(o,{unit:"year"}):M(o,e.length)},R:function(n,e){return M(nn(n),e.length)},u:function(n,e){return M(n.getFullYear(),e.length)},Q:function(n,e,t){const i=Math.ceil((n.getMonth()+1)/3);switch(e){case"Q":return String(i);case"QQ":return M(i,2);case"Qo":return t.ordinalNumber(i,{unit:"quarter"});case"QQQ":return t.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(i,{width:"narrow",context:"formatting"});default:return t.quarter(i,{width:"wide",context:"formatting"})}},q:function(n,e,t){const i=Math.ceil((n.getMonth()+1)/3);switch(e){case"q":return String(i);case"qq":return M(i,2);case"qo":return t.ordinalNumber(i,{unit:"quarter"});case"qqq":return t.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(i,{width:"narrow",context:"standalone"});default:return t.quarter(i,{width:"wide",context:"standalone"})}},M:function(n,e,t){const i=n.getMonth();switch(e){case"M":case"MM":return Mt.M(n,e);case"Mo":return t.ordinalNumber(i+1,{unit:"month"});case"MMM":return t.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(i,{width:"narrow",context:"formatting"});default:return t.month(i,{width:"wide",context:"formatting"})}},L:function(n,e,t){const i=n.getMonth();switch(e){case"L":return String(i+1);case"LL":return M(i+1,2);case"Lo":return t.ordinalNumber(i+1,{unit:"month"});case"LLL":return t.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(i,{width:"narrow",context:"standalone"});default:return t.month(i,{width:"wide",context:"standalone"})}},w:function(n,e,t,i){const a=An(n,i);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):M(a,e.length)},I:function(n,e,t){const i=zn(n);return e==="Io"?t.ordinalNumber(i,{unit:"week"}):M(i,e.length)},d:function(n,e,t){return e==="do"?t.ordinalNumber(n.getDate(),{unit:"date"}):Mt.d(n,e)},D:function(n,e,t){const i=Wn(n);return e==="Do"?t.ordinalNumber(i,{unit:"dayOfYear"}):M(i,e.length)},E:function(n,e,t){const i=n.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(i,{width:"short",context:"formatting"});default:return t.day(i,{width:"wide",context:"formatting"})}},e:function(n,e,t,i){const a=n.getDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return M(o,2);case"eo":return t.ordinalNumber(o,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(n,e,t,i){const a=n.getDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return M(o,e.length);case"co":return t.ordinalNumber(o,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(n,e,t){const i=n.getDay(),a=i===0?7:i;switch(e){case"i":return String(a);case"ii":return M(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(i,{width:"short",context:"formatting"});default:return t.day(i,{width:"wide",context:"formatting"})}},a:function(n,e,t){const i=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(i,{width:"narrow",context:"formatting"});default:return t.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(n,e,t){const i=n.getHours();let a;switch(a=i===12?En:i===0?Rn:i/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,e,t){const i=n.getHours();let a;switch(a=i>=17?Fn:i>=12?On:i>=4?jn:In,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,e,t){if(e==="ho"){let i=n.getHours()%12;return i===0&&(i=12),t.ordinalNumber(i,{unit:"hour"})}return Mt.h(n,e)},H:function(n,e,t){return e==="Ho"?t.ordinalNumber(n.getHours(),{unit:"hour"}):Mt.H(n,e)},K:function(n,e,t){const i=n.getHours()%12;return e==="Ko"?t.ordinalNumber(i,{unit:"hour"}):M(i,e.length)},k:function(n,e,t){let i=n.getHours();return i===0&&(i=24),e==="ko"?t.ordinalNumber(i,{unit:"hour"}):M(i,e.length)},m:function(n,e,t){return e==="mo"?t.ordinalNumber(n.getMinutes(),{unit:"minute"}):Mt.m(n,e)},s:function(n,e,t){return e==="so"?t.ordinalNumber(n.getSeconds(),{unit:"second"}):Mt.s(n,e)},S:function(n,e){return Mt.S(n,e)},X:function(n,e,t){const i=n.getTimezoneOffset();if(i===0)return"Z";switch(e){case"X":return He(i);case"XXXX":case"XX":return St(i);default:return St(i,":")}},x:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"x":return He(i);case"xxxx":case"xx":return St(i);default:return St(i,":")}},O:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+Ye(i,":");default:return"GMT"+St(i,":")}},z:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+Ye(i,":");default:return"GMT"+St(i,":")}},t:function(n,e,t){return M(Math.trunc(n.getTime()/1e3),e.length)},T:function(n,e,t){return M(n.getTime(),e.length)}};function Ye(n,e=""){const t=n>0?"-":"+",i=Math.abs(n),a=Math.trunc(i/60),o=i%60;return o===0?t+String(a):t+String(a)+e+M(o,2)}function He(n,e){return n%60==0?(n>0?"-":"+")+M(Math.abs(n)/60,2):St(n,e)}function St(n,e=""){const t=n>0?"-":"+",i=Math.abs(n);return t+M(Math.trunc(i/60),2)+e+M(i%60,2)}const Be=(n,e)=>{switch(n){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},Le=(n,e)=>{switch(n){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},Yn={p:Le,P:(n,e)=>{const t=n.match(/(P+)(p+)?/)||[],i=t[1],a=t[2];if(!a)return Be(n,e);let o;switch(i){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",Be(i,e)).replace("{{time}}",Le(a,e))}},Hn=/^D+$/,Bn=/^Y+$/,Ln=["D","DD","YY","YYYY"],Gn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Qn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Un=/^'([^]*?)'?$/,Xn=/''/g,Jn=/[a-zA-Z]/;function Ge(n,e,t){var u,v,b,k,$,T,E,_;const i=me(),a=(t==null?void 0:t.locale)??i.locale??$n,o=(t==null?void 0:t.firstWeekContainsDate)??((v=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:v.firstWeekContainsDate)??i.firstWeekContainsDate??((k=(b=i.locale)==null?void 0:b.options)==null?void 0:k.firstWeekContainsDate)??1,p=(t==null?void 0:t.weekStartsOn)??((T=($=t==null?void 0:t.locale)==null?void 0:$.options)==null?void 0:T.weekStartsOn)??i.weekStartsOn??((_=(E=i.locale)==null?void 0:E.options)==null?void 0:_.weekStartsOn)??0,m=bt(n);if(!Tn(m))throw new RangeError("Invalid time value");let c=e.match(Qn).map(l=>{const s=l[0];return s==="p"||s==="P"?(0,Yn[s])(l,a.formatLong):l}).join("").match(Gn).map(l=>{if(l==="''")return{isToken:!1,value:"'"};const s=l[0];if(s==="'")return{isToken:!1,value:Zn(l)};if(Ie[s])return{isToken:!0,value:l};if(s.match(Jn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+s+"`");return{isToken:!1,value:l}});a.localize.preprocessor&&(c=a.localize.preprocessor(m,c));const h={firstWeekContainsDate:o,weekStartsOn:p,locale:a};return c.map(l=>{if(!l.isToken)return l.value;const s=l.value;return(!(t!=null&&t.useAdditionalWeekYearTokens)&&function(d){return Bn.test(d)}(s)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&function(d){return Hn.test(d)}(s))&&function(d,A,q){const F=function(Z,nt,L){const it=Z[0]==="Y"?"years":"days of the month";return`Use \`${Z.toLowerCase()}\` instead of \`${Z}\` (in \`${nt}\`) for formatting ${it} to the input \`${L}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(d,A,q);if(console.warn(F),Ln.includes(d))throw new RangeError(F)}(s,e,String(n)),(0,Ie[s[0]])(m,s,a.localize,h)}).join("")}function Zn(n){const e=n.match(Un);return e?e[1].replace(Xn,"'"):n}function ge(n,e){const t=function(m){const c={},h=m.split(ce.dateTimeDelimiter);let u;if(h.length>2)return c;if(/:/.test(h[0])?u=h[0]:(c.date=h[0],u=h[1],ce.timeZoneDelimiter.test(c.date)&&(c.date=m.split(ce.timeZoneDelimiter)[0],u=m.substr(c.date.length,m.length))),u){const v=ce.timezone.exec(u);v?(c.time=u.replace(v[1],""),c.timezone=v[1]):c.time=u}return c}(n);let i;if(t.date){const m=function(c,h){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+h)+"})|(\\d{2}|[+-]\\d{"+(2+h)+"})$)"),v=c.match(u);if(!v)return{year:NaN,restDateString:""};const b=v[1]?parseInt(v[1]):null,k=v[2]?parseInt(v[2]):null;return{year:k===null?b:100*k,restDateString:c.slice((v[1]||v[2]).length)}}(t.date,2);i=function(c,h){if(h===null)return new Date(NaN);const u=c.match(Vn);if(!u)return new Date(NaN);const v=!!u[4],b=Vt(u[1]),k=Vt(u[2])-1,$=Vt(u[3]),T=Vt(u[4]),E=Vt(u[5])-1;if(v)return function(_,l,s){return l>=1&&l<=53&&s>=0&&s<=6}(0,T,E)?function(_,l,s){const d=new Date(0);d.setUTCFullYear(_,0,4);const A=d.getUTCDay()||7,q=7*(l-1)+s+1-A;return d.setUTCDate(d.getUTCDate()+q),d}(h,T,E):new Date(NaN);{const _=new Date(0);return function(l,s,d){return s>=0&&s<=11&&d>=1&&d<=(ei[s]||(Qe(l)?29:28))}(h,k,$)&&function(l,s){return s>=1&&s<=(Qe(l)?366:365)}(h,b)?(_.setUTCFullYear(h,k,Math.max(b,$)),_):new Date(NaN)}}(m.restDateString,m.year)}if(!i||isNaN(i.getTime()))return new Date(NaN);const a=i.getTime();let o,p=0;if(t.time&&(p=function(m){const c=m.match(Kn);if(!c)return NaN;const h=ve(c[1]),u=ve(c[2]),v=ve(c[3]);return function(b,k,$){return b===24?k===0&&$===0:$>=0&&$<60&&k>=0&&k<60&&b>=0&&b<25}(h,u,v)?h*Oe+u*Fe+1e3*v:NaN}(t.time),isNaN(p)))return new Date(NaN);if(!t.timezone){const m=new Date(a+p),c=new Date(0);return c.setFullYear(m.getUTCFullYear(),m.getUTCMonth(),m.getUTCDate()),c.setHours(m.getUTCHours(),m.getUTCMinutes(),m.getUTCSeconds(),m.getUTCMilliseconds()),c}return o=function(m){if(m==="Z")return 0;const c=m.match(ti);if(!c)return 0;const h=c[1]==="+"?-1:1,u=parseInt(c[2]),v=c[3]&&parseInt(c[3])||0;return function(b,k){return k>=0&&k<=59}(0,v)?h*(u*Oe+v*Fe):NaN}(t.timezone),isNaN(o)?new Date(NaN):new Date(a+p+o)}const ce={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Vn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Kn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,ti=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Vt(n){return n?parseInt(n):1}function ve(n){return n&&parseFloat(n.replace(",","."))||0}const ei=[31,null,31,30,31,30,31,31,30,31,30,31];function Qe(n){return n%400==0||n%4==0&&n%100!=0}function ue(n){return typeof n=="string"?n:n==null?void 0:n.value}var ni=x('<div><div class="background-slider svelte-axvozx"></div> <!></div>');function an(n,e){qt(e,!1);let t=X(e,"options",8),i=X(e,"size",8,2),a=X(e,"disabled",8,!1),o=X(e,"onSelectOption",8),p=X(e,"activeOption",28,()=>ue(t()[0])),m=Qt(),c=Qt();function h(){var s;const _=(s=r(m))==null?void 0:s.querySelectorAll(".c-toggle-button__button");if(!_)return;const l=_[t().findIndex(d=>ue(d)===p())];if(r(m)&&r(c)&&l){const d=l.getBoundingClientRect(),A=r(m).getBoundingClientRect();he(c,r(c).style.left=d.left-A.left+"px"),he(c,r(c).style.width=`${d.width}px`),he(c,r(c).style.height=`${d.height}px`)}}let u=Qt(),v=Qt(!1),b=Qt();Je(()=>{var _;(_=r(u))==null||_.disconnect(),S(u,void 0),clearTimeout(r(b))}),We(()=>be(p()),()=>{p()&&h()}),We(()=>(r(m),r(u),r(b)),()=>{r(m)&&!r(u)&&(S(u,new ResizeObserver(()=>{S(v,!0),h(),clearTimeout(r(b)),S(b,setTimeout(()=>{S(v,!1)},100))})),r(u).observe(r(m)))}),mn(),Ne();var k=ni();let $;var T=f(k);ze(T,_=>S(c,_),()=>r(c));var E=N(T,2);Ct(E,1,t,ee,(_,l)=>{const s=Kt(()=>r(l)===p()?"c-toggle-button__button--active":"");pn(_,{get size(){return i()},get disabled(){return a()},variant:"ghost",color:"neutral",get class(){return`c-toggle-button__button ${r(s)??""}`},$$events:{click:()=>function(d){!a()&&o()(d)&&p(d)}(ue(r(l)))},children:(d,A)=>{var q=Me(),F=pt(q);const Z=Kt(()=>(r(l),ye(()=>ue(r(l)))));hn(F,e,"option-button-contents",{get option(){return r(Z)},get size(){return i()}},nt=>{const L=Kt(()=>i()===.5?1:i());fn(nt,{get size(){return r(L)},children:(it,ut)=>{var ht=we();ot(()=>st(ht,(r(l),ye(()=>typeof r(l)=="string"?r(l):r(l).label)))),y(it,ht)},$$slots:{default:!0}})}),y(d,q)},$$slots:{default:!0}})}),ze(k,_=>S(m,_),()=>r(m)),ot(_=>$=ke(k,1,"c-toggle-button svelte-axvozx",null,$,_),[()=>({"c-toggle-button--disabled":a(),"c-toggle-button--size-0_5":i()===.5,"c-toggle-button--size-1":i()===1,"c-toggle-button--size-2":i()===2,"c-toggle-button--size-3":i()===3,"c-toggle-button--size-4":i()===4,"c-toggle-button--resizing":r(v)})],Kt),y(n,k),$t()}const te=new class{constructor(){$e(this,"_state");this._state=Wt.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(n){return this._state.feedback[n]?this._state.feedback[n]:{selectedRating:vt.unset,feedbackNote:""}}setFeedback(n,e){this._state.feedback[n]=e,Wt.setState(this._state)}cleanupFeedback(n){for(const e of Object.keys(this._state.feedback))n[e]||delete this._state.feedback[e];Wt.setState(this._state)}},Ue=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],_e={lineNumbers:"off",padding:{top:18,bottom:18}};var ii=x('<div class="c-completion-code-block svelte-krgqjl"><div class="c-completion-code-block__content svelte-krgqjl"><!></div></div>'),ri=x('<span slot="text" class="c-history-header--ellipsis svelte-8btr94"> </span>'),ai=x('<span class="c-history-header--ellipsis-left svelte-8btr94"> </span>'),oi=x('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">File:</span> <!></div>'),si=x('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Instruction:</span> <span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ci=x('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ui=x('<div class="c-history-header svelte-8btr94"><div class="c-history-header__timestamp svelte-8btr94"> </div> <div class="c-history-header__metadata svelte-8btr94"><div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Request ID:</span> <!></div> <!> <!> <!></div></div>');function on(n,e){qt(e,!1);let t=X(e,"occuredAt",8),i=X(e,"requestID",8),a=X(e,"pathName",8,""),o=X(e,"repoRoot",8),p=X(e,"prompt",8,""),m=X(e,"others",24,()=>[]);function c(){Wt.postMessage({type:ct.openFile,data:{repoRoot:o(),pathName:a()}})}Ne();var h=ui(),u=f(h),v=f(u),b=N(u,2),k=f(b),$=N(f(k),2);Nn($,{get text(){return i()},variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:(d,A)=>{var q=ri(),F=f(q);ot(()=>st(F,i())),y(d,q)}}});var T=N(k,2),E=d=>{var A=oi(),q=N(f(A),2);xe(q,{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$events:{click:c},children:(F,Z)=>{var nt=ai(),L=f(nt);ot(()=>st(L,`‎${a()??""}`)),y(F,nt)},$$slots:{default:!0}}),y(d,A)};B(T,d=>{a()&&d(E)});var _=N(T,2),l=d=>{var A=si(),q=N(f(A),2),F=f(q);ot(()=>st(F,p())),y(d,A)};B(_,d=>{p()&&d(l)});var s=N(_,2);Ct(s,1,m,ee,(d,A)=>{var q=ci(),F=f(q),Z=f(F);ot(()=>st(Z,r(A))),y(d,q)}),ot(d=>st(v,d),[()=>(be(Ge),be(t()),ye(()=>Ge(t(),"p 'on' P")))],Kt),y(n,h),$t()}var li=x('<div class="c-unified-history-item__tabs svelte-179mxe5"><!></div>'),di=x('<div class="c-unified-history-item__code-block svelte-179mxe5"><!></div>'),mi=(n,e,t)=>e(r(t)),hi=x('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext"><code><span> </span></code></pre></div> <div> </div> <section>original: <!> modified: <!></section>',1),fi=(n,e,t)=>e(r(t)),gi=x('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext" class="c-next-edit-addition svelte-179mxe5"><code><span> </span></code></pre></div>'),vi=x('<section><!> <div class="c-unified-history-item__no-modifications svelte-179mxe5">Unchanged locations:</div> <!></section>'),pi=x('<div class="c-unified-history-item__feedback-area svelte-179mxe5"><div class="c-unified-history-item__feedback-content svelte-179mxe5"><!></div> <div class="c-unified-history-item__feedback-actions svelte-179mxe5"><!> <!></div></div>'),bi=x('<div class="c-unified-history-item__header svelte-179mxe5"><!></div> <!> <div class="c-unified-history-item__content svelte-179mxe5"><!></div> <div class="c-unified-history-item__footer svelte-179mxe5"><div class="c-unified-history-item__ratings svelte-179mxe5"><div class="c-unified-history-item__rating-buttons svelte-179mxe5"><!> <!></div> <div class="c-unified-history-item__thankyou svelte-179mxe5"> </div></div> <!></div>',1);function pe(n,e){qt(e,!0);let t=X(e,"completion",19,()=>{}),i=X(e,"nextEdit",19,()=>{}),a=X(e,"demo",3,!1),o=P(()=>t()?"completion":"nextEdit"),p=P(()=>{var w,W;return((w=t())==null?void 0:w.requestId)||((W=i())==null?void 0:W.requestId)||""}),m=P(()=>{var w,W;return((w=t())==null?void 0:w.occuredAt)||((W=i())==null?void 0:W.occurredAt)||new Date}),c=P(()=>{var w;return((w=t())==null?void 0:w.pathName)||""}),h=P(()=>{var w,W,J;return((w=t())==null?void 0:w.repoRoot)||((J=(W=i())==null?void 0:W.qualifiedPathName)==null?void 0:J.rootPath)||""}),u=P(()=>r(o)==="completion"?"Completion":"Next Edit");const v=t()?["Completion"]:["Next Edit"];let b,k,$=P(()=>"Leave feedback about this "+(r(o)==="completion"?"completion":"next edit")),T=P(()=>te.getFeedback(r(p))),E=mt(!1),_=mt(""),l=mt(!1),s=null,d=mt(""),A=mt(void 0),q=mt(Yt([])),F=mt(Yt([]));function Z(){S(_,Ue[Math.floor(Math.random()*Ue.length)],!0),b&&clearTimeout(b),b=setTimeout(()=>{S(_,"")},4e3)}function nt(w){r(T).selectedRating=w,s=w,S(d,""),S(l,!0)}function L(){S(l,!1),s=null,S(d,""),r(T).selectedRating=vt.unset}function it(){s&&r(d).trim().length!==0&&(function(w,W){if(Z(),k=r(T).selectedRating,w!==vt.unset&&(r(T).selectedRating=w),a())return;let J=W||r(T).feedbackNote;te.setFeedback(r(p),r(T)),S(E,!0);const At=r(o)==="completion"?ct.completionRating:ct.nextEditRating;Wt.postMessage({type:At,data:{requestId:r(p),rating:w,note:J.trim()}})}(s,r(d).trim()),L())}function ut(w){Wt.postMessage({type:ct.openFile,data:{repoRoot:w.qualifiedPathName.rootPath,pathName:w.result.path,range:w.lineRange,differentTab:!0}}),S(A,w,!0)}function ht(w){return S(u,w),!0}Ve(()=>{i()&&(S(q,i().suggestions.filter(w=>w.changeType!==je.noop),!0),S(F,i().suggestions.filter(w=>w.changeType===je.noop),!0))}),Ke("message",tn,function(w){if(a())return;const W=w.data;switch(W.type){case ct.completionRatingDone:{const{requestId:J}=W.data;if(J!==r(p))return;S(E,!1),W.data.success||(r(T).selectedRating=k,te.setFeedback(r(p),r(T)));break}case ct.nextEditRatingDone:{const{requestId:J}=W.data;if(J!==r(p))return;S(E,!1),W.data.success||(r(T).selectedRating=k,te.setFeedback(r(p),r(T)));break}}});const Ht=P(()=>"c-unified-history-item "+(r(E)?"c-unified-history-item--sending-feedback":""));bn(n,{size:2,variant:"surface",get class(){return r(Ht)},children:(w,W)=>{var J=bi(),At=pt(J),V=f(At);const lt=P(()=>i()?[`Request type: ${i().mode}/${i().scope}`]:void 0);on(V,{get occuredAt(){return r(m)},get requestID(){return r(p)},get pathName(){return r(c)},get repoRoot(){return r(h)},get others(){return r(lt)}});var dt=N(At,2),K=z=>{var U=li();an(f(U),{get options(){return v},onSelectOption:ht,get activeOption(){return r(u)},size:1}),y(z,U)};B(dt,z=>{v.length>1&&z(K)});var wt=N(dt,2),Rt=f(wt),Tt=z=>{var U=Me(),tt=pt(U);Ct(tt,17,()=>t().completions,ee,(ft,G)=>{var I=di();(function(et,O){qt(O,!0);const g=function(Nt){const at=Nt.split(`
`).slice(-6);for(let gt=0;gt<at.length;gt++)if(at[gt].trim().length>0)return at.slice(gt).join(`
`);return""}(O.prefix),j=(Y=O.suffix,!!(kt=O.completion.skippedSuffix)&&Y.indexOf(kt)===0);var Y,kt;const Dt=j?function(Nt,at){return at?Nt.indexOf(at)!==0?Nt:Nt.slice(at.length):Nt}(O.suffix,O.completion.skippedSuffix):O.suffix,xt=function(Nt){const at=Nt.split(`
`).slice(0,6);for(let gt=at.length-1;gt>=0;gt--)if(at[gt].trim().length>0)return at.slice(0,gt+1).join(`
`);return""}(Dt),R=O.completion.text,Q=j?O.completion.skippedSuffix:"",rt=O.completion.suffixReplacementText,_t=g+R+Q+rt+xt,H=_n.createModel(_t,"plaintext"),oe=H.getPositionAt(0),Lt=H.getPositionAt(g.length),se=H.getPositionAt(g.length),It=H.getPositionAt(g.length+R.length),Gt=H.getPositionAt(g.length+R.length),Te=H.getPositionAt(g.length+R.length+Q.length),Pe=H.getPositionAt(g.length+R.length+Q.length),De=H.getPositionAt(g.length+R.length+Q.length+rt.length),Se=H.getPositionAt(g.length+R.length+Q.length+rt.length),qe=H.getPositionAt(_t.length),sn=[{range:new Ut(oe.lineNumber,oe.column,Lt.lineNumber,Lt.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Ut(Se.lineNumber,Se.column,qe.lineNumber,qe.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Ut(se.lineNumber,se.column,It.lineNumber,It.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Ut(Pe.lineNumber,Pe.column,De.lineNumber,De.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Ut(Gt.lineNumber,Gt.column,Te.lineNumber,Te.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];Je(()=>{H==null||H.dispose()});var Ce=ii(),cn=f(Ce),un=f(cn);wn(un,{get options(){return _e},get model(){return H},get decorations(){return sn}}),y(et,Ce),$t()})(f(I),{get completion(){return r(G)},get prefix(){return t().prefix},get suffix(){return t().suffix}}),y(ft,I)}),y(z,U)},Et=(z,U)=>{var tt=ft=>{var G=vi(),I=f(G);Ct(I,17,()=>r(q),ee,(O,g)=>{var j=hi(),Y=pt(j);Y.__click=[mi,ut,g];var kt=P(()=>Ee("Enter",()=>ut(r(g))));Y.__keydown=function(...It){var Gt;(Gt=r(kt))==null||Gt.apply(this,It)};var Dt=f(Y),xt=f(Dt),R=f(xt);let Q;var rt=f(R),_t=N(Y,2),H=f(_t),oe=N(_t,2),Lt=N(f(oe));le(Lt,{get text(){return r(g).result.existingCode},get pathName(){return r(g).qualifiedPathName.relPath},options:{lineNumbers:"off"}});var se=N(Lt,2);le(se,{get text(){return r(g).result.suggestedCode},get pathName(){return r(g).qualifiedPathName.relPath},options:{lineNumbers:"off"}}),ot(It=>{Q=ke(R,1,"c-next-edit-addition svelte-179mxe5",null,Q,It),st(rt,`${r(g).qualifiedPathName.relPath??""}: ${r(g).lineRange.start+(r(g).lineRange.start<r(g).lineRange.stop?1:0)}-${r(g).lineRange.stop??""}`),st(H,r(g).result.changeDescription)},[()=>({"c-next-edit-addition-clicked":r(A)===r(g)})]),y(O,j)});var et=N(I,4);Ct(et,17,()=>r(F),ee,(O,g)=>{var j=gi();j.__click=[fi,ut,g];var Y=P(()=>Ee("Enter",()=>ut(r(g))));j.__keydown=function(...rt){var _t;(_t=r(Y))==null||_t.apply(this,rt)};var kt=f(j),Dt=f(kt),xt=f(Dt);let R;var Q=f(xt);ot(rt=>{R=ke(xt,1,"c-next-edit-addition svelte-179mxe5",null,R,rt),st(Q,`${r(g).qualifiedPathName.relPath??""}: ${r(g).lineRange.start+(r(g).lineRange.start<r(g).lineRange.stop?1:0)}-${r(g).lineRange.stop??""}`)},[()=>({"c-next-edit-addition-clicked":r(A)===r(g)})]),y(O,j)}),y(ft,G)};B(z,ft=>{r(o)==="nextEdit"&&i()&&ft(tt)},U)};B(Rt,z=>{r(o)==="completion"&&t()?z(Tt):z(Et,!1)});var Bt=N(wt,2),jt=f(Bt),Ot=f(jt),Ft=f(Ot);const C=P(()=>r(T).selectedRating===vt.positive?"success":"neutral");Ae(Ft,{variant:"ghost",get color(){return r(C)},size:2,get disabled(){return r(E)},get title(){return r($)},onclick:()=>nt(vt.positive),children:(z,U)=>{const tt=P(()=>r(T).selectedRating===vt.positive);Re(z,{iconName:"thumb_up",get fill(){return r(tt)}})},$$slots:{default:!0}});var D=N(Ft,2);const Pt=P(()=>r(T).selectedRating===vt.negative?"error":"neutral");Ae(D,{variant:"ghost",get color(){return r(Pt)},size:2,get disabled(){return r(E)},get title(){return r($)},onclick:()=>nt(vt.negative),children:(z,U)=>{const tt=P(()=>r(T).selectedRating===vt.negative);Re(z,{iconName:"thumb_down",get fill(){return r(tt)}})},$$slots:{default:!0}});var yt=N(Ot,2),ie=f(yt),re=N(jt,2),ae=z=>{var U=pi(),tt=f(U),ft=f(tt);kn(ft,{rows:4,placeholder:"Enter your feedback...",resize:"none",get value(){return r(d)},set value(g){S(d,g,!0)}});var G=N(tt,2),I=f(G);xe(I,{variant:"ghost",size:2,onclick:L,children:(g,j)=>{var Y=we("Cancel");y(g,Y)},$$slots:{default:!0}});var et=N(I,2);const O=P(()=>r(d).trim().length===0);xe(et,{variant:"solid",size:2,get disabled(){return r(O)},onclick:it,children:(g,j)=>{var Y=we("Share Feedback");y(g,Y)},$$slots:{default:!0}}),y(z,U)};B(re,z=>{r(l)&&z(ae)}),ot(()=>st(ie,r(_))),y(w,J)},$$slots:{default:!0}}),$t()}Ze(["click","keydown"]);var wi=x(`<div class="l-no-items svelte-10bvc8"><div class="l-no-items__msg svelte-10bvc8"><h2>History.</h2> <p>As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we'll display for each suggestion.</p></div> <div class="l-no-items__divider svelte-10bvc8"></div> <div class="l-no-items__example svelte-10bvc8"><!></div></div>`);function Xe(n,e,t){S(e,t.instruction.requestId,!0)}var yi=x('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),ki=x("modified: <!>",1),xi=x('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),_i=x("<section>original: <!> <!></section>"),Ni=x('<div class="c-instruction-item__no-modifications svelte-15p7ohn" role="button" tabindex="0">Click to view diff</div>'),Mi=x('<div class="c-instruction-item svelte-15p7ohn"><!> <!></div>');Ze(["keyup","click"]);var Ti=x('<div class="l-items-list__empty svelte-5e6wj2"><!></div>'),Pi=x('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Di=x('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Si=x('<div class="l-items-list__instructions-section svelte-5e6wj2"><h3 class="l-items-list__section-title svelte-5e6wj2">Instructions</h3> <div class="l-items-list__content svelte-5e6wj2"></div></div>'),qi=x('<div class="l-items-list__empty-panel svelte-5e6wj2"><p> </p></div>'),Ci=x('<div class="l-items-list__divider svelte-5e6wj2"></div>'),$i=x('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Wi=x('<div class="l-items-list__content svelte-5e6wj2"></div>'),zi=x('<!> <!> <div class="l-items-list__panel-content"><!></div>',1),Ai=x('<main class="l-items-list svelte-5e6wj2"><!></main>');vn(function(n,e){qt(e,!0);let t=Yt({}),i=Yt({}),a=Yt({});function o(l){for(const s of l)t[s.requestId]||(t[s.requestId]={...s,occuredAt:ge(s.occuredAt)});te.cleanupFeedback(t)}function p(l){for(const s of l)if(!i[s.requestId]){if(typeof s.occuredAt=="string"){const d=s.occuredAt;s.occuredAt=ge(d)}i[s.requestId]={...s}}}function m(l){for(const s of l)s.suggestions.length!==0&&(a[s.requestId]={requestId:s.requestId,occuredAt:ge(s.occurredAt),result:s})}Wt.postMessage({type:ct.historyLoaded});let c=mt(Yt([]));Ve(()=>{S(c,[...Object.values(i),...Object.values(t),...Object.values(a)].sort((l,s)=>s.occuredAt.getTime()-l.occuredAt.getTime()),!0)});let h=mt("Completions"),u=P(()=>r(c).filter(l=>"completions"in l)),v=P(()=>r(c).filter(l=>"result"in l)),b=P(()=>r(c).filter(l=>"prompt"in l)),k=P(()=>[{value:"Completions",label:`Completions ${r(u).length}`},{value:"Next Edits",label:`Next Edits ${r(v).length}`}]),$=P(()=>r(h)==="Completions"?r(u):r(v));function T(l){return S(h,l,!0),!0}var E=Me();Ke("message",tn,function(l){const s=l.data;switch(s.type){case ct.historyInitialize:p(s.data.instructions),o(s.data.completionRequests),m(s.data.nextEdits);break;case ct.completions:o(s.data);break;case ct.instructions:p(s.data);break;case ct.nextEditSuggestions:m([s.data])}});var _=pt(E);gn(_,()=>yn.Root,(l,s)=>{s(l,{children:(d,A)=>{var q=Ai(),F=f(q),Z=L=>{var it=Ti();(function(ut,ht){qt(ht,!1);const Ht={occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`};Ne();var w=wi(),W=N(f(w),4);pe(f(W),{get completion(){return Ht},demo:!0}),y(ut,w),$t()})(f(it),{}),y(L,it)},nt=L=>{var it=zi(),ut=pt(it);an(ut,{get options(){return r(k)},onSelectOption:T,get activeOption(){return r(h)},size:2});var ht=N(ut,2),Ht=V=>{var lt=Si(),dt=N(f(lt),2);Ct(dt,23,()=>r(b),K=>K.requestId,(K,wt,Rt)=>{var Tt=Di(),Et=pt(Tt),Bt=f(Et);const jt=P(()=>function(C){if(!("prompt"in C))throw new Error("wrong type");if("completions"in C)throw new Error("wrong type");return C}(r(wt)));(function(C,D){qt(D,!0);let Pt=mt(void 0);function yt(G){const I=G.split(`
`);for(let et=I.length-1;et>=0;et--)if(I[et].trim().length>0)return I.slice(0,et+1).join(`
`);return""}let ie=P(()=>yt(D.instruction.selectedText)),re=P(()=>yt(D.instruction.modifiedText));var ae=Mi(),z=f(ae);on(z,{get occuredAt(){return D.instruction.occuredAt},get requestID(){return D.instruction.requestId},get pathName(){return D.instruction.pathName},get repoRoot(){return D.instruction.repoRoot},get prompt(){return D.instruction.prompt}});var U=N(z,2),tt=G=>{var I=yi();y(G,I)},ft=(G,I)=>{var et=g=>{var j=_i(),Y=N(f(j));le(Y,{get options(){return _e},get text(){return r(ie)},get pathName(){return D.instruction.pathName}});var kt=N(Y,2),Dt=R=>{var Q=ki(),rt=N(pt(Q));le(rt,{get options(){return _e},get text(){return r(re)},get pathName(){return D.instruction.pathName}}),y(R,Q)},xt=R=>{var Q=xi();y(R,Q)};B(kt,R=>{r(ie)!==r(re)?R(Dt):R(xt,!1)}),y(g,j)},O=g=>{var j=Ni();j.__keyup=[Xe,Pt,D],j.__click=[Xe,Pt,D],y(g,j)};B(G,g=>{D.instruction.userRequested||r(Pt)===D.instruction.requestId?g(et):g(O,!1)},I)};B(U,G=>{D.instruction.selectedText===D.instruction.modifiedText?G(tt):G(ft,!1)}),y(C,ae),$t()})(Bt,{get instruction(){return r(jt)}});var Ot=N(Et,2),Ft=C=>{var D=Pi();y(C,D)};B(Ot,C=>{r(Rt)<r(b).length-1&&C(Ft)}),y(K,Tt)}),y(V,lt)};B(ht,V=>{r(b).length>0&&V(Ht)});var w=N(ht,2),W=f(w),J=V=>{var lt=qi(),dt=f(lt),K=f(dt);ot(wt=>st(K,`No ${wt??""} found.`),[()=>r(h).toLowerCase()]),y(V,lt)},At=V=>{var lt=Wi();Ct(lt,23,()=>r($),dt=>dt.requestId,(dt,K,wt)=>{var Rt=$i(),Tt=pt(Rt),Et=f(Tt),Bt=C=>{pe(C,{get completion(){return r(K)}})},jt=(C,D)=>{var Pt=yt=>{pe(yt,{get nextEdit(){return r(K).result}})};B(C,yt=>{"result"in r(K)&&yt(Pt)},D)};B(Et,C=>{"completions"in r(K)?C(Bt):C(jt,!1)});var Ot=N(Tt,2),Ft=C=>{var D=Ci();y(C,D)};B(Ot,C=>{r(wt)<r($).length-1&&C(Ft)}),y(dt,Rt)}),y(V,lt)};B(W,V=>{r($).length===0?V(J):V(At,!1)}),y(L,it)};B(F,L=>{r(c).length?L(nt,!1):L(Z)}),y(d,q)},$$slots:{default:!0}})}),y(n,E),$t()},{target:document.getElementById("app")});
