var Ht=Object.defineProperty;var $t=e=>{throw TypeError(e)};var It=(e,t,n)=>t in e?Ht(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var P=(e,t,n)=>It(e,typeof t!="symbol"?t+"":t,n),dt=(e,t,n)=>t.has(e)||$t("Cannot "+n);var l=(e,t,n)=>(dt(e,t,"read from private field"),n?n.call(e):t.get(e)),y=(e,t,n)=>t.has(e)?$t("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),D=(e,t,n,o)=>(dt(e,t,"write to private field"),o?o.call(e,n):t.set(e,n),n),tt=(e,t,n)=>(dt(e,t,"access private method"),n);import{b5 as Zt,ac as R,b6 as Ft,P as f,s as Tt,aw as Gt,A as ot,b3 as Xt,t as Ut,f as yt,a as rt,u as bt,b as j,ag as Jt,ah as Kt,R as k,a2 as Ct,b7 as pt,b8 as At,b9 as Qt,ak as jt,al as Yt,ba as te,Z as ee,N as st,x as w,G as it,H as M,z as V,X as ct,y as Y,r as wt,a5 as ne,ap as Pt,bb as oe,B as re,S as Rt,D as se,V as ie,aj as ce}from"./GuardedIcon-BFT2yJIo.js";import{h as ae}from"./IconButtonAugment-CR0fVrwD.js";import{s as ue}from"./plus-BGQoI3Yl.js";import{b as le}from"./BaseTextInput-BaUpeUef.js";function he(){return Symbol(Zt)}function de(e){let t,n=0,o=Tt(0);return()=>{Ft()&&(f(o),Gt(()=>(n===0&&(t=ot(()=>e(()=>function(s){R(s,s.v+1)}(o)))),n+=1,()=>{Xt().then(()=>{n-=1,n===0&&(t==null||t(),t=void 0)})})))}}var pe=yt("<svg><!></svg>");function nn(e,t){const n=Ut(t,["children","$$slots","$$events","$$legacy"]);var o=pe();rt(o,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...n}));var s=bt(o);ae(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M354.9 121.7c13.8 16 36.5 21.1 55.9 12.5 8.9-3.9 18.7-6.2 29.2-6.2 39.8 0 72 32.2 72 72q0 6-.9 11.7c-3.5 21.6 8.1 42.9 28.1 51.7C570.4 276.9 592 308 592 344c0 46.8-36.6 85.2-82.8 87.8-.6 0-1.3.1-1.9.2H144c-53 0-96-43-96-96 0-41.7 26.6-77.3 64-90.5 19.2-6.8 32-24.9 32-45.3v-.2c0-66.3 53.7-120 120-120 36.3 0 68.8 16.1 90.9 41.7M512 480v-.2c71.4-4.1 128-63.3 128-135.8 0-55.7-33.5-103.7-81.5-124.7 1-6.3 1.5-12.8 1.5-19.3 0-66.3-53.7-120-120-120-17.4 0-33.8 3.7-48.7 10.3C360.4 54.6 314.9 32 264 32c-92.8 0-168 75.2-168 168v.2C40.1 220 0 273.3 0 336c0 79.5 64.5 144 144 144zM223 255c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 384c0 13.3 10.7 24 24 24s24-10.7 24-24V249.9l39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0z"/>',!0),j(e,o)}const fe=["string","number","bigint","boolean"];function ft(e){return e==null||!!fe.includes(typeof e)||(Array.isArray(e)?e.every(t=>ft(t)):typeof e=="object"&&Object.getPrototypeOf(e)===Object.prototype)}const W=Symbol("box"),gt=Symbol("is-writable");function d(e){let t=Jt(Kt(e));return{[W]:!0,[gt]:!0,get current(){return f(t)},set current(n){R(t,n,!0)}}}function ge(...e){return function(t){var n;for(const o of e)if(o){if(t.defaultPrevented)return;typeof o=="function"?o.call(this,t):(n=o.current)==null||n.call(this,t)}}}d.from=function(e){return d.isBox(e)?e:function(t){return typeof t=="function"}(e)?d.with(e):d(e)},d.with=function(e,t){const n=k(e);return t?{[W]:!0,[gt]:!0,get current(){return f(n)},set current(o){t(o)}}:{[W]:!0,get current(){return e()}}},d.flatten=function(e){return Object.entries(e).reduce((t,[n,o])=>d.isBox(o)?(d.isWritableBox(o)?Object.defineProperty(t,n,{get:()=>o.current,set(s){o.current=s}}):Object.defineProperty(t,n,{get:()=>o.current}),t):Object.assign(t,{[n]:o}),{})},d.readonly=function(e){return d.isWritableBox(e)?{[W]:!0,get current(){return e.current}}:e},d.isBox=function(e){return function(t){return t!==null&&typeof t=="object"}(e)&&W in e},d.isWritableBox=function(e){return d.isBox(e)&&gt in e};var zt={},Ot=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,ve=/\n/g,me=/^\s*/,ye=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,be=/^:\s*/,we=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,ke=/^[;\s]*/,xe=/^\s+|\s+$/g,E="";function Bt(e){return e?e.replace(xe,E):E}var $e=Ct&&Ct.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(zt,"__esModule",{value:!0});var qt=zt.default=function(e,t){var n=null;if(!e||typeof e!="string")return n;var o=(0,Ce.default)(e),s=typeof t=="function";return o.forEach(function(r){if(r.type==="declaration"){var i=r.property,h=r.value;s?t(i,h,r):h&&((n=n||{})[i]=h)}}),n},Ce=$e(function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,o=1;function s(a){var c=a.match(ve);c&&(n+=c.length);var v=a.lastIndexOf(`
`);o=~v?a.length-v:o+a.length}function r(){var a={line:n,column:o};return function(c){return c.position=new i(a),p(),c}}function i(a){this.start=a,this.end={line:n,column:o},this.source=t.source}function h(a){var c=new Error(t.source+":"+n+":"+o+": "+a);if(c.reason=a,c.filename=t.source,c.line=n,c.column=o,c.source=e,!t.silent)throw c}function u(a){var c=a.exec(e);if(c){var v=c[0];return s(v),e=e.slice(v.length),c}}function p(){u(me)}function g(a){var c;for(a=a||[];c=$();)c!==!1&&a.push(c);return a}function $(){var a=r();if(e.charAt(0)=="/"&&e.charAt(1)=="*"){for(var c=2;E!=e.charAt(c)&&(e.charAt(c)!="*"||e.charAt(c+1)!="/");)++c;if(c+=2,E===e.charAt(c-1))return h("End of comment missing");var v=e.slice(2,c-2);return o+=2,s(v),e=e.slice(c),o+=2,a({type:"comment",comment:v})}}function A(){var a=r(),c=u(ye);if(c){if($(),!u(be))return h("property missing ':'");var v=u(we),x=a({type:"declaration",property:Bt(c[0].replace(Ot,E)),value:v?Bt(v[0].replace(Ot,E)):E});return u(ke),x}}return i.prototype.content=e,p(),function(){var a,c=[];for(g(c);a=A();)a!==!1&&(c.push(a),g(c));return c}()});const Ae=qt.default||qt,je=/\d/,Re=["-","_","/","."];function Oe(e=""){if(!je.test(e))return e!==e.toLowerCase()}function Dt(e){return e?function(t){const n=[];let o,s,r="";for(const i of t){const h=Re.includes(i);if(h===!0){n.push(r),r="",o=void 0;continue}const u=Oe(i);if(s===!1){if(o===!1&&u===!0){n.push(r),r=i,o=u;continue}if(o===!0&&u===!1&&r.length>1){const p=r.at(-1);n.push(r.slice(0,Math.max(0,r.length-1))),r=p+i,o=u;continue}}r+=i,o=u,s=h}return n.push(r),n}(e).map(t=>function(n){return n?n[0].toUpperCase()+n.slice(1):""}(t)).join(""):""}function et(e){if(!e)return{};const t={};return Ae(e,function(n,o){var s;n.startsWith("-moz-")||n.startsWith("-webkit-")||n.startsWith("-ms-")||n.startsWith("-o-")?t[Dt(n)]=o:n.startsWith("--")?t[n]=o:t[s=n,function(r){return r?r[0].toLowerCase()+r.slice(1):""}(Dt(s||""))]=o}),t}function Be(...e){return(...t)=>{for(const n of e)typeof n=="function"&&n(...t)}}const qe=function(e,t){const n=RegExp(e,"g");return o=>{if(typeof o!="string")throw new TypeError("expected an argument of type string, but got "+typeof o);return o.match(n)?o.replace(n,t):o}}(/[A-Z]/,e=>`-${e.toLowerCase()}`);function Lt(e={}){return function(t){if(!t||typeof t!="object"||Array.isArray(t))throw new TypeError("expected an argument of type object, but got "+typeof t);return Object.keys(t).map(n=>`${qe(n)}: ${t[n]};`).join(`
`)}(e).replace(`
`," ")}const De=Lt({position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",transform:"translateX(-100%)"}),Ee=new Set(["onabort","onanimationcancel","onanimationend","onanimationiteration","onanimationstart","onauxclick","onbeforeinput","onbeforetoggle","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncompositionend","oncompositionstart","oncompositionupdate","oncontextlost","oncontextmenu","oncontextrestored","oncopy","oncuechange","oncut","ondblclick","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","onfocusin","onfocusout","onformdata","ongotpointercapture","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onload","onloadeddata","onloadedmetadata","onloadstart","onlostpointercapture","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onpaste","onpause","onplay","onplaying","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointermove","onpointerout","onpointerover","onpointerup","onprogress","onratechange","onreset","onresize","onscroll","onscrollend","onsecuritypolicyviolation","onseeked","onseeking","onselect","onselectionchange","onselectstart","onslotchange","onstalled","onsubmit","onsuspend","ontimeupdate","ontoggle","ontouchcancel","ontouchend","ontouchmove","ontouchstart","ontransitioncancel","ontransitionend","ontransitionrun","ontransitionstart","onvolumechange","onwaiting","onwebkitanimationend","onwebkitanimationiteration","onwebkitanimationstart","onwebkittransitionend","onwheel"]);function Pe(e){return Ee.has(e)}function St(...e){const t={...e[0]};for(let n=1;n<e.length;n++){const o=e[n];if(o){for(const s of Object.keys(o)){const r=t[s],i=o[s],h=typeof r=="function",u=typeof i=="function";if(h&&Pe(s)){const p=r,g=i;t[s]=ge(p,g)}else if(h&&u)t[s]=Be(r,i);else if(s==="class"){const p=ft(r),g=ft(i);p&&g?t[s]=pt(r,i):p?t[s]=pt(r):g&&(t[s]=pt(i))}else if(s==="style"){const p=typeof r=="object",g=typeof i=="object",$=typeof r=="string",A=typeof i=="string";if(p&&g)t[s]={...r,...i};else if(p&&A){const a=et(i);t[s]={...r,...a}}else if($&&g){const a=et(r);t[s]={...a,...i}}else if($&&A){const a=et(r),c=et(i);t[s]={...a,...c}}else p?t[s]=r:g?t[s]=i:$?t[s]=r:A&&(t[s]=i)}else t[s]=i!==void 0?i:r}for(const s of Object.getOwnPropertySymbols(o)){const r=t[s],i=o[s];t[s]=i!==void 0?i:r}}}return typeof t.style=="object"&&(t.style=Lt(t.style).replaceAll(`
`," ")),t.hidden!==!0&&(t.hidden=void 0,delete t.hidden),t.disabled!==!0&&(t.disabled=void 0,delete t.disabled),t}const ze=typeof window<"u"?window:void 0;var z,N,Et;new(Et=class{constructor(e={}){y(this,z);y(this,N);const{window:t=ze,document:n=t==null?void 0:t.document}=e;t!==void 0&&(D(this,z,n),D(this,N,de(o=>{const s=At(t,"focusin",o),r=At(t,"focusout",o);return()=>{s(),r()}})))}get current(){var e;return(e=l(this,N))==null||e.call(this),l(this,z)?function(t){let n=t.activeElement;for(;n!=null&&n.shadowRoot;){const o=n.shadowRoot.activeElement;if(o===n)break;n=o}return n}(l(this,z)):null}},z=new WeakMap,N=new WeakMap,Et);var H,q;class _t{constructor(t){y(this,H);y(this,q);D(this,H,t),D(this,q,Symbol(t))}get key(){return l(this,q)}exists(){return Qt(l(this,q))}get(){const t=jt(l(this,q));if(t===void 0)throw new Error(`Context "${l(this,H)}" not found`);return t}getOr(t){const n=jt(l(this,q));return n===void 0?t:n}set(t){return Yt(l(this,q),t)}}H=new WeakMap,q=new WeakMap;function Wt(e,t,n,o={}){const{lazy:s=!1}=o;let r=!s,i=Array.isArray(e)?[]:void 0;(function(h,u){switch(h){case"post":ee(u);break;case"pre":te(u)}})(t,()=>{const h=Array.isArray(e)?e.map(p=>p()):e();if(!r)return r=!0,void(i=h);const u=ot(()=>n(h,i));return i=h,u})}function at(e,t,n){Wt(e,"post",t,n)}function Le(e){return e?"true":"false"}function Se(e){return e?"":void 0}function _e(e){return e?"":void 0}at.pre=function(e,t,n){Wt(e,"pre",t,n)};var L,I;class We{constructor(t){y(this,L);y(this,I);P(this,"attrs");D(this,L,t.getVariant?t.getVariant():null),D(this,I,l(this,L)?`data-${l(this,L)}-`:`data-${t.component}-`),this.getAttr=this.getAttr.bind(this),this.selector=this.selector.bind(this),this.attrs=Object.fromEntries(t.parts.map(n=>[n,this.getAttr(n)]))}getAttr(t,n){return n?`data-${n}-${t}`:`${l(this,I)}${t}`}selector(t,n){return`[${this.getAttr(t,n)}]`}}L=new WeakMap,I=new WeakMap;const Me=function(e){const t=new We(e);return{...t.attrs,selector:t.selector,getAttr:t.getAttr}}({component:"checkbox",parts:["root","group","group-label","input"]}),Ve=new _t("Checkbox.Group"),Mt=new _t("Checkbox.Root");var Z,F,T,G,S,nt,X,U;const kt=class kt{constructor(t,n){y(this,S);P(this,"opts");P(this,"group");y(this,Z,k(()=>this.group&&this.group.opts.name.current?this.group.opts.name.current:this.opts.name.current));y(this,F,k(()=>!(!this.group||!this.group.opts.required.current)||this.opts.required.current));y(this,T,k(()=>!(!this.group||!this.group.opts.disabled.current)||this.opts.disabled.current));y(this,G,k(()=>!(!this.group||!this.group.opts.readonly.current)||this.opts.readonly.current));P(this,"attachment");y(this,X,k(()=>({checked:this.opts.checked.current,indeterminate:this.opts.indeterminate.current})));y(this,U,k(()=>{return{id:this.opts.id.current,role:"checkbox",type:this.opts.type.current,disabled:this.trueDisabled,"aria-checked":(n=this.opts.checked.current,o=this.opts.indeterminate.current,o?"mixed":n?"true":"false"),"aria-required":(t=this.trueRequired,t?"true":"false"),"aria-readonly":Le(this.trueReadonly),"data-disabled":Se(this.trueDisabled),"data-readonly":_e(this.trueReadonly),"data-state":Ne(this.opts.checked.current,this.opts.indeterminate.current),[Me.root]:"",onclick:this.onclick,onkeydown:this.onkeydown,...this.attachment};var t,n,o}));var o,s;this.opts=t,this.group=n,this.attachment=(o=this.opts.ref,{[he()]:r=>d.isBox(o)?(o.current=r,ot(()=>s==null?void 0:s(r)),()=>{"isConnected"in r&&r.isConnected||(o.current=null)}):(o(r),ot(()=>s==null?void 0:s(r)),()=>{"isConnected"in r&&r.isConnected||o(null)})}),this.onkeydown=this.onkeydown.bind(this),this.onclick=this.onclick.bind(this),at.pre([()=>{var r;return ue((r=this.group)==null?void 0:r.opts.value.current)},()=>this.opts.value.current],([r,i])=>{r&&i&&(this.opts.checked.current=r.includes(i))}),at.pre(()=>this.opts.checked.current,r=>{var i,h;this.group&&(r?(i=this.group)==null||i.addValue(this.opts.value.current):(h=this.group)==null||h.removeValue(this.opts.value.current))})}static create(t,n=null){return Mt.set(new kt(t,n))}get trueName(){return f(l(this,Z))}set trueName(t){R(l(this,Z),t)}get trueRequired(){return f(l(this,F))}set trueRequired(t){R(l(this,F),t)}get trueDisabled(){return f(l(this,T))}set trueDisabled(t){R(l(this,T),t)}get trueReadonly(){return f(l(this,G))}set trueReadonly(t){R(l(this,G),t)}onkeydown(t){this.trueDisabled||this.trueReadonly||(t.key==="Enter"&&t.preventDefault(),t.key===" "&&(t.preventDefault(),tt(this,S,nt).call(this)))}onclick(t){this.trueDisabled||this.trueReadonly||(this.opts.type.current!=="submit"?(t.preventDefault(),tt(this,S,nt).call(this)):tt(this,S,nt).call(this))}get snippetProps(){return f(l(this,X))}set snippetProps(t){R(l(this,X),t)}get props(){return f(l(this,U))}set props(t){R(l(this,U),t)}};Z=new WeakMap,F=new WeakMap,T=new WeakMap,G=new WeakMap,S=new WeakSet,nt=function(){this.opts.indeterminate.current?(this.opts.indeterminate.current=!1,this.opts.checked.current=!0):this.opts.checked.current=!this.opts.checked.current},X=new WeakMap,U=new WeakMap;let vt=kt;var J,K,Q;const xt=class xt{constructor(t){P(this,"root");y(this,J,k(()=>this.root.group?!(this.root.opts.value.current===void 0||!this.root.group.opts.value.current.includes(this.root.opts.value.current)):this.root.opts.checked.current));y(this,K,k(()=>!!this.root.trueName));y(this,Q,k(()=>({type:"checkbox",checked:this.root.opts.checked.current===!0,disabled:this.root.trueDisabled,required:this.root.trueRequired,name:this.root.trueName,value:this.root.opts.value.current,readonly:this.root.trueReadonly})));this.root=t}static create(){return new xt(Mt.get())}get trueChecked(){return f(l(this,J))}set trueChecked(t){R(l(this,J),t)}get shouldRender(){return f(l(this,K))}set shouldRender(t){R(l(this,K),t)}get props(){return f(l(this,Q))}set props(t){R(l(this,Q),t)}};J=new WeakMap,K=new WeakMap,Q=new WeakMap;let mt=xt;function Ne(e,t){return t?"indeterminate":e?"checked":"unchecked"}var He=Y("<input/>"),Ie=Y("<input/>");function Ze(e,t){st(t,!1);const n=mt.create();ne();var o=it(),s=M(o),r=i=>{(function(h,u){st(u,!0);let p=w(u,"value",15),g=wt(u,["$$slots","$$events","$$legacy","value"]);const $=k(()=>St(g,{"aria-hidden":"true",tabindex:-1,style:De}));var A=it(),a=M(A),c=x=>{var m=He();rt(m,()=>({...f($),value:p()})),j(x,m)},v=x=>{var m=Ie();rt(m,()=>({...f($)})),le(m,p),j(x,m)};V(a,x=>{f($).type==="checkbox"?x(c):x(v,!1)}),j(h,A),ct()})(i,Pt(()=>n.props))};V(s,i=>{n.shouldRender&&i(r)}),j(e,o),ct()}var Fe=Y("<button><!></button>"),Te=Y("<!> <!>",1);function Ge(e,t){const n=oe();st(t,!0);let o=w(t,"checked",15,!1),s=w(t,"ref",15,null),r=w(t,"disabled",3,!1),i=w(t,"required",3,!1),h=w(t,"name",19,()=>{}),u=w(t,"value",3,"on"),p=w(t,"id",19,()=>`bits-${n}`),g=w(t,"indeterminate",15,!1),$=w(t,"type",3,"button"),A=wt(t,["$$slots","$$events","$$legacy","checked","ref","onCheckedChange","children","disabled","required","name","value","id","indeterminate","onIndeterminateChange","child","type","readonly"]);const a=Ve.getOr(null);a&&u()&&(a.opts.value.current.includes(u())?o(!0):o(!1)),at.pre(()=>u(),()=>{a&&u()&&(a.opts.value.current.includes(u())?o(!0):o(!1))});const c=vt.create({checked:d.with(()=>o(),b=>{var C;o(b),(C=t.onCheckedChange)==null||C.call(t,b)}),disabled:d.with(()=>r()??!1),required:d.with(()=>i()),name:d.with(()=>h()),value:d.with(()=>u()),id:d.with(()=>p()),ref:d.with(()=>s(),b=>s(b)),indeterminate:d.with(()=>g(),b=>{var C;g(b),(C=t.onIndeterminateChange)==null||C.call(t,b)}),type:d.with(()=>$()),readonly:d.with(()=>!!t.readonly)},a),v=k(()=>St({...A},c.props));var x=Te(),m=M(x),O=b=>{var C=it(),B=M(C),_=se(()=>({props:f(v),...c.snippetProps}));Rt(B,()=>t.child,()=>f(_)),j(b,C)},ut=b=>{var C=Fe();rt(C,()=>({...f(v)}));var B=bt(C);Rt(B,()=>t.children??ie,()=>c.snippetProps),j(b,C)};V(m,b=>{t.child?b(O):b(ut,!1)}),Ze(re(m,2),{}),j(e,x),ct()}var Xe=yt('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.2892 4.96914C15.6744 5.221 15.7825 5.73746 15.5307 6.12266L9.86399 14.7894C9.73044 14.9936 9.51408 15.129 9.27203 15.1599C9.02996 15.1908 8.78652 15.114 8.60595 14.9499L4.93928 11.6166C4.59873 11.307 4.57364 10.7799 4.88323 10.4394C5.19281 10.0988 5.71985 10.0737 6.0604 10.3833L9.00389 13.0592L14.1357 5.21058C14.3876 4.82538 14.904 4.71728 15.2892 4.96914Z" fill="currentColor"></path></svg>'),Ue=yt('<svg width="6" height="2" viewBox="0 0 6 2" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.666626 0.999992C0.666626 0.742261 0.875562 0.533325 1.13329 0.533325H4.86663C5.12436 0.533325 5.33329 0.742261 5.33329 0.999992C5.33329 1.25772 5.12436 1.46666 4.86663 1.46666H1.13329C0.875562 1.46666 0.666626 1.25772 0.666626 0.999992Z" fill="currentColor"></path></svg>'),Je=Y('<div data-slot="checkbox-indicator" class="c-checkbox-augment__indicator svelte-99adf3"><!></div>');function on(e,t){st(t,!0);let n=w(t,"ref",15,null),o=w(t,"checked",15,!1),s=w(t,"indeterminate",15,!1),r=w(t,"size",3,2),i=w(t,"variant",3,"primary"),h=w(t,"state",19,()=>{}),u=wt(t,["$$slots","$$events","$$legacy","ref","checked","indeterminate","size","variant","state","class"]);const p=k(()=>`c-checkbox-augment--size-${r()}`),g=k(()=>`c-checkbox-augment--variant-${i()}`),$=k(()=>h()==="hover"?"c-checkbox-augment--hover":h()==="active"?"c-checkbox-augment--active":"");var A=it(),a=M(A);const c=k(()=>["c-checkbox-augment",f(p),f(g),f($),t.class]);{const v=(x,m)=>{var O=Je(),ut=bt(O),b=B=>{(function(_){var lt=Xe();j(_,lt)})(B)},C=(B,_)=>{var lt=ht=>{(function(Vt){var Nt=Ue();j(Vt,Nt)})(ht)};V(B,ht=>{m!=null&&m().indeterminate&&ht(lt)},_)};V(ut,B=>{m!=null&&m().checked?B(b):B(C,!1)}),j(x,O)};ce(a,()=>Ge,(x,m)=>{m(x,Pt({"data-slot":"checkbox",get class(){return f(c)}},()=>u,{get ref(){return n()},set ref(O){n(O)},get checked(){return o()},set checked(O){o(O)},get indeterminate(){return s()},set indeterminate(O){s(O)},children:v,$$slots:{default:!0}}))})}j(e,A),ct()}export{on as C,nn as a};
