import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AICodingAssistant, AssistantMode } from '../core/AICodingAssistant';
import { AgentSystem } from '../core/AgentSystem';

export interface WebviewMessage {
    type: string;
    data?: any;
}

/**
 * Webview provider that integrates Augment's beautiful UI
 * Supports chat, agent, and auto modes
 */
export class AugmentWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'augment-ai-assistant';
    private _view?: vscode.WebviewView;
    private _extensionUri: vscode.Uri;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private aiAssistant: AICodingAssistant,
        private agentSystem: AgentSystem
    ) {
        this._extensionUri = context.extensionUri;
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri,
                vscode.Uri.joinPath(this._extensionUri, 'augment-main', 'extension', 'common-webviews')
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            (message: WebviewMessage) => this.handleMessage(message),
            undefined,
            []
        );

        console.log('Augment webview initialized');
    }

    private async handleMessage(message: WebviewMessage): Promise<void> {
        try {
            switch (message.type) {
                case 'chat-message':
                    await this.handleChatMessage(message.data);
                    break;
                case 'set-mode':
                    await this.handleSetMode(message.data);
                    break;
                case 'get-models':
                    await this.handleGetModels();
                    break;
                case 'set-model':
                    await this.handleSetModel(message.data);
                    break;
                case 'set-provider':
                    await this.handleSetProvider(message.data);
                    break;
                case 'clear-history':
                    await this.handleClearHistory();
                    break;
                case 'agent-task':
                    await this.handleAgentTask(message.data);
                    break;
                default:
                    console.log(`Unknown message type: ${message.type}`);
            }
        } catch (error) {
            console.error(`Error handling message: ${error}`);
            this.sendMessage({
                type: 'error',
                data: { message: `Error: ${error}` }
            });
        }
    }

    private async handleChatMessage(data: { message: string, mode?: AssistantMode }): Promise<void> {
        const { message, mode } = data;
        
        if (mode) {
            this.aiAssistant.setMode(mode);
        }

        const response = await this.aiAssistant.processMessage(message);
        
        this.sendMessage({
            type: 'chat-response',
            data: {
                message: response.message,
                mode: response.mode,
                success: response.success
            }
        });
    }

    private async handleSetMode(data: { mode: AssistantMode }): Promise<void> {
        this.aiAssistant.setMode(data.mode);
        this.sendMessage({
            type: 'mode-changed',
            data: { mode: data.mode }
        });
    }

    private async handleGetModels(): Promise<void> {
        try {
            const models = await this.aiAssistant.getAvailableModels();
            this.sendMessage({
                type: 'models-list',
                data: { models }
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                data: { message: `Failed to get models: ${error}` }
            });
        }
    }

    private async handleSetModel(data: { model: string }): Promise<void> {
        try {
            await this.aiAssistant.setModel(data.model);
            this.sendMessage({
                type: 'model-changed',
                data: { model: data.model }
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                data: { message: `Failed to set model: ${error}` }
            });
        }
    }

    private async handleSetProvider(data: { provider: 'ollama' | 'gemini' }): Promise<void> {
        try {
            await this.aiAssistant.switchProvider(data.provider);
            this.sendMessage({
                type: 'provider-changed',
                data: { provider: data.provider }
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                data: { message: `Failed to switch provider: ${error}` }
            });
        }
    }

    private async handleClearHistory(): Promise<void> {
        this.aiAssistant.clearHistory();
        this.sendMessage({
            type: 'history-cleared',
            data: {}
        });
    }

    private async handleAgentTask(data: { instruction: string }): Promise<void> {
        try {
            const task = await this.agentSystem.executeTask(data.instruction);
            this.sendMessage({
                type: 'agent-task-result',
                data: { task }
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                data: { message: `Agent task failed: ${error}` }
            });
        }
    }

    private sendMessage(message: WebviewMessage): void {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Try to load the main-panel.html from augment-main
        const augmentWebviewsPath = path.join(this._extensionUri.fsPath, 'augment-main', 'extension', 'common-webviews');
        const mainPanelPath = path.join(augmentWebviewsPath, 'main-panel.html');
        
        if (fs.existsSync(mainPanelPath)) {
            let html = fs.readFileSync(mainPanelPath, 'utf8');
            
            // Update resource URIs to work with VSCode webview
            const assetsPath = vscode.Uri.joinPath(this._extensionUri, 'augment-main', 'extension', 'common-webviews', 'assets');
            const assetsUri = webview.asWebviewUri(assetsPath);
            
            // Replace asset paths
            html = html.replace(/assets\//g, `${assetsUri}/`);
            
            // Add our custom integration script
            const integrationScript = this.getIntegrationScript();
            html = html.replace('</body>', `${integrationScript}</body>`);
            
            return html;
        }
        
        // Fallback to simple HTML if augment files not found
        return this.getFallbackHtml(webview);
    }

    private getIntegrationScript(): string {
        return `
        <script>
            const vscode = acquireVsCodeApi();
            
            // Integration with VSCode extension
            window.sendToExtension = function(type, data) {
                vscode.postMessage({ type, data });
            };
            
            // Listen for messages from extension
            window.addEventListener('message', event => {
                const message = event.data;
                if (window.handleExtensionMessage) {
                    window.handleExtensionMessage(message);
                }
            });
            
            // Initialize AI assistant integration
            window.aiAssistant = {
                sendMessage: (message, mode) => {
                    sendToExtension('chat-message', { message, mode });
                },
                setMode: (mode) => {
                    sendToExtension('set-mode', { mode });
                },
                getModels: () => {
                    sendToExtension('get-models');
                },
                setModel: (model) => {
                    sendToExtension('set-model', { model });
                },
                setProvider: (provider) => {
                    sendToExtension('set-provider', { provider });
                },
                clearHistory: () => {
                    sendToExtension('clear-history');
                },
                executeAgentTask: (instruction) => {
                    sendToExtension('agent-task', { instruction });
                }
            };
        </script>`;
    }

    private getFallbackHtml(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Coding Assistant</title>
            <style>
                body { 
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                }
                .mode-selector {
                    margin-bottom: 20px;
                }
                .mode-button {
                    margin-right: 10px;
                    padding: 8px 16px;
                    border: 1px solid var(--vscode-button-border);
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    cursor: pointer;
                }
                .mode-button.active {
                    background: var(--vscode-button-hoverBackground);
                }
                .chat-container {
                    border: 1px solid var(--vscode-panel-border);
                    height: 300px;
                    overflow-y: auto;
                    padding: 10px;
                    margin-bottom: 10px;
                }
                .input-container {
                    display: flex;
                    gap: 10px;
                }
                .message-input {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                }
                .send-button {
                    padding: 8px 16px;
                    border: 1px solid var(--vscode-button-border);
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    cursor: pointer;
                }
            </style>
        </head>
        <body>
            <h2>AI Coding Assistant</h2>
            
            <div class="mode-selector">
                <button class="mode-button active" onclick="setMode('chat')">Chat</button>
                <button class="mode-button" onclick="setMode('agent')">Agent</button>
                <button class="mode-button" onclick="setMode('auto')">Auto</button>
            </div>
            
            <div class="chat-container" id="chatContainer">
                <p>Welcome! Select a mode and start chatting.</p>
            </div>
            
            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="Type your message...">
                <button class="send-button" onclick="sendMessage()">Send</button>
            </div>
            
            ${this.getIntegrationScript()}
            
            <script>
                let currentMode = 'chat';
                
                function setMode(mode) {
                    currentMode = mode;
                    document.querySelectorAll('.mode-button').forEach(btn => btn.classList.remove('active'));
                    event.target.classList.add('active');
                    window.aiAssistant.setMode(mode);
                }
                
                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    if (message) {
                        addMessageToChat('You', message);
                        window.aiAssistant.sendMessage(message, currentMode);
                        input.value = '';
                    }
                }
                
                function addMessageToChat(sender, message) {
                    const container = document.getElementById('chatContainer');
                    const messageDiv = document.createElement('div');
                    messageDiv.innerHTML = '<strong>' + sender + ':</strong> ' + message;
                    messageDiv.style.marginBottom = '10px';
                    container.appendChild(messageDiv);
                    container.scrollTop = container.scrollHeight;
                }
                
                // Handle extension messages
                window.handleExtensionMessage = function(message) {
                    switch (message.type) {
                        case 'chat-response':
                            addMessageToChat('Assistant', message.data.message);
                            break;
                        case 'error':
                            addMessageToChat('Error', message.data.message);
                            break;
                    }
                };
                
                // Enter key support
                document.getElementById('messageInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            </script>
        </body>
        </html>`;
    }

    public updateAIAssistant(aiAssistant: AICodingAssistant): void {
        this.aiAssistant = aiAssistant;
    }

    public updateAgentSystem(agentSystem: AgentSystem): void {
        this.agentSystem = agentSystem;
    }

    // Methods called by extension.ts
    public show(): void {
        if (this._view) {
            this._view.show();
        }
    }

    public clearHistory(): void {
        this.aiAssistant.clearHistory();
        this.sendMessage({
            type: 'history-cleared',
            data: {}
        });
    }

    public switchMode(mode: 'chat' | 'agent' | 'auto'): void {
        this.aiAssistant.setMode(mode);
        this.sendMessage({
            type: 'mode-changed',
            data: { mode }
        });
    }

    public dispose(): void {
        this._view = undefined;
    }
}
