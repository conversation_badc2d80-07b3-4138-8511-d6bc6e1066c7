import * as vscode from 'vscode';
import { ConfigurationManager } from '../services/configurationManager';
import { AIProviderFactory } from '../services/aiProviderFactory';
import { AIProvider } from '../services/aiProviderInterface';

export type AssistantMode = 'chat' | 'agent' | 'auto';

export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
}

export interface AssistantResponse {
    message: string;
    mode: AssistantMode;
    success: boolean;
    metadata?: any;
}

/**
 * Main AI Coding Assistant class that orchestrates all AI interactions
 * Supports multiple modes: chat, agent, and auto
 */
export class AICodingAssistant {
    private currentProvider: AIProvider;
    private currentMode: AssistantMode = 'chat';
    private chatHistory: ChatMessage[] = [];
    
    constructor(
        private configManager: ConfigurationManager,
        private outputChannel: vscode.OutputChannel
    ) {}

    async initialize(): Promise<void> {
        try {
            const providerType = this.configManager.getAiProvider();
            this.currentProvider = AIProviderFactory.createProvider(providerType, this.configManager);
            
            if (this.currentProvider.initialize) {
                await this.currentProvider.initialize();
            }
            
            this.outputChannel.appendLine(`Initialized with ${providerType} provider`);
        } catch (error) {
            this.outputChannel.appendLine(`Failed to initialize provider: ${error}`);
            throw error;
        }
    }

    async switchProvider(providerType: 'ollama' | 'gemini'): Promise<void> {
        try {
            await this.configManager.setAiProvider(providerType);
            this.currentProvider = AIProviderFactory.createProvider(providerType, this.configManager);
            
            if (this.currentProvider.initialize) {
                await this.currentProvider.initialize();
            }
            
            this.outputChannel.appendLine(`Switched to ${providerType} provider`);
        } catch (error) {
            this.outputChannel.appendLine(`Failed to switch provider: ${error}`);
            throw error;
        }
    }

    async setModel(modelName: string): Promise<void> {
        if (this.configManager.getAiProvider() === 'ollama') {
            await this.configManager.setDefaultModel(modelName);
            await this.configManager.setChatModel(modelName);
            this.outputChannel.appendLine(`Set model to ${modelName}`);
        }
    }

    async getAvailableModels(): Promise<string[]> {
        if (this.configManager.getAiProvider() === 'ollama') {
            // This would need to be implemented in the provider
            return [];
        }
        return [];
    }

    async validateProvider(): Promise<boolean> {
        try {
            // Simple validation - try to make a basic request
            const response = await this.currentProvider.generateResponse('Hello', []);
            return response !== null && response !== undefined;
        } catch (error) {
            this.outputChannel.appendLine(`Provider validation failed: ${error}`);
            return false;
        }
    }

    setMode(mode: AssistantMode): void {
        this.currentMode = mode;
        this.outputChannel.appendLine(`Switched to ${mode} mode`);
    }

    getMode(): AssistantMode {
        return this.currentMode;
    }

    async processMessage(message: string, context?: any): Promise<AssistantResponse> {
        try {
            // Add user message to history
            this.addToHistory('user', message);
            
            let response: string;
            
            switch (this.currentMode) {
                case 'chat':
                    response = await this.processChatMessage(message, context);
                    break;
                case 'agent':
                    response = await this.processAgentMessage(message, context);
                    break;
                case 'auto':
                    response = await this.processAutoMessage(message, context);
                    break;
                default:
                    response = await this.processChatMessage(message, context);
            }
            
            // Add assistant response to history
            this.addToHistory('assistant', response);
            
            return {
                message: response,
                mode: this.currentMode,
                success: true
            };
        } catch (error) {
            this.outputChannel.appendLine(`Error processing message: ${error}`);
            return {
                message: `Error: ${error}`,
                mode: this.currentMode,
                success: false
            };
        }
    }

    private async processChatMessage(message: string, context?: any): Promise<string> {
        // Simple chat mode - direct interaction with AI
        const systemPrompt = this.buildSystemPrompt('chat');
        return await this.currentProvider.generateResponse(message, this.chatHistory, systemPrompt);
    }

    private async processAgentMessage(message: string, context?: any): Promise<string> {
        // Agent mode - will be handled by AgentSystem
        const systemPrompt = this.buildSystemPrompt('agent');
        return await this.currentProvider.generateResponse(message, this.chatHistory, systemPrompt);
    }

    private async processAutoMessage(message: string, context?: any): Promise<string> {
        // Auto mode - intelligent routing between chat and agent
        const isComplexTask = this.isComplexTask(message);
        
        if (isComplexTask) {
            return await this.processAgentMessage(message, context);
        } else {
            return await this.processChatMessage(message, context);
        }
    }

    private isComplexTask(message: string): boolean {
        // Simple heuristics to determine if a task is complex
        const complexKeywords = [
            'create', 'build', 'implement', 'refactor', 'debug', 'fix', 'test',
            'analyze', 'optimize', 'migrate', 'setup', 'configure', 'deploy'
        ];
        
        const lowerMessage = message.toLowerCase();
        return complexKeywords.some(keyword => lowerMessage.includes(keyword));
    }

    private buildSystemPrompt(mode: AssistantMode): string {
        const basePrompt = `You are an AI coding assistant helping a software developer.`;
        
        switch (mode) {
            case 'chat':
                return `${basePrompt} Provide helpful, concise responses to coding questions and requests.`;
            case 'agent':
                return `${basePrompt} You have access to tools to analyze code, run commands, and make file changes. Break down complex tasks into steps.`;
            case 'auto':
                return `${basePrompt} Automatically determine the best approach for each request - simple chat or complex agent workflow.`;
            default:
                return basePrompt;
        }
    }

    private addToHistory(role: 'user' | 'assistant' | 'system', content: string): void {
        this.chatHistory.push({
            role,
            content,
            timestamp: new Date()
        });
        
        // Keep history manageable (last 20 messages)
        if (this.chatHistory.length > 20) {
            this.chatHistory = this.chatHistory.slice(-20);
        }
    }

    getChatHistory(): ChatMessage[] {
        return [...this.chatHistory];
    }

    clearHistory(): void {
        this.chatHistory = [];
        this.outputChannel.appendLine('Chat history cleared');
    }

    getCurrentProvider(): AIProvider {
        return this.currentProvider;
    }

    dispose(): void {
        if (this.currentProvider && this.currentProvider.dispose) {
            this.currentProvider.dispose();
        }
        this.chatHistory = [];
    }
}
