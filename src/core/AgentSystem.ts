import * as vscode from 'vscode';
import { AICodingAssistant, AssistantResponse } from './AICodingAssistant';
import { BashTool } from '../tools/BashTool';
import { StrReplaceTool } from '../tools/StrReplaceTool';
import { SequentialThinkingTool } from '../tools/SequentialThinkingTool';
import { CompleteTool } from '../tools/CompleteTool';

export interface AgentTask {
    id: string;
    instruction: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    result?: string;
    error?: string;
    tools_used: string[];
    created_at: Date;
    completed_at?: Date;
}

export interface ToolCall {
    tool_name: string;
    tool_input: any;
    result?: any;
    error?: string;
}

/**
 * Agent System that provides advanced capabilities using SWEBench tools
 * Integrates bash execution, file editing, and sequential thinking
 */
export class AgentSystem {
    private bashTool: BashTool;
    private strReplaceTool: StrReplaceTool;
    private sequentialThinkingTool: SequentialThinkingTool;
    private completeTool: CompleteTool;
    private activeTasks: Map<string, AgentTask> = new Map();
    private maxTurns = 10;

    constructor(
        private aiAssistant: AICodingAssistant,
        private outputChannel: vscode.OutputChannel
    ) {
        this.initializeTools();
    }

    private initializeTools(): void {
        // Initialize SWEBench tools
        this.bashTool = new BashTool({
            requireConfirmation: true,
            workspaceRoot: this.getWorkspaceRoot()
        });

        this.strReplaceTool = new StrReplaceTool({
            workspaceRoot: this.getWorkspaceRoot()
        });

        this.sequentialThinkingTool = new SequentialThinkingTool();
        this.completeTool = new CompleteTool();

        this.outputChannel.appendLine('Agent tools initialized');
    }

    private getWorkspaceRoot(): string | undefined {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return workspaceFolder?.uri.fsPath;
    }

    async executeTask(instruction: string): Promise<AgentTask> {
        const taskId = this.generateTaskId();
        const task: AgentTask = {
            id: taskId,
            instruction,
            status: 'pending',
            tools_used: [],
            created_at: new Date()
        };

        this.activeTasks.set(taskId, task);
        this.outputChannel.appendLine(`Starting agent task: ${taskId}`);

        try {
            task.status = 'running';
            const result = await this.runAgentLoop(instruction, task);
            
            task.status = 'completed';
            task.result = result;
            task.completed_at = new Date();
            
            this.outputChannel.appendLine(`Agent task completed: ${taskId}`);
            return task;
        } catch (error) {
            task.status = 'failed';
            task.error = error instanceof Error ? error.message : String(error);
            task.completed_at = new Date();
            
            this.outputChannel.appendLine(`Agent task failed: ${taskId} - ${task.error}`);
            return task;
        }
    }

    private async runAgentLoop(instruction: string, task: AgentTask): Promise<string> {
        let currentInstruction = instruction;
        let turnCount = 0;
        let conversationHistory: string[] = [];

        // Set assistant to agent mode
        this.aiAssistant.setMode('agent');

        while (turnCount < this.maxTurns) {
            turnCount++;
            this.outputChannel.appendLine(`Agent turn ${turnCount}/${this.maxTurns}`);

            // Get AI response with tool calling capability
            const response = await this.aiAssistant.processMessage(
                currentInstruction,
                { 
                    availableTools: this.getAvailableTools(),
                    conversationHistory 
                }
            );

            conversationHistory.push(`Turn ${turnCount}: ${response.message}`);

            // Check if the AI wants to use tools
            const toolCalls = this.extractToolCalls(response.message);
            
            if (toolCalls.length === 0) {
                // No tools called, task might be complete
                if (this.isTaskComplete(response.message)) {
                    return response.message;
                }
                // Continue with next turn
                currentInstruction = "Please continue or use appropriate tools to complete the task.";
                continue;
            }

            // Execute tool calls
            for (const toolCall of toolCalls) {
                try {
                    const toolResult = await this.executeTool(toolCall, task);
                    toolCall.result = toolResult;
                    conversationHistory.push(`Tool ${toolCall.tool_name}: ${toolResult}`);
                } catch (error) {
                    toolCall.error = error instanceof Error ? error.message : String(error);
                    conversationHistory.push(`Tool ${toolCall.tool_name} failed: ${toolCall.error}`);
                }
            }

            // Check if task is complete after tool execution
            if (this.completeTool.isComplete()) {
                return this.completeTool.getResult();
            }

            // Prepare next instruction based on tool results
            currentInstruction = this.buildNextInstruction(toolCalls);
        }

        return `Task completed after ${this.maxTurns} turns. Final state: ${conversationHistory[conversationHistory.length - 1]}`;
    }

    private getAvailableTools(): string[] {
        return ['bash', 'str_replace_editor', 'sequential_thinking', 'complete'];
    }

    private extractToolCalls(message: string): ToolCall[] {
        // Simple tool call extraction - in a real implementation, this would be more sophisticated
        const toolCalls: ToolCall[] = [];
        
        // Look for tool call patterns in the message
        const bashMatch = message.match(/```bash\n(.*?)\n```/s);
        if (bashMatch) {
            toolCalls.push({
                tool_name: 'bash',
                tool_input: { command: bashMatch[1].trim() }
            });
        }

        const editMatch = message.match(/```edit\n(.*?)\n```/s);
        if (editMatch) {
            // Parse edit command - simplified
            toolCalls.push({
                tool_name: 'str_replace_editor',
                tool_input: { command: 'str_replace', path: 'file.txt', old_str: '', new_str: editMatch[1] }
            });
        }

        return toolCalls;
    }

    private async executeTool(toolCall: ToolCall, task: AgentTask): Promise<string> {
        task.tools_used.push(toolCall.tool_name);
        
        switch (toolCall.tool_name) {
            case 'bash':
                return await this.bashTool.execute(toolCall.tool_input);
            case 'str_replace_editor':
                return await this.strReplaceTool.execute(toolCall.tool_input);
            case 'sequential_thinking':
                return await this.sequentialThinkingTool.execute(toolCall.tool_input);
            case 'complete':
                return await this.completeTool.execute(toolCall.tool_input);
            default:
                throw new Error(`Unknown tool: ${toolCall.tool_name}`);
        }
    }

    private isTaskComplete(message: string): boolean {
        const completionIndicators = [
            'task completed',
            'finished',
            'done',
            'complete',
            'successfully implemented',
            'no further action needed'
        ];
        
        const lowerMessage = message.toLowerCase();
        return completionIndicators.some(indicator => lowerMessage.includes(indicator));
    }

    private buildNextInstruction(toolCalls: ToolCall[]): string {
        const results = toolCalls.map(call => 
            call.error ? `${call.tool_name} failed: ${call.error}` : `${call.tool_name}: ${call.result}`
        ).join('\n');
        
        return `Based on the tool results:\n${results}\n\nPlease continue with the next step or complete the task.`;
    }

    private generateTaskId(): string {
        return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getActiveTask(taskId: string): AgentTask | undefined {
        return this.activeTasks.get(taskId);
    }

    getAllTasks(): AgentTask[] {
        return Array.from(this.activeTasks.values());
    }

    cancelTask(taskId: string): boolean {
        const task = this.activeTasks.get(taskId);
        if (task && task.status === 'running') {
            task.status = 'failed';
            task.error = 'Cancelled by user';
            task.completed_at = new Date();
            return true;
        }
        return false;
    }

    dispose(): void {
        // Clean up tools and tasks
        this.activeTasks.clear();
        this.outputChannel.appendLine('Agent system disposed');
    }
}
