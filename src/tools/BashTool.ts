import * as vscode from 'vscode';
import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';

export interface BashToolOptions {
    requireConfirmation?: boolean;
    workspaceRoot?: string;
    timeout?: number;
    bannedCommands?: string[];
}

export interface BashResult {
    success: boolean;
    output: string;
    error?: string;
    exitCode?: number;
}

/**
 * Bash tool for executing shell commands safely within VSCode
 * Based on SWEBench agent bash tool implementation
 */
export class BashTool {
    private workspaceRoot: string;
    private requireConfirmation: boolean;
    private timeout: number;
    private bannedCommands: string[];

    constructor(options: BashToolOptions = {}) {
        this.workspaceRoot = options.workspaceRoot || this.getDefaultWorkspaceRoot();
        this.requireConfirmation = options.requireConfirmation ?? true;
        this.timeout = options.timeout || 30000; // 30 seconds default
        this.bannedCommands = options.bannedCommands || [
            'rm -rf /',
            'sudo rm',
            'format',
            'mkfs',
            'dd if=',
            'shutdown',
            'reboot',
            'halt'
        ];
    }

    private getDefaultWorkspaceRoot(): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return workspaceFolder?.uri.fsPath || process.cwd();
    }

    async execute(input: { command: string }): Promise<string> {
        const { command } = input;

        // Validate command
        if (!command || command.trim().length === 0) {
            throw new Error('Command cannot be empty');
        }

        // Check for banned commands
        const lowerCommand = command.toLowerCase();
        for (const banned of this.bannedCommands) {
            if (lowerCommand.includes(banned.toLowerCase())) {
                throw new Error(`Command contains banned string: ${banned}`);
            }
        }

        // Request confirmation if required
        if (this.requireConfirmation) {
            const confirmed = await this.requestConfirmation(command);
            if (!confirmed) {
                return 'Command execution cancelled by user';
            }
        }

        try {
            const result = await this.executeCommand(command);
            return this.formatResult(result);
        } catch (error) {
            throw new Error(`Command execution failed: ${error}`);
        }
    }

    private async requestConfirmation(command: string): Promise<boolean> {
        const response = await vscode.window.showWarningMessage(
            `Execute command: ${command}`,
            { modal: true },
            'Execute',
            'Cancel'
        );
        return response === 'Execute';
    }

    private executeCommand(command: string): Promise<BashResult> {
        return new Promise((resolve, reject) => {
            const isWindows = process.platform === 'win32';
            const shell = isWindows ? 'cmd' : '/bin/bash';
            const args = isWindows ? ['/c', command] : ['-c', command];

            const child: ChildProcess = spawn(shell, args, {
                cwd: this.workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env }
            });

            let stdout = '';
            let stderr = '';

            child.stdout?.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr?.on('data', (data) => {
                stderr += data.toString();
            });

            const timeoutId = setTimeout(() => {
                child.kill('SIGTERM');
                reject(new Error(`Command timed out after ${this.timeout}ms`));
            }, this.timeout);

            child.on('close', (code) => {
                clearTimeout(timeoutId);
                
                const result: BashResult = {
                    success: code === 0,
                    output: stdout,
                    error: stderr,
                    exitCode: code || 0
                };

                resolve(result);
            });

            child.on('error', (error) => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }

    private formatResult(result: BashResult): string {
        let output = '';
        
        if (result.success) {
            output += `Command executed successfully (exit code: ${result.exitCode})\n`;
            if (result.output) {
                output += `Output:\n${result.output}`;
            }
        } else {
            output += `Command failed (exit code: ${result.exitCode})\n`;
            if (result.output) {
                output += `Output:\n${result.output}\n`;
            }
            if (result.error) {
                output += `Error:\n${result.error}`;
            }
        }

        return output.trim();
    }

    // Additional utility methods
    async listFiles(directory?: string): Promise<string> {
        const targetDir = directory || this.workspaceRoot;
        const command = process.platform === 'win32' ? `dir "${targetDir}"` : `ls -la "${targetDir}"`;
        return await this.execute({ command });
    }

    async getCurrentDirectory(): Promise<string> {
        const command = process.platform === 'win32' ? 'cd' : 'pwd';
        return await this.execute({ command });
    }

    async checkFileExists(filePath: string): Promise<boolean> {
        try {
            const fullPath = path.resolve(this.workspaceRoot, filePath);
            const command = process.platform === 'win32' 
                ? `if exist "${fullPath}" echo exists` 
                : `test -f "${fullPath}" && echo exists`;
            
            const result = await this.execute({ command });
            return result.includes('exists');
        } catch {
            return false;
        }
    }

    setWorkspaceRoot(newRoot: string): void {
        this.workspaceRoot = newRoot;
    }

    getWorkspaceRoot(): string {
        return this.workspaceRoot;
    }

    setBannedCommands(commands: string[]): void {
        this.bannedCommands = commands;
    }

    addBannedCommand(command: string): void {
        if (!this.bannedCommands.includes(command)) {
            this.bannedCommands.push(command);
        }
    }

    setRequireConfirmation(require: boolean): void {
        this.requireConfirmation = require;
    }

    setTimeout(timeoutMs: number): void {
        this.timeout = timeoutMs;
    }
}
