import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface StrReplaceToolOptions {
    workspaceRoot?: string;
    maxFileSize?: number;
    backupFiles?: boolean;
}

export interface FileOperation {
    command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';
    path: string;
    file_text?: string;
    old_str?: string;
    new_str?: string;
    insert_line?: number;
    view_range?: [number, number];
}

/**
 * String replacement and file editing tool
 * Based on SWEBench agent str_replace_tool implementation
 */
export class StrReplaceTool {
    private workspaceRoot: string;
    private maxFileSize: number;
    private backupFiles: boolean;
    private fileHistory: Map<string, string[]> = new Map();

    constructor(options: StrReplaceToolOptions = {}) {
        this.workspaceRoot = options.workspaceRoot || this.getDefaultWorkspaceRoot();
        this.maxFileSize = options.maxFileSize || 1024 * 1024; // 1MB default
        this.backupFiles = options.backupFiles ?? true;
    }

    private getDefaultWorkspaceRoot(): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        return workspaceFolder?.uri.fsPath || process.cwd();
    }

    async execute(operation: FileOperation): Promise<string> {
        const { command, path: filePath } = operation;
        const fullPath = this.resolveFilePath(filePath);

        // Validate path is within workspace
        if (!this.isPathInWorkspace(fullPath)) {
            throw new Error(`Path ${filePath} is outside workspace root: ${this.workspaceRoot}`);
        }

        switch (command) {
            case 'view':
                return await this.viewFile(fullPath, operation.view_range);
            case 'create':
                return await this.createFile(fullPath, operation.file_text || '');
            case 'str_replace':
                return await this.replaceString(fullPath, operation.old_str || '', operation.new_str || '');
            case 'insert':
                return await this.insertText(fullPath, operation.insert_line || 0, operation.new_str || '');
            case 'undo_edit':
                return await this.undoEdit(fullPath);
            default:
                throw new Error(`Unknown command: ${command}`);
        }
    }

    private resolveFilePath(filePath: string): string {
        if (path.isAbsolute(filePath)) {
            return filePath;
        }
        return path.resolve(this.workspaceRoot, filePath);
    }

    private isPathInWorkspace(fullPath: string): boolean {
        const relativePath = path.relative(this.workspaceRoot, fullPath);
        return !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
    }

    private async viewFile(fullPath: string, viewRange?: [number, number]): Promise<string> {
        try {
            if (!fs.existsSync(fullPath)) {
                throw new Error(`File does not exist: ${fullPath}`);
            }

            const stats = fs.statSync(fullPath);
            if (stats.isDirectory()) {
                return await this.listDirectory(fullPath);
            }

            if (stats.size > this.maxFileSize) {
                throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
            }

            const content = fs.readFileSync(fullPath, 'utf-8');
            const lines = content.split('\n');

            let displayContent: string;
            let startLine = 1;

            if (viewRange) {
                const [start, end] = viewRange;
                if (start < 1 || start > lines.length) {
                    throw new Error(`Invalid start line: ${start} (file has ${lines.length} lines)`);
                }
                
                const endLine = end === -1 ? lines.length : Math.min(end, lines.length);
                displayContent = lines.slice(start - 1, endLine).join('\n');
                startLine = start;
            } else {
                displayContent = content;
            }

            // Format with line numbers
            const numberedLines = displayContent.split('\n').map((line, index) => {
                const lineNum = startLine + index;
                return `${lineNum.toString().padStart(6)}  ${line}`;
            }).join('\n');

            return `Here's the result of running \`cat -n\` on ${path.relative(this.workspaceRoot, fullPath)}:\n${numberedLines}\nTotal lines in file: ${lines.length}`;
        } catch (error) {
            throw new Error(`Failed to view file: ${error}`);
        }
    }

    private async listDirectory(dirPath: string): Promise<string> {
        try {
            const items = fs.readdirSync(dirPath, { withFileTypes: true });
            const listing = items
                .filter(item => !item.name.startsWith('.'))
                .map(item => {
                    const type = item.isDirectory() ? 'DIR' : 'FILE';
                    return `${type.padEnd(4)} ${item.name}`;
                })
                .join('\n');

            return `Directory listing for ${path.relative(this.workspaceRoot, dirPath)}:\n${listing}`;
        } catch (error) {
            throw new Error(`Failed to list directory: ${error}`);
        }
    }

    private async createFile(fullPath: string, content: string): Promise<string> {
        try {
            if (fs.existsSync(fullPath)) {
                const existingContent = fs.readFileSync(fullPath, 'utf-8');
                if (existingContent.trim()) {
                    throw new Error(`File already exists and is not empty: ${fullPath}`);
                }
            }

            // Ensure directory exists
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            fs.writeFileSync(fullPath, content, 'utf-8');
            
            const relativePath = path.relative(this.workspaceRoot, fullPath);
            return `File created successfully at: ${relativePath}`;
        } catch (error) {
            throw new Error(`Failed to create file: ${error}`);
        }
    }

    private async replaceString(fullPath: string, oldStr: string, newStr: string): Promise<string> {
        try {
            if (!fs.existsSync(fullPath)) {
                throw new Error(`File does not exist: ${fullPath}`);
            }

            const content = fs.readFileSync(fullPath, 'utf-8');
            
            // Save backup if enabled
            if (this.backupFiles) {
                this.saveToHistory(fullPath, content);
            }

            // Handle empty old_str (replace entire file)
            if (!oldStr.trim()) {
                if (content.trim()) {
                    throw new Error('Cannot use empty old_str when file is not empty');
                }
                fs.writeFileSync(fullPath, newStr, 'utf-8');
                return `File ${path.relative(this.workspaceRoot, fullPath)} has been replaced entirely.`;
            }

            // Count occurrences
            const occurrences = (content.match(new RegExp(this.escapeRegExp(oldStr), 'g')) || []).length;
            
            if (occurrences === 0) {
                throw new Error(`String not found in file:\n\`\`\`\n${oldStr}\n\`\`\``);
            }
            
            if (occurrences > 1) {
                throw new Error(`Multiple occurrences found (${occurrences}). Please make old_str more specific.`);
            }

            // Perform replacement
            const newContent = content.replace(oldStr, newStr);
            fs.writeFileSync(fullPath, newContent, 'utf-8');

            // Create snippet showing the change
            const lines = newContent.split('\n');
            const replacementLine = content.split(oldStr)[0].split('\n').length - 1;
            const startLine = Math.max(0, replacementLine - 2);
            const endLine = Math.min(lines.length, replacementLine + newStr.split('\n').length + 2);
            
            const snippet = lines.slice(startLine, endLine).map((line, index) => {
                const lineNum = startLine + index + 1;
                return `${lineNum.toString().padStart(6)}  ${line}`;
            }).join('\n');

            const relativePath = path.relative(this.workspaceRoot, fullPath);
            return `The file ${relativePath} has been edited. Here's the result of running \`cat -n\` on a snippet of the edited file:\n${snippet}\nTotal lines in file: ${lines.length}`;
        } catch (error) {
            throw new Error(`Failed to replace string: ${error}`);
        }
    }

    private async insertText(fullPath: string, insertLine: number, text: string): Promise<string> {
        try {
            if (!fs.existsSync(fullPath)) {
                throw new Error(`File does not exist: ${fullPath}`);
            }

            const content = fs.readFileSync(fullPath, 'utf-8');
            const lines = content.split('\n');

            if (insertLine < 0 || insertLine > lines.length) {
                throw new Error(`Invalid insert line: ${insertLine} (file has ${lines.length} lines)`);
            }

            // Save backup if enabled
            if (this.backupFiles) {
                this.saveToHistory(fullPath, content);
            }

            // Insert text
            const newLines = text.split('\n');
            lines.splice(insertLine, 0, ...newLines);
            
            const newContent = lines.join('\n');
            fs.writeFileSync(fullPath, newContent, 'utf-8');

            // Create snippet showing the insertion
            const startLine = Math.max(0, insertLine - 2);
            const endLine = Math.min(lines.length, insertLine + newLines.length + 2);
            
            const snippet = lines.slice(startLine, endLine).map((line, index) => {
                const lineNum = startLine + index + 1;
                return `${lineNum.toString().padStart(6)}  ${line}`;
            }).join('\n');

            const relativePath = path.relative(this.workspaceRoot, fullPath);
            return `The file ${relativePath} has been edited. Here's the result of running \`cat -n\` on a snippet of the edited file:\n${snippet}\nTotal lines in file: ${lines.length}`;
        } catch (error) {
            throw new Error(`Failed to insert text: ${error}`);
        }
    }

    private async undoEdit(fullPath: string): Promise<string> {
        const history = this.fileHistory.get(fullPath);
        if (!history || history.length === 0) {
            throw new Error(`No edit history found for ${fullPath}`);
        }

        const previousContent = history.pop()!;
        fs.writeFileSync(fullPath, previousContent, 'utf-8');

        const relativePath = path.relative(this.workspaceRoot, fullPath);
        return `Last edit to ${relativePath} undone successfully.`;
    }

    private saveToHistory(fullPath: string, content: string): void {
        if (!this.fileHistory.has(fullPath)) {
            this.fileHistory.set(fullPath, []);
        }
        
        const history = this.fileHistory.get(fullPath)!;
        history.push(content);
        
        // Keep only last 10 versions
        if (history.length > 10) {
            history.shift();
        }
    }

    private escapeRegExp(string: string): string {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    setWorkspaceRoot(newRoot: string): void {
        this.workspaceRoot = newRoot;
    }

    getWorkspaceRoot(): string {
        return this.workspaceRoot;
    }

    clearHistory(): void {
        this.fileHistory.clear();
    }
}
