export interface CompleteInput {
    answer: string;
    summary?: string;
    confidence?: number;
    next_steps?: string[];
}

export interface TaskCompletion {
    answer: string;
    summary: string;
    confidence: number;
    next_steps: string[];
    completed_at: Date;
    execution_time?: number;
}

/**
 * Complete Tool for signaling task completion
 * Based on SWEBench agent complete_tool implementation
 */
export class CompleteTool {
    private isCompleted: boolean = false;
    private completionResult: TaskCompletion | null = null;
    private startTime: Date | null = null;

    constructor() {
        this.reset();
    }

    async execute(input: CompleteInput): Promise<string> {
        const { answer, summary, confidence, next_steps } = input;

        if (!answer || answer.trim().length === 0) {
            throw new Error('Answer is required to complete the task');
        }

        const executionTime = this.startTime ? Date.now() - this.startTime.getTime() : undefined;

        this.completionResult = {
            answer: answer.trim(),
            summary: summary || 'Task completed successfully',
            confidence: confidence ?? 0.8,
            next_steps: next_steps || [],
            completed_at: new Date(),
            execution_time: executionTime
        };

        this.isCompleted = true;

        return this.formatCompletionMessage();
    }

    private formatCompletionMessage(): string {
        if (!this.completionResult) {
            return 'Task completion failed - no result available';
        }

        const { answer, summary, confidence, next_steps, completed_at, execution_time } = this.completionResult;

        let message = `🎯 TASK COMPLETED\n\n`;
        message += `Answer: ${answer}\n\n`;
        message += `Summary: ${summary}\n\n`;
        message += `Confidence: ${(confidence * 100).toFixed(1)}%\n`;
        message += `Completed at: ${completed_at.toISOString()}\n`;

        if (execution_time) {
            message += `Execution time: ${(execution_time / 1000).toFixed(2)}s\n`;
        }

        if (next_steps.length > 0) {
            message += `\nNext Steps:\n`;
            next_steps.forEach((step, index) => {
                message += `${index + 1}. ${step}\n`;
            });
        }

        return message;
    }

    isComplete(): boolean {
        return this.isCompleted;
    }

    getResult(): string {
        if (!this.completionResult) {
            return 'No completion result available';
        }
        return this.completionResult.answer;
    }

    getCompletionDetails(): TaskCompletion | null {
        return this.completionResult ? { ...this.completionResult } : null;
    }

    reset(): void {
        this.isCompleted = false;
        this.completionResult = null;
        this.startTime = new Date();
    }

    // Utility methods
    setStartTime(time?: Date): void {
        this.startTime = time || new Date();
    }

    getExecutionTime(): number | null {
        if (!this.startTime || !this.completionResult) {
            return null;
        }
        return this.completionResult.execution_time || null;
    }

    validateCompletion(): boolean {
        return this.isCompleted && this.completionResult !== null && this.completionResult.answer.trim().length > 0;
    }

    // Static helper methods
    static createQuickCompletion(answer: string): CompleteInput {
        return {
            answer,
            summary: 'Task completed',
            confidence: 0.8,
            next_steps: []
        };
    }

    static createDetailedCompletion(
        answer: string,
        summary: string,
        confidence: number,
        nextSteps: string[]
    ): CompleteInput {
        return {
            answer,
            summary,
            confidence: Math.max(0, Math.min(1, confidence)), // Clamp between 0 and 1
            next_steps: nextSteps
        };
    }
}
