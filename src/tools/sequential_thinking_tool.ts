export interface ThinkingStep {
    step: number;
    thought: string;
    reasoning: string;
    timestamp: Date;
}

export interface ThinkingInput {
    thoughts: string[];
    context?: string;
}

/**
 * Sequential Thinking Tool for multi-step reasoning
 * Based on SWEBench agent sequential_thinking_tool implementation
 */
export class SequentialThinkingTool {
    private currentSession: ThinkingStep[] = [];
    private sessionHistory: ThinkingStep[][] = [];

    constructor() {}

    async execute(input: ThinkingInput): Promise<string> {
        const { thoughts, context } = input;

        if (!thoughts || thoughts.length === 0) {
            throw new Error('No thoughts provided for sequential thinking');
        }

        // Start new thinking session
        this.startNewSession();

        // Process each thought sequentially
        for (let i = 0; i < thoughts.length; i++) {
            const thought = thoughts[i];
            const step = await this.processThought(i + 1, thought, context);
            this.currentSession.push(step);
        }

        // Generate summary
        const summary = this.generateSummary();

        // Save session to history
        this.sessionHistory.push([...this.currentSession]);

        return summary;
    }

    private startNewSession(): void {
        this.currentSession = [];
    }

    private async processThought(stepNumber: number, thought: string, context?: string): Promise<ThinkingStep> {
        // Analyze the thought in context of previous steps
        const previousSteps = this.currentSession.map(s => s.thought).join(' -> ');

        let reasoning = `Step ${stepNumber}: ${thought}`;

        if (previousSteps) {
            reasoning += `\nBuilding on: ${previousSteps}`;
        }

        if (context) {
            reasoning += `\nContext: ${context}`;
        }

        return {
            step: stepNumber,
            thought: thought.trim(),
            reasoning,
            timestamp: new Date()
        };
    }

    private generateSummary(): string {
        if (this.currentSession.length === 0) {
            return 'No thoughts processed in this session.';
        }

        const steps = this.currentSession.map(step =>
            `${step.step}. ${step.thought}`
        ).join('\n');

        const reasoning = this.currentSession.map(step =>
            `Step ${step.step}: ${step.reasoning}`
        ).join('\n\n');

        return `Sequential Thinking Summary:

Thought Process:
${steps}

Detailed Reasoning:
${reasoning}

Conclusion: Based on the sequential analysis of ${this.currentSession.length} steps, the reasoning chain provides a structured approach to the problem.`;
    }

    getCurrentSession(): ThinkingStep[] {
        return [...this.currentSession];
    }

    getSessionHistory(): ThinkingStep[][] {
        return [...this.sessionHistory];
    }

    getLastSession(): ThinkingStep[] | null {
        if (this.sessionHistory.length === 0) {
            return null;
        }
        return [...this.sessionHistory[this.sessionHistory.length - 1]];
    }

    clearHistory(): void {
        this.sessionHistory = [];
        this.currentSession = [];
    }

    // Utility methods for analysis
    analyzeThoughtPattern(): string {
        if (this.sessionHistory.length === 0) {
            return 'No thinking sessions to analyze.';
        }

        const totalSessions = this.sessionHistory.length;
        const avgStepsPerSession = this.sessionHistory.reduce((sum, session) => sum + session.length, 0) / totalSessions;

        const commonPatterns = this.identifyCommonPatterns();

        return `Thinking Pattern Analysis:
- Total sessions: ${totalSessions}
- Average steps per session: ${avgStepsPerSession.toFixed(1)}
- Common patterns: ${commonPatterns}`;
    }

    private identifyCommonPatterns(): string {
        // Simple pattern identification
        const allThoughts = this.sessionHistory.flat().map(step => step.thought.toLowerCase());
        const wordFreq: { [key: string]: number } = {};

        allThoughts.forEach(thought => {
            const words = thought.split(/\s+/).filter(word => word.length > 3);
            words.forEach(word => {
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            });
        });

        const commonWords = Object.entries(wordFreq)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([word, freq]) => `${word}(${freq})`)
            .join(', ');

        return commonWords || 'No clear patterns identified';
    }
}
