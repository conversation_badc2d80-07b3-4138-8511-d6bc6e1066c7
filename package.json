{"name": "ai-coding-assistant", "displayName": "AI Coding Assistant", "description": "Advanced AI coding assistant with chat, agent, and auto modes. Supports Ollama and Gemini providers.", "version": "0.2.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Programming Languages"], "keywords": ["ai", "ollama", "gemini", "agent", "chat", "assistant", "coding", "swebench"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-assistant.openChat", "title": "Open AI Assistant", "category": "AI Assistant"}, {"command": "ai-assistant.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Set Chat Mode", "category": "AI Assistant"}, {"command": "ai-assistant.setAgentMode", "title": "Set Agent Mode", "category": "AI Assistant"}, {"command": "ai-assistant.set<PERSON><PERSON><PERSON><PERSON>", "title": "Set Auto Mode", "category": "AI Assistant"}, {"command": "ai-assistant.select<PERSON><PERSON><PERSON>", "title": "Select AI Provider", "category": "AI Assistant"}, {"command": "ai-assistant.select<PERSON><PERSON>l", "title": "Select Model", "category": "AI Assistant"}, {"command": "ai-assistant.configure<PERSON><PERSON><PERSON>", "title": "Configure Gemini API Key", "category": "AI Assistant"}, {"command": "ai-assistant.<PERSON><PERSON><PERSON><PERSON>", "title": "Clear Chat History", "category": "AI Assistant"}, {"command": "ai-assistant.executeAgentTask", "title": "Execute Agent Task", "category": "AI Assistant"}], "viewsContainers": {"activitybar": [{"id": "ai-assistant", "title": "AI Coding Assistant", "icon": "$(robot)"}]}, "views": {"ai-assistant": [{"id": "augment-ai-assistant", "name": "AI Assistant", "type": "webview", "when": "true"}]}, "viewsWelcome": [{"view": "augment-ai-assistant", "contents": "Welcome to AI Coding Assistant!\n\nChoose your mode:\n• **Chat** - Direct conversation with AI\n• **Agent** - Advanced task execution with tools\n• **Auto** - Intelligent mode selection\n\n[Select Provider](command:ai-assistant.selectProvider)\n[Configure Settings](command:workbench.action.openSettings?%5B%22ai-assistant%22%5D)"}], "configuration": {"title": "AI Coding Assistant", "properties": {"ai-assistant.provider": {"type": "string", "enum": ["ollama", "gemini"], "default": "ollama", "description": "AI provider to use (Ollama or Gemini)"}, "ai-assistant.ollamaServerUrl": {"type": "string", "default": "http://localhost:11434", "description": "Ollama server URL"}, "ai-assistant.defaultModel": {"type": "string", "default": "", "description": "Default <PERSON> model to use"}, "ai-assistant.chatModel": {"type": "string", "default": "", "description": "Model to use for chat conversations"}, "ai-assistant.geminiApiKey": {"type": "string", "default": "", "description": "Google Gemini API key (required for Gemini provider)"}, "ai-assistant.enableCompletions": {"type": "boolean", "default": true, "description": "Enable AI-powered code completions"}, "ai-assistant.completionDelay": {"type": "number", "default": 500, "description": "Delay in milliseconds before triggering completions"}, "ai-assistant.maxContextLines": {"type": "number", "default": 100, "description": "Maximum number of lines to include in context"}, "ai-assistant.defaultMode": {"type": "string", "enum": ["chat", "agent", "auto"], "default": "auto", "description": "Default assistant mode (chat, agent, or auto)"}, "ai-assistant.agentMaxTurns": {"type": "number", "default": 10, "description": "Maximum number of turns for agent tasks"}, "ai-assistant.requireBashConfirmation": {"type": "boolean", "default": true, "description": "Require confirmation before executing bash commands"}}}}, "scripts": {"vscode:prepublish": "npm run build", "build": "tsc -p ./", "build:intelligent": "node build-intelligent.js", "build:complete": "esbuild src/complete-extension.ts --bundle --outfile=out/complete-extension.js --external:vscode --format=cjs --platform=node", "build:webview": "cd webview && npm run build", "dev": "node build-intelligent.js --watch", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "build:app": "node --max-old-space-size=8192 ./node_modules/typescript/bin/tsc -p ./tsconfig.app.json", "watch:app": "node --max-old-space-size=4096 ./node_modules/typescript/bin/tsc -watch -p ./tsconfig.app.json"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/lodash": "^4.14.195", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/vscode": "^1.80.0", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "@vscode/test-electron": "^2.3.0", "@vscode/vsce": "^2.19.0", "concurrently": "^8.2.2", "esbuild": "^0.25.9", "eslint": "^8.39.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.0.4", "vite": "^5.0.0"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@lancedb/lancedb": "^0.22.0", "apache-arrow": "^21.0.0", "axios": "^1.4.0", "node-fetch": "^3.3.2"}}