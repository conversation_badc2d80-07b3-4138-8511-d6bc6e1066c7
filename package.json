{"name": "o<PERSON><PERSON>-code-assistant", "displayName": "Intelligent AI Assistant", "description": "Lightweight, intelligent AI coding assistant with multi-provider support", "version": "0.1.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "ollama", "code-completion", "chat", "assistant"], "activationEvents": ["onStartupFinished"], "main": "./out/complete-extension.js", "contributes": {"commands": [{"command": "intelligent-ai.openChat", "title": "Open Intelligent AI Chat", "category": "Intelligent AI"}, {"command": "modern-ai.openChat", "title": "Open Modern AI Chat", "category": "Modern AI"}, {"command": "intelligent-ai.selectProvider", "title": "Select AI Provider", "category": "Intelligent AI"}, {"command": "intelligent-ai.configure<PERSON><PERSON><PERSON>", "title": "Configure Gemini API Key", "category": "Intelligent AI"}, {"command": "intelligent-ai.validate<PERSON><PERSON><PERSON>", "title": "Validate AI Provider", "category": "Intelligent AI"}, {"command": "intelligent-ai.showStatus", "title": "Show Provider Status", "category": "Intelligent AI"}], "viewsContainers": {"activitybar": [{"id": "intelligent-ai", "title": "Intelligent AI Assistant", "icon": "$(robot)"}]}, "views": {"intelligent-ai": [{"id": "modern-ai-chat", "name": "AI Chat", "type": "webview"}, {"id": "ai-provider-dropdown", "name": "Select AI Provider", "type": "webview", "icon": "$(gear)"}, {"id": "ollama-model-dropdown", "name": "Select Ollama Model", "type": "webview", "icon": "$(database)"}]}, "viewsWelcome": [{"view": "olla<PERSON>-assistant.chat<PERSON><PERSON><PERSON>", "contents": "Welcome to Ollama Code Assistant!\n[Open Chat](command:ollama-assistant.openChat)\n[Select Model](command:ollama-assistant.selectModel)"}, {"view": "<PERSON><PERSON><PERSON>-assistant.task<PERSON><PERSON><PERSON><PERSON><PERSON>", "contents": "AI Task Agent - Multi-step task execution\n[Open Task Agent](command:ollama-assistant.openTaskChat)\n[Select Model](command:ollama-assistant.selectModel)"}], "configuration": {"title": "Ollama Assistant", "properties": {"ollama-assistant.serverUrl": {"type": "string", "default": "http://localhost:11434", "description": "Ollama server URL"}, "ollama-assistant.defaultModel": {"type": "string", "default": "", "description": "Default model to use for completions"}, "ollama-assistant.chatModel": {"type": "string", "default": "", "description": "Model to use for chat conversations"}, "ollama-assistant.enableCompletions": {"type": "boolean", "default": true, "description": "Enable AI-powered code completions"}, "ollama-assistant.completionDelay": {"type": "number", "default": 500, "description": "Delay in milliseconds before triggering completions"}, "ollama-assistant.maxContextLines": {"type": "number", "default": 100, "description": "Maximum number of lines to include in context"}, "ollama-assistant.aiProvider": {"type": "string", "enum": ["ollama", "gemini"], "default": "ollama", "description": "AI provider to use (Ollama or Google Gemini)"}, "ollama-assistant.geminiApiKey": {"type": "string", "default": "", "description": "Google Gemini API key (required for Gemini provider)"}}}}, "scripts": {"vscode:prepublish": "npm run build:intelligent", "build": "node esbuild.config.js && npm run build:webview", "build:intelligent": "node build-intelligent.js", "build:complete": "esbuild src/complete-extension.ts --bundle --outfile=out/complete-extension.js --external:vscode --format=cjs --platform=node", "build:webview": "cd webview && npm run build", "dev": "node build-intelligent.js --watch", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "build:app": "node --max-old-space-size=8192 ./node_modules/typescript/bin/tsc -p ./tsconfig.app.json", "watch:app": "node --max-old-space-size=4096 ./node_modules/typescript/bin/tsc -watch -p ./tsconfig.app.json"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/lodash": "^4.14.195", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/vscode": "^1.80.0", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "@vscode/test-electron": "^2.3.0", "@vscode/vsce": "^2.19.0", "concurrently": "^8.2.2", "esbuild": "^0.25.9", "eslint": "^8.39.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.0.4", "vite": "^5.0.0"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@lancedb/lancedb": "^0.22.0", "apache-arrow": "^21.0.0", "axios": "^1.4.0", "node-fetch": "^3.3.2"}}