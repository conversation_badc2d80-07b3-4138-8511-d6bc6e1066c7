// Simple test to verify the extension loads
const vscode = require('vscode');

async function testExtension() {
    console.log('Testing Intelligent AI Assistant Extension...');
    
    try {
        // Check if extension is loaded
        const extension = vscode.extensions.getExtension('intelligent-ai-assistant');
        if (extension) {
            console.log('✅ Extension found');
            
            // Check if commands are registered
            const commands = await vscode.commands.getCommands();
            const aiCommands = commands.filter(cmd => cmd.startsWith('intelligent-ai.'));
            
            console.log('✅ AI Commands registered:', aiCommands.length);
            aiCommands.forEach(cmd => console.log('  -', cmd));
            
        } else {
            console.log('❌ Extension not found');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run test if this is the main module
if (require.main === module) {
    testExtension();
}

module.exports = { testExtension };