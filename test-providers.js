// Test the AI providers directly
const { SimpleGeminiClient } = require('./out/services/simple-gemini-client');
const { SimpleOllamaClient } = require('./out/services/simple-ollama-client');

async function testProviders() {
    console.log('Testing AI Providers...\n');
    
    // Test Ollama
    console.log('🦙 Testing Ollama...');
    try {
        const ollama = new SimpleOllamaClient('http://localhost:11434', 'codellama');
        const available = await ollama.isAvailable();
        console.log('Ollama available:', available);
        
        if (available) {
            const response = await ollama.generateResponse('Hello, how are you?');
            console.log('Ollama response:', response.substring(0, 100) + '...');
        }
    } catch (error) {
        console.log('Ollama error:', error.message);
    }
    
    console.log('\n✨ Testing Gemini...');
    // Test Gemini (you'll need to add your API key)
    const geminiKey = process.env.GEMINI_API_KEY || 'your-api-key-here';
    
    if (geminiKey && geminiKey !== 'your-api-key-here') {
        try {
            const gemini = new SimpleGeminiClient(geminiKey);
            const available = await gemini.isAvailable();
            console.log('Gemini available:', available);
            
            if (available) {
                const response = await gemini.generateResponse('Hello, how are you?');
                console.log('Gemini response:', response.substring(0, 100) + '...');
            }
        } catch (error) {
            console.log('Gemini error:', error.message);
        }
    } else {
        console.log('Gemini API key not provided (set GEMINI_API_KEY env var)');
    }
}

testProviders();