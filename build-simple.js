const fs = require('fs');
const path = require('path');

// Simple build script that copies TypeScript files to out directory
function copyFiles(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    
    for (const file of files) {
        const srcPath = path.join(src, file);
        const destPath = path.join(dest, file);
        
        if (fs.statSync(srcPath).isDirectory()) {
            copyFiles(srcPath, destPath);
        } else if (file.endsWith('.ts')) {
            // Copy .ts as .js for simple deployment
            const jsPath = destPath.replace('.ts', '.js');
            let content = fs.readFileSync(srcPath, 'utf8');
            
            // Simple TS to JS conversion
            content = content
                .replace(/import\s+.*?from\s+['"]([^'"]+)['"];?/g, 'const $1 = require("$1");')
                .replace(/export\s+/g, 'module.exports = ');
                
            fs.writeFileSync(jsPath, content);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

console.log('Building extension...');
copyFiles('./src', './out');
console.log('Build complete!');