{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "incremental": true, "tsBuildInfoFile": "./out/.tsbuildinfo"}, "include": ["src/extension.ts", "src/core/AICodingAssistant.ts", "src/core/AgentSystem.ts", "src/webviews/AugmentWebviewProvider.ts", "src/services/configurationManager.ts", "src/services/aiProviderFactory.ts", "src/services/aiProviderInterface.ts", "src/tools/BashTool.ts", "src/tools/StrReplaceTool.ts", "src/tools/sequential_thinking_tool.ts", "src/tools/complete_tool.ts"], "exclude": ["out", "node_modules", "**/*.test.ts"]}