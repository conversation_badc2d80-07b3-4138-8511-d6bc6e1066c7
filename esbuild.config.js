const esbuild = require('esbuild');

const config = {
  entryPoints: ['src/working-extension.ts'],
  bundle: true,
  outfile: 'out/extension.js',
  external: [
    'vscode',
    // Externalize LangChain - load at runtime
    '@langchain/core',
    '@langchain/google-genai', 
    '@langchain/ollama',
    '@langchain/langgraph',
    'langchain'
  ],
  format: 'cjs',
  platform: 'node',
  target: 'node18',
  sourcemap: true,
  minify: false,
  keepNames: true,
  metafile: true
};

esbuild.build(config).catch(() => process.exit(1));