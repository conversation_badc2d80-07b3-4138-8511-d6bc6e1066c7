{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "incremental": true, "tsBuildInfoFile": "./out/.tsbuildinfo", "types": ["mocha"]}, "include": ["src/**/*.ts"], "exclude": ["out", "node_modules", "**/*.test.ts"]}