var r=Object.defineProperty;var u=(n,t,e)=>t in n?r(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var a=(n,t,e)=>u(n,typeof t!="symbol"?t+"":t,e);const c=class c{static getStack(){const t=typeof document<"u"?document:null;return t?(t[c.STACK_KEY]||(t[c.STACK_KEY]=[]),t[c.STACK_KEY]):[]}static add(t){const e=c.getStack();e.includes(t)||e.push(t)}static remove(t){const e=c.getStack(),s=e.indexOf(t);return s!==-1&&(e.splice(s,1),!0)}static isActive(t){const e=c.getStack();return e.length>0&&e[e.length-1]===t}static getActive(){const t=c.getStack();return t.length>0?t[t.length-1]:void 0}static size(){return c.getStack().length}static isEmpty(){return c.size()===0}static clear(){const t=typeof document<"u"?document:null;t&&(t[c.STACK_KEY]=[])}static getAll(){return[...c.getStack()]}};a(c,"STACK_KEY","__focusTrapStack");let i=c;export{i as F};
