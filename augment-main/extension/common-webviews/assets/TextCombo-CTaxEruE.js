import{v as M,x as i,T as N,y as x,z as u,A as v,u as m,B as h,C as T,D as k,E as I,F as s,b as o,G as z,H as $,I as d}from"./GuardedIcon-BFT2yJIo.js";import{b as n}from"./IconButtonAugment-CR0fVrwD.js";var O=x('<span class="c-text-combo__text svelte-1pddlam"><!></span>'),P=x('<div><div class="c-text-combo__gray-text svelte-1pddlam"><!></div></div>'),Q=x('<div role="button" tabindex="-1"><!> <!> <!> <!></div>');function V(w,a){const r=M(a);let D=i(a,"class",8,""),A=i(a,"size",8,1),B=i(a,"align",8,"left"),f=i(a,"greyTextTruncateDirection",8,"right"),C=i(a,"shrink",8,!1);N(w,{get size(){return A()},weight:"medium",children:(G,R)=>{var l=Q();let g;var b=m(l),j=t=>{var e=z(),c=$(e);d(c,a,"leftIcon",{},null),o(t,e)};u(b,t=>{v(()=>r.leftIcon)&&t(j)});var p=h(b,2),E=t=>{var e=O(),c=m(e);d(c,a,"text",{},null),o(t,e)};u(p,t=>{v(()=>r.text)&&t(E)});var _=h(p,2),F=t=>{var e=P();let c;var J=m(e),K=m(J);d(K,a,"grayText",{},null),T(L=>c=I(e,1,"c-text-combo__gray svelte-1pddlam",null,c,L),[()=>({"c-text-combo--gray-truncate-left":f()==="left","c-text-combo--gray-truncate-right":f()==="right"})],k),o(t,e)};u(_,t=>{v(()=>r.grayText)&&t(F)});var H=h(_,2),q=t=>{var e=z(),c=$(e);d(c,a,"rightIcon",{},null),o(t,e)};u(H,t=>{v(()=>r.rightIcon)&&t(q)}),T(t=>g=I(l,1,`c-text-combo ${D()}`,"svelte-1pddlam",g,t),[()=>({"c-text-combo--align-right":B()==="right","c-text-combo--shrink":C()})],k),s("click",l,function(t){n.call(this,a,t)}),s("keydown",l,function(t){n.call(this,a,t)}),s("keyup",l,function(t){n.call(this,a,t)}),s("blur",l,function(t){n.call(this,a,t)}),s("focus",l,function(t){n.call(this,a,t)}),s("mouseenter",l,function(t){n.call(this,a,t)}),s("mouseleave",l,function(t){n.call(this,a,t)}),s("contextmenu",l,function(t){n.call(this,a,t)}),o(G,l)},$$slots:{default:!0}})}export{V as T};
