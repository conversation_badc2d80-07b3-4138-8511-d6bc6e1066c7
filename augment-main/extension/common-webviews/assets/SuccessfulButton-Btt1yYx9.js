import{t as A,N as et,x as i,m as v,a3 as q,a4 as at,a5 as lt,y as Y,G as k,H as $,z as st,b as f,ac as s,P as a,D as ot,ap as G,I as p,B as S,u as rt,X as nt,A as it,ad as w,Y as ct}from"./GuardedIcon-BFT2yJIo.js";import{I as ut,b as l}from"./IconButtonAugment-CR0fVrwD.js";import{T as vt,a as X}from"./CardAugment-DVbbQqkH.js";import{B as ht}from"./ButtonAugment-CWDjQYWT.js";var dt=Y("<!> <!> <!>",1),ft=Y('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function yt(j,e){var P;const E=A(e,["children","$$slots","$$events","$$legacy"]),T=A(E,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);et(e,!1);const g=v(),y=v(),b=v();let C,x=i(e,"defaultColor",8),D=i(e,"tooltip",24,()=>{}),z=i(e,"stateVariant",24,()=>{}),F=i(e,"onClick",8),J=i(e,"tooltipDuration",8,1500),K=i(e,"icon",8,!1),B=i(e,"stickyColor",8,!0),M=i(e,"persistOnTooltipClose",8,!1),Q=i(e,"tooltipNested",24,()=>{}),n=v("neutral"),m=v(x()),L=v(void 0),N=v((P=D())==null?void 0:P.neutral);async function H(o){var h;o.stopPropagation();try{s(n,await F()(o)??"neutral")}catch(u){console.error(u),s(n,"failure")}s(N,(h=D())==null?void 0:h[a(n)]),clearTimeout(C),C=setTimeout(()=>{var u;(u=a(L))==null||u(),B()||s(n,"neutral")},J())}q(()=>(a(g),a(y),w(T)),()=>{s(g,T.variant),s(y,ct(T,["variant"]))}),q(()=>(w(z()),a(n),a(g)),()=>{var o;s(b,((o=z())==null?void 0:o[a(n)])??a(g))}),q(()=>(a(n),w(x())),()=>{a(n)==="success"?s(m,"success"):a(n)==="failure"?s(m,"error"):s(m,x())}),at(),lt();var I=ft(),U=rt(I);const W=ot(()=>(w(X),it(()=>[X.Hover])));vt(U,{onOpenChange:function(o){var h;M()||o||(clearTimeout(C),C=void 0,s(N,(h=D())==null?void 0:h.neutral),B()||s(n,"neutral"))},get content(){return a(N)},get triggerOn(){return a(W)},get nested(){return Q()},get requestClose(){return a(L)},set requestClose(o){s(L,o)},children:(o,h)=>{var u=k(),Z=$(u),_=d=>{ut(d,G(()=>a(y),{get color(){return a(m)},get variant(){return a(b)},$$events:{click:H,keyup(t){l.call(this,e,t)},keydown(t){l.call(this,e,t)},mousedown(t){l.call(this,e,t)},mouseover(t){l.call(this,e,t)},focus(t){l.call(this,e,t)},mouseleave(t){l.call(this,e,t)},blur(t){l.call(this,e,t)},contextmenu(t){l.call(this,e,t)}},children:(t,R)=>{var r=dt(),c=$(r);p(c,e,"iconLeft",{},null);var V=S(c,2);p(V,e,"default",{},null);var tt=S(V,2);p(tt,e,"iconRight",{},null),f(t,r)},$$slots:{default:!0}}))},O=d=>{ht(d,G(()=>a(y),{get color(){return a(m)},get variant(){return a(b)},$$events:{click:H,keyup(t){l.call(this,e,t)},keydown(t){l.call(this,e,t)},mousedown(t){l.call(this,e,t)},mouseover(t){l.call(this,e,t)},focus(t){l.call(this,e,t)},mouseleave(t){l.call(this,e,t)},blur(t){l.call(this,e,t)},contextmenu(t){l.call(this,e,t)}},children:(t,R)=>{var r=k(),c=$(r);p(c,e,"default",{},null),f(t,r)},$$slots:{default:!0,iconLeft:(t,R)=>{var r=k(),c=$(r);p(c,e,"iconLeft",{},null),f(t,r)},iconRight:(t,R)=>{var r=k(),c=$(r);p(c,e,"iconRight",{},null),f(t,r)}}}))};st(Z,d=>{K()?d(_):d(O,!1)}),f(o,u)},$$slots:{default:!0},$$legacy:!0}),f(j,I),nt()}export{yt as S};
