var S=Object.defineProperty;var q=(n,s,o)=>s in n?S(n,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[s]=o;var X=(n,s,o)=>q(n,typeof s!="symbol"?s+"":s,o);import{v as J,N as H,x as c,al as L,a5 as K,y,a as M,O as U,aN as V,Q as W,I as p,u as C,B as O,z as Y,A as B,F as u,b,X as P,T as Z,t as A,ak as z,a3 as ss,ac as D,m as G,Y as ts,ad as as,P as _,a4 as es,ap as ls,G as ns,H as os,D as cs,C as is,E as rs}from"./GuardedIcon-BFT2yJIo.js";import{b as e,C as us}from"./IconButtonAugment-CR0fVrwD.js";class E{constructor(s){this._opts=s}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}X(E,"CONTEXT_KEY","augment-badge");var ds=y('<div class="c-badge__left-buttons svelte-1h72ojf"><!></div>'),hs=y('<div class="c-badge-body svelte-1h72ojf"><!></div>'),vs=y('<div class="c-badge__right-buttons svelte-1h72ojf"><!></div>'),gs=y("<div><!> <!> <!> <!></div>");function bs(n,s){const o=J(s);H(s,!1);let d=c(s,"color",8,"accent"),h=c(s,"variant",8,"soft"),r=c(s,"size",8,1),v=c(s,"radius",8,"medium"),T=c(s,"highContrast",8,!1),N=c(s,"inset",8,!1),j=c(s,"controlledIconSize",8,!0);const k=r()===0?0:r()===3?2:1,f=new E({color:d(),size:r(),variant:h()});L(E.CONTEXT_KEY,f),K();var l=gs();M(l,(t,i,g)=>({...t,...i,class:`c-badge c-badge--${d()} c-badge--${h()} c-badge--size-${r()}`,role:"button",tabindex:"0",[W]:g}),[()=>U(d()),()=>V(v()),()=>({"c-badge--highContrast":T(),"c-badge--inset":N(),"c-badge--controlled-icon-size":j()})],"svelte-1h72ojf");var w=C(l);p(w,s,"chaser",{},null);var a=O(w,2),m=t=>{var i=ds(),g=C(i);p(g,s,"leftButtons",{},null),b(t,i)};Y(a,t=>{B(()=>o.leftButtons)&&t(m)});var $=O(a,2),x=t=>{Z(t,{get size(){return k},weight:"medium",children:(i,g)=>{var I=hs(),R=C(I);p(R,s,"default",{},null),b(i,I)},$$slots:{default:!0}})};Y($,t=>{B(()=>o.default)&&t(x)});var Q=O($,2),F=t=>{var i=vs(),g=C(i);p(g,s,"rightButtons",{},null),b(t,i)};Y(Q,t=>{B(()=>o.rightButtons)&&t(F)}),u("click",l,function(t){e.call(this,s,t)}),u("keydown",l,function(t){e.call(this,s,t)}),u("keyup",l,function(t){e.call(this,s,t)}),u("mousedown",l,function(t){e.call(this,s,t)}),u("mouseover",l,function(t){e.call(this,s,t)}),u("focus",l,function(t){e.call(this,s,t)}),u("mouseleave",l,function(t){e.call(this,s,t)}),u("blur",l,function(t){e.call(this,s,t)}),b(n,l),P()}var fs=y("<div><!></div>");const ps={Root:bs,IconButton:function(n,s){const o=A(s,["children","$$slots","$$events","$$legacy"]),d=A(o,["color","highContrast","disabled"]);H(s,!1);const h=G(),r=G(),v=z(E.CONTEXT_KEY);let T=c(s,"color",24,()=>{return a=v.color,m="neutral",typeof a=="string"&&["accent","neutral","error","success","warning","info"].includes(a)?a:m;var a,m}),N=c(s,"highContrast",8,!1),j=c(s,"disabled",8,!1),k=v.size===0?.5:v.size;ss(()=>(_(h),_(r),as(d)),()=>{D(h,d.class),D(r,ts(d,["class"]))}),es(),K();var f=fs(),l=C(f);const w=cs(()=>`c-badge-icon-btn__base-btn ${_(h)}`);us(l,ls({get size(){return k},variant:"ghost",get color(){return T()},get highContrast(){return N()},get disabled(){return j()},get class(){return _(w)}},()=>_(r),{$$events:{click(a){e.call(this,s,a)},keyup(a){e.call(this,s,a)},keydown(a){e.call(this,s,a)},mousedown(a){e.call(this,s,a)},mouseover(a){e.call(this,s,a)},focus(a){e.call(this,s,a)},mouseleave(a){e.call(this,s,a)},blur(a){e.call(this,s,a)},contextmenu(a){e.call(this,s,a)}},children:(a,m)=>{var $=ns(),x=os($);p(x,s,"default",{},null),b(a,$)},$$slots:{default:!0}})),is(()=>rs(f,1,B(()=>`c-badge-icon-btn c-badge-icon-btn--${v.variant} c-badge-icon-btn--size-${k}`),"svelte-1im94um")),b(n,f),P()}};export{ps as B,bs as a};
