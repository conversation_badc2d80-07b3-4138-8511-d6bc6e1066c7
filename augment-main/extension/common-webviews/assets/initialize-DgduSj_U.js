import{bs as I,bt as x,bu as at,bv as j,bw as J,bx as ht,by as qt,bz as M,bA as Ft,bB as ae,bC as fe,bD as as,bE as F,bF as tt,bG as is,bH as _e,bI as H,bJ as dn,bK as ie,bL as cs,bM as gt,bN as Ve,bO as Ze,bP as us,bQ as Qe,bR as It,bS as Tt,bT as ds,bU as vr,bV as ls,bW as ps,bX as lt,bY as _t,bZ as fs,b_ as Me,b$ as ms,c0 as ce,c1 as ln,c2 as tn,c3 as hs,c4 as pn,c5 as jt,c6 as gs,c7 as vs,c8 as ys,c9 as _s,ca as bs,cb as yr,cc as _r,cd as br,ce as Es,cf as fn,cg as qe,ch as me,ci as mn,cj as Ss,ck as hn,cl as gn,cm as ws,cn as Ts,co as xs,cp as ks,cq as Ps,cr as Rs,cs as en,ct as ft,cu as mt,cv as ue,cw as $s,cx as X,cy as vn,cz as yn,cA as Er,cB as Sr,cC as Cs,cD as he,cE as W,cF as wr,cG as Ut,cH as zt,cI as Os,cJ as As,cK as Fe,cL as Tr,cM as _n,cN as Ds,cO as nn,cP as Is,cQ as Ls,cR as Ns,cS as K,cT as xr,cU as kr,cV as xt,cW as Ms,cX as Pr,cY as At,cZ as qs,c_ as St,c$ as bn,d0 as Fs,d1 as js,d2 as Us,d3 as Hs,d4 as En,d5 as Bs,d6 as Ot,d7 as zs}from"./GuardedIcon-BFT2yJIo.js";const ne={},Sn={};function it(e,t){ne[e]=ne[e]||[],ne[e].push(t)}function ct(e,t){if(!Sn[e]){Sn[e]=!0;try{t()}catch(n){I&&x.error(`Error while instrumenting ${e}`,n)}}}function V(e,t){const n=e&&ne[e];if(n)for(const r of n)try{r(t)}catch(s){I&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${at(r)}
Error:`,s)}}let be=null;function Rr(e){const t="error";it(t,e),ct(t,Ws)}function Ws(){be=j.onerror,j.onerror=function(e,t,n,r,s){return V("error",{column:r,error:s,line:n,msg:e,url:t}),!!be&&be.apply(this,arguments)},j.onerror.__SENTRY_INSTRUMENTED__=!0}let Ee=null;function $r(e){const t="unhandledrejection";it(t,e),ct(t,Gs)}function Gs(){Ee=j.onunhandledrejection,j.onunhandledrejection=function(e){return V("unhandledrejection",e),!Ee||Ee.apply(this,arguments)},j.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let wn=!1;function je(){const e=J(),t=e&&ht(e);if(t){const n="internal_error";I&&x.log(`[Tracing] Root span: ${n} -> Global error occurred`),t.setStatus({code:qt,message:n})}}je.tag="sentry_tracingErrorCallback";const re={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},Xs="heartbeatFailed",Js="idleTimeout",Ys="finalTimeout",Ks="externalFinish";function Tn(e,t={}){const n=new Map;let r,s=!1,o=Ks,a=!t.disableAutoFinish;const i=[],{idleTimeout:c=re.idleTimeout,finalTimeout:u=re.finalTimeout,childSpanTimeout:l=re.childSpanTimeout,beforeSpanEnd:m}=t,d=M();if(!d||!Ft()){const g=new ae,v={sample_rate:"0",sampled:"false",...fe(g)};return as(g,v),g}const p=F(),f=J(),h=function(g){const v=gt(g);return dn(F(),v),I&&x.log("[Tracing] Started span is an idle span"),v}(e);function E(){r&&(clearTimeout(r),r=void 0)}function S(g){E(),r=setTimeout(()=>{!s&&n.size===0&&a&&(o=Js,h.end(g))},c)}function _(g){r=setTimeout(()=>{!s&&a&&(o=Xs,h.end(g))},l)}function k(g){s=!0,n.clear(),i.forEach(y=>y()),dn(p,f);const v=H(h),{start_timestamp:T}=v;if(!T)return;v.data[ie]||h.setAttribute(ie,o),x.log(`[Tracing] Idle span "${v.op}" finished`);const b=_e(h).filter(y=>y!==h);let N=0;b.forEach(y=>{y.isRecording()&&(y.setStatus({code:qt,message:"cancelled"}),y.end(g),I&&x.log("[Tracing] Cancelling span since span ended early",JSON.stringify(y,void 0,2)));const O=H(y),{timestamp:D=0,start_timestamp:w=0}=O,P=w<=g,$=D-w<=(u+c)/1e3;if(I){const R=JSON.stringify(y,void 0,2);P?$||x.log("[Tracing] Discarding span since it finished after idle span final timeout",R):x.log("[Tracing] Discarding span since it happened after idle span was finished",R)}$&&P||(cs(h,y),N++)}),N>0&&h.setAttribute("sentry.idle_span_discarded_spans",N)}return h.end=new Proxy(h.end,{apply(g,v,T){if(m&&m(h),v instanceof ae)return;const[b,...N]=T,y=b||tt(),O=is(y),D=_e(h).filter(A=>A!==h);if(!D.length)return k(O),Reflect.apply(g,v,[O,...N]);const w=D.map(A=>H(A).timestamp).filter(A=>!!A),P=w.length?Math.max(...w):void 0,$=H(h).start_timestamp,R=Math.min($?$+u/1e3:1/0,Math.max($||-1/0,Math.min(O,P||1/0)));return k(R),Reflect.apply(g,v,[R,...N])}}),i.push(d.on("spanStart",g=>{if(!(s||g===h||H(g).timestamp)){var v;_e(h).includes(g)&&(v=g.spanContext().spanId,E(),n.set(v,!0),_(tt()+l/1e3))}})),i.push(d.on("spanEnd",g=>{var v;s||(v=g.spanContext().spanId,n.has(v)&&n.delete(v),n.size===0&&S(tt()+c/1e3))})),i.push(d.on("idleSpanEnableAutoFinish",g=>{g===h&&(a=!0,S(),n.size&&_())})),t.disableAutoFinish||S(),setTimeout(()=>{s||(h.setStatus({code:qt,message:"deadline_exceeded"}),o=Ys,h.end())},u),h}var et;function vt(e){return new ut(t=>{t(e)})}function de(e){return new ut((t,n)=>{n(e)})}(function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"})(et||(et={}));class ut{constructor(t){this._state=et.PENDING,this._handlers=[],this._runExecutor(t)}then(t,n){return new ut((r,s)=>{this._handlers.push([!1,o=>{if(t)try{r(t(o))}catch(a){s(a)}else r(o)},o=>{if(n)try{r(n(o))}catch(a){s(a)}else s(o)}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new ut((n,r)=>{let s,o;return this.then(a=>{o=!1,s=a,t&&t()},a=>{o=!0,s=a,t&&t()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===et.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===et.RESOLVED&&n[1](this._value),this._state===et.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(t){const n=(o,a)=>{this._state===et.PENDING&&(Ve(a)?a.then(r,s):(this._state=o,this._value=a,this._executeHandlers()))},r=o=>{n(et.RESOLVED,o)},s=o=>{n(et.REJECTED,o)};try{t(r,s)}catch(o){s(o)}}}function Ue(e,t,n,r=0){return new ut((s,o)=>{const a=e[r];if(t===null||typeof a!="function")s(t);else{const i=a({...t},n);I&&a.id&&i===null&&x.log(`Event processor "${a.id}" dropped event`),Ve(i)?i.then(c=>Ue(e,c,n,r+1).then(s)).then(null,o):Ue(e,i,n,r+1).then(s).then(null,o)}})}let Xt,xn,Se;function Vs(e,t){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=t;(function(a,i){const{extra:c,tags:u,user:l,contexts:m,level:d,transactionName:p}=i;Object.keys(c).length&&(a.extra={...c,...a.extra}),Object.keys(u).length&&(a.tags={...u,...a.tags}),Object.keys(l).length&&(a.user={...l,...a.user}),Object.keys(m).length&&(a.contexts={...m,...a.contexts}),d&&(a.level=d),p&&a.type!=="transaction"&&(a.transaction=p)})(e,t),r&&function(a,i){a.contexts={trace:us(i),...a.contexts},a.sdkProcessingMetadata={dynamicSamplingContext:fe(i),...a.sdkProcessingMetadata};const c=ht(i),u=H(c).description;u&&!a.transaction&&a.type==="transaction"&&(a.transaction=u)}(e,r),function(a,i){a.fingerprint=a.fingerprint?Array.isArray(a.fingerprint)?a.fingerprint:[a.fingerprint]:[],i&&(a.fingerprint=a.fingerprint.concat(i)),a.fingerprint.length||delete a.fingerprint}(e,n),function(a,i){const c=[...a.breadcrumbs||[],...i];a.breadcrumbs=c.length?c:void 0}(e,s),function(a,i){a.sdkProcessingMetadata={...a.sdkProcessingMetadata,...i}}(e,o)}function kn(e,t){const{extra:n,tags:r,user:s,contexts:o,level:a,sdkProcessingMetadata:i,breadcrumbs:c,fingerprint:u,eventProcessors:l,attachments:m,propagationContext:d,transactionName:p,span:f}=t;Jt(e,"extra",n),Jt(e,"tags",r),Jt(e,"user",s),Jt(e,"contexts",o),e.sdkProcessingMetadata=Ze(e.sdkProcessingMetadata,i,2),a&&(e.level=a),p&&(e.transactionName=p),f&&(e.span=f),c.length&&(e.breadcrumbs=[...e.breadcrumbs,...c]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),l.length&&(e.eventProcessors=[...e.eventProcessors,...l]),m.length&&(e.attachments=[...e.attachments,...m]),e.propagationContext={...e.propagationContext,...d}}function Jt(e,t,n){e[t]=Ze(e[t],n,1)}function Zs(e,t,n,r,s,o){const{normalizeDepth:a=3,normalizeMaxBreadth:i=1e3}=e,c={...t,event_id:t.event_id||n.event_id||It(),timestamp:t.timestamp||Qe()},u=n.integrations||e.integrations.map(f=>f.name);(function(f,h){const{environment:E,release:S,dist:_,maxValueLength:k=250}=h;f.environment=f.environment||E||vr,!f.release&&S&&(f.release=S),!f.dist&&_&&(f.dist=_);const g=f.request;g!=null&&g.url&&(g.url=ls(g.url,k))})(c,e),function(f,h){h.length>0&&(f.sdk=f.sdk||{},f.sdk.integrations=[...f.sdk.integrations||[],...h])}(c,u),s&&s.emit("applyFrameMetadata",t),t.type===void 0&&function(f,h){var S,_;const E=function(k){const g=j._sentryDebugIds;if(!g)return{};const v=Object.keys(g);return Se&&v.length===xn||(xn=v.length,Se=v.reduce((T,b)=>{Xt||(Xt={});const N=Xt[b];if(N)T[N[0]]=N[1];else{const y=k(b);for(let O=y.length-1;O>=0;O--){const D=y[O],w=D==null?void 0:D.filename,P=g[b];if(w&&P){T[w]=P,Xt[b]=[w,P];break}}}return T},{})),Se}(h);(_=(S=f.exception)==null?void 0:S.values)==null||_.forEach(k=>{var g,v;(v=(g=k.stacktrace)==null?void 0:g.frames)==null||v.forEach(T=>{T.filename&&(T.debug_id=E[T.filename])})})}(c,e.stackParser);const l=function(f,h){if(!h)return f;const E=f?f.clone():new ps;return E.update(h),E}(r,n.captureContext);n.mechanism&&Tt(c,n.mechanism);const m=s?s.getEventProcessors():[],d=ds().getScopeData();o&&kn(d,o.getScopeData()),l&&kn(d,l.getScopeData());const p=[...n.attachments||[],...d.attachments];return p.length&&(n.attachments=p),Vs(c,d),Ue([...m,...d.eventProcessors],c,n).then(f=>(f&&function(h){var _,k;const E={};if((k=(_=h.exception)==null?void 0:_.values)==null||k.forEach(g=>{var v,T;(T=(v=g.stacktrace)==null?void 0:v.frames)==null||T.forEach(b=>{b.debug_id&&(b.abs_path?E[b.abs_path]=b.debug_id:b.filename&&(E[b.filename]=b.debug_id),delete b.debug_id)})}),Object.keys(E).length===0)return;h.debug_meta=h.debug_meta||{},h.debug_meta.images=h.debug_meta.images||[];const S=h.debug_meta.images;Object.entries(E).forEach(([g,v])=>{S.push({type:"sourcemap",code_file:g,debug_id:v})})}(f),typeof a=="number"&&a>0?function(h,E,S){var k,g;if(!h)return null;const _={...h,...h.breadcrumbs&&{breadcrumbs:h.breadcrumbs.map(v=>({...v,...v.data&&{data:lt(v.data,E,S)}}))},...h.user&&{user:lt(h.user,E,S)},...h.contexts&&{contexts:lt(h.contexts,E,S)},...h.extra&&{extra:lt(h.extra,E,S)}};return(k=h.contexts)!=null&&k.trace&&_.contexts&&(_.contexts.trace=h.contexts.trace,h.contexts.trace.data&&(_.contexts.trace.data=lt(h.contexts.trace.data,E,S))),h.spans&&(_.spans=h.spans.map(v=>({...v,...v.data&&{data:lt(v.data,E,S)}}))),(g=h.contexts)!=null&&g.flags&&_.contexts&&(_.contexts.flags=lt(h.contexts.flags,3,S)),_}(f,a,i):f))}function Pn(e,t){return F().captureEvent(e,t)}function Rn(e){const t=_t(),n=F(),{userAgent:r}=j.navigator||{},s=fs({user:n.getUser()||t.getUser(),...r&&{userAgent:r},...e}),o=t.getSession();return(o==null?void 0:o.status)==="ok"&&Me(o,{status:"exited"}),Cr(),t.setSession(s),s}function Cr(){const e=_t(),t=F().getSession()||e.getSession();t&&ms(t),Or(),e.setSession()}function Or(){const e=_t(),t=M(),n=e.getSession();n&&t&&t.captureSession(n)}function $n(e=!1){e?Cr():Or()}const Qs="7";function to(e,t,n){return t||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",a=s.port?`:${s.port}`:"";return`${o}//${s.host}${a}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(e)}?${function(r,s){const o={sentry_version:Qs};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(e,n)}`}const Cn=[];function eo(e){const t=e.defaultIntegrations||[],n=e.integrations;let r;if(t.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...t,...n];else if(typeof n=="function"){const s=n(t);r=Array.isArray(s)?s:[s]}else r=t;return function(s){const o={};return s.forEach(a=>{const{name:i}=a,c=o[i];c&&!c.isDefaultInstance&&a.isDefaultInstance||(o[i]=a)}),Object.values(o)}(r)}function On(e,t){for(const n of t)n!=null&&n.afterAllSetup&&n.afterAllSetup(e)}function An(e,t,n){if(n[t.name])I&&x.log(`Integration skipped because it was already installed: ${t.name}`);else{if(n[t.name]=t,Cn.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),Cn.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(s,o)=>r(s,o,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),s=Object.assign((o,a)=>r(o,a,e),{id:t.name});e.addEventProcessor(s)}I&&x.log(`Integration installed: ${t.name}`)}}function Ar(e){const t=[];e.message&&t.push(e.message);try{const n=e.exception.values[e.exception.values.length-1];n!=null&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch{}return t}const Dn="Not capturing exception because it's already been captured.",In="Discarded session because of missing or non-string release",Dr=Symbol.for("SentryInternalError"),Ir=Symbol.for("SentryDoNotSendEventError");function Yt(e){return{message:e,[Dr]:!0}}function we(e){return{message:e,[Ir]:!0}}function Ln(e){return!!e&&typeof e=="object"&&Dr in e}function Nn(e){return!!e&&typeof e=="object"&&Ir in e}class no{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=hs(t.dsn):I&&x.warn("No DSN provided, client will not send events."),this._dsn){const n=to(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const s=It();if(pn(t))return I&&x.log(Dn),s;const o={event_id:s,...n};return this._process(this.eventFromException(t,o).then(a=>this._captureEvent(a,o,r))),o.event_id}captureMessage(t,n,r,s){const o={event_id:It(),...r},a=br(t)?t:String(t),i=jt(t)?this.eventFromMessage(a,n,o):this.eventFromException(t,o);return this._process(i.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(t,n,r){const s=It();if(n!=null&&n.originalException&&pn(n.originalException))return I&&x.log(Dn),s;const o={event_id:s,...n},a=t.sdkProcessingMetadata||{},i=a.capturedSpanScope,c=a.capturedSpanIsolationScope;return this._process(this._captureEvent(t,o,i||r,c)),o.event_id}captureSession(t){this.sendSession(t),Me(t,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(s=>r&&s))):vt(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];An(this,t,this._integrations),n||On(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=gs(t,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=vs(r,ys(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",t,o),null)}sendSession(t){const{release:n,environment:r=vr}=this._options;if("aggregates"in t){const o=t.attrs||{};if(!o.release&&!n)return void(I&&x.warn(In));o.release=o.release||n,o.environment=o.environment||r,t.attrs=o}else{if(!t.release&&!n)return void(I&&x.warn(In));t.release=t.release||n,t.environment=t.environment||r}this.emit("beforeSendSession",t);const s=_s(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(t,n,r=1){if(this._options.sendClientReports){const s=`${t}:${n}`;I&&x.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(s=>s(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(I&&x.error("Error while sending envelope:",n),n)):(I&&x.error("Transport disabled"),vt({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&An(n,o,s)}),s}(this,t),On(this,t)}_updateSessionFromEvent(t,n){var i;let r=n.level==="fatal",s=!1;const o=(i=n.exception)==null?void 0:i.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const a=t.status==="ok";(a&&t.errors===0||a&&r)&&(Me(t,{...r&&{status:"crashed"},errors:t.errors||Number(s||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new ut(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,t&&r>=t&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,s){const o=this.getOptions(),a=Object.keys(this._integrations);return!n.integrations&&(a!=null&&a.length)&&(n.integrations=a),this.emit("preprocessEvent",t,n),t.type||s.setLastEventId(t.event_id||n.event_id),Zs(o,t,n,r,this,s).then(i=>{if(i===null)return i;this.emit("postprocessEvent",i,n),i.contexts={trace:bs(r),...i.contexts};const c=yr(this,r);return i.sdkProcessingMetadata={dynamicSamplingContext:c,...i.sdkProcessingMetadata},i})}_captureEvent(t,n={},r=F(),s=_t()){return I&&Te(t)&&x.log(`Captured error event \`${Ar(t)[0]||"<unknown>"}\``),this._processEvent(t,n,r,s).then(o=>o.event_id,o=>{I&&(Nn(o)?x.log(o.message):Ln(o)?x.warn(o.message):x.warn(o))})}_processEvent(t,n,r,s){const o=this.getOptions(),{sampleRate:a}=o,i=Mn(t),c=Te(t),u=t.type||"error",l=`before send for type \`${u}\``,m=a===void 0?void 0:Es(a);if(c&&typeof m=="number"&&Math.random()>m)return this.recordDroppedEvent("sample_rate","error"),de(we(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));const d=u==="replay_event"?"replay":u;return this._prepareEvent(t,n,r,s).then(p=>{if(p===null)throw this.recordDroppedEvent("event_processor",d),we("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return p;const f=function(h,E,S,_){const{beforeSend:k,beforeSendTransaction:g,beforeSendSpan:v}=E;let T=S;if(Te(T)&&k)return k(T,_);if(Mn(T)){if(v){const N=v(function(y){var U;const{trace_id:O,parent_span_id:D,span_id:w,status:P,origin:$,data:R,op:A}=((U=y.contexts)==null?void 0:U.trace)??{};return{data:R??{},description:y.transaction,op:A,parent_span_id:D,span_id:w??"",start_timestamp:y.start_timestamp??0,status:P,timestamp:y.timestamp,trace_id:O??"",origin:$,profile_id:R==null?void 0:R[ln],exclusive_time:R==null?void 0:R[ce],measurements:y.measurements,is_segment:!0}}(T));if(N?T=Ze(S,{type:"transaction",timestamp:(b=N).timestamp,start_timestamp:b.start_timestamp,transaction:b.description,contexts:{trace:{trace_id:b.trace_id,span_id:b.span_id,parent_span_id:b.parent_span_id,op:b.op,status:b.status,origin:b.origin,data:{...b.data,...b.profile_id&&{[ln]:b.profile_id},...b.exclusive_time&&{[ce]:b.exclusive_time}}}},measurements:b.measurements}):fn(),T.spans){const y=[];for(const O of T.spans){const D=v(O);D?y.push(D):(fn(),y.push(O))}T.spans=y}}if(g){if(T.spans){const N=T.spans.length;T.sdkProcessingMetadata={...S.sdkProcessingMetadata,spanCountBeforeProcessing:N}}return g(T,_)}}var b;return T}(0,o,p,n);return function(h,E){const S=`${E} must return \`null\` or a valid event.`;if(Ve(h))return h.then(_=>{if(!qe(_)&&_!==null)throw Yt(S);return _},_=>{throw Yt(`${E} rejected with ${_}`)});if(!qe(h)&&h!==null)throw Yt(S);return h}(f,l)}).then(p=>{var E;if(p===null){if(this.recordDroppedEvent("before_send",d),i){const S=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",S)}throw we(`${l} returned \`null\`, will not send event.`)}const f=r.getSession()||s.getSession();if(c&&f&&this._updateSessionFromEvent(f,p),i){const S=(((E=p.sdkProcessingMetadata)==null?void 0:E.spanCountBeforeProcessing)||0)-(p.spans?p.spans.length:0);S>0&&this.recordDroppedEvent("before_send","span",S)}const h=p.transaction_info;if(i&&h&&p.transaction!==t.transaction){const S="custom";p.transaction_info={...h,source:S}}return this.sendEvent(p,n),p}).then(null,p=>{throw Nn(p)||Ln(p)?p:(this.captureException(p,{data:{__sentry__:!0},originalException:p}),Yt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${p}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){I&&x.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0)return void(I&&x.log("No outcomes to send"));if(!this._dsn)return void(I&&x.log("No dsn provided, will not send outcomes"));I&&x.log("Sending outcomes:",t);const n=function(r,s,o){const a=[{type:"client_report"},{timestamp:Qe(),discarded_events:r}];return tn(s?{dsn:s}:{},[a])}(t,this._options.tunnel&&_r(this._dsn));this.sendEnvelope(n)}}function Te(e){return e.type===void 0}function Mn(e){return e.type==="transaction"}function xe(e,t){var o;const n=function(a){var i;return(i=j._sentryClientToLogBufferMap)==null?void 0:i.get(a)}(e)??[];if(n.length===0)return;const r=e.getOptions(),s=function(a,i,c,u){const l={};return i!=null&&i.sdk&&(l.sdk={name:i.sdk.name,version:i.sdk.version}),c&&u&&(l.dsn=_r(u)),tn(l,[(m=a,[{type:"log",item_count:m.length,content_type:"application/vnd.sentry.items.log+json"},{items:m}])]);var m}(n,r._metadata,r.tunnel,e.getDsn());(o=j._sentryClientToLogBufferMap)==null||o.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(s)}function ro(e,t){t.debug===!0&&(I?x.enable():me(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),F().update(t.initialScope);const n=new e(t);return function(r){F().setClient(r)}(n),n.init(),n}j._sentryClientToLogBufferMap=new WeakMap;const Lr=Symbol.for("SentryBufferFullError");function so(e){const t=[];function n(r){return t.splice(t.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(e===void 0||t.length<e))return de(Lr);const s=r();return t.indexOf(s)===-1&&t.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new ut((s,o)=>{let a=t.length;if(!a)return s(!0);const i=setTimeout(()=>{r&&r>0&&s(!1)},r);t.forEach(c=>{vt(c).then(()=>{--a||(clearTimeout(i),s(!0))},o)})})}}}const oo=6e4;function ao(e,{statusCode:t,headers:n},r=Date.now()){const s={...e},o=n==null?void 0:n["x-sentry-rate-limits"],a=n==null?void 0:n["retry-after"];if(o)for(const i of o.trim().split(",")){const[c,u,,,l]=i.split(":",5),m=parseInt(c,10),d=1e3*(isNaN(m)?60:m);if(u)for(const p of u.split(";"))p==="metric_bucket"&&l&&!l.split(";").includes("custom")||(s[p]=r+d);else s.all=r+d}else a?s.all=r+function(i,c=Date.now()){const u=parseInt(`${i}`,10);if(!isNaN(u))return 1e3*u;const l=Date.parse(`${i}`);return isNaN(l)?oo:l-c}(a,r):t===429&&(s.all=r+6e4);return s}const io=64;function co(e,t,n=so(e.bufferSize||io)){let r={};return{send:function(s){const o=[];if(mn(s,(c,u)=>{const l=hn(u);(function(m,d,p=Date.now()){return function(f,h){return f[h]||f.all||0}(m,d)>p})(r,l)?e.recordDroppedEvent("ratelimit_backoff",l):o.push(c)}),o.length===0)return vt({});const a=tn(s[0],o),i=c=>{mn(a,(u,l)=>{e.recordDroppedEvent(c,hn(l))})};return n.add(()=>t({body:Ss(a)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&I&&x.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=ao(r,c),c),c=>{throw i("network_error"),I&&x.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===Lr)return I&&x.error("Skipped sending event because buffer is full."),i("queue_overflow"),vt({});throw c})},flush:s=>n.drain(s)}}function uo(e){var t;((t=e.user)==null?void 0:t.ip_address)===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function lo(e){var t;"aggregates"in e?((t=e.attrs)==null?void 0:t.ip_address)===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):e.ipAddress===void 0&&(e.ipAddress="{{auto}}")}function Nr(e,t,n=[t],r="npm"){const s=e._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${t}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:gn})),version:gn}),e._metadata=s}function Mr(e={}){const t=M();if(!function(){const u=M();return(u==null?void 0:u.getOptions().enabled)!==!1&&!!(u!=null&&u.getTransport())}()||!t)return{};const n=ws(),r=Ts(n);if(r.getTraceData)return r.getTraceData(e);const s=F(),o=e.span||J(),a=o?xs(o):function(u){const{traceId:l,sampled:m,propagationSpanId:d}=u.getPropagationContext();return Rs(l,d,m)}(s),i=o?fe(o):yr(t,s),c=ks(i);return Ps.test(a)?{"sentry-trace":a,baggage:c}:(x.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const po=100;function pt(e,t){const n=M(),r=_t();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=po}=n.getOptions();if(o<=0)return;const a={timestamp:Qe(),...e},i=s?me(()=>s(a,t)):a;i!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",i,t),r.addBreadcrumb(i,o))}let qn;const Fn=new WeakMap,fo=()=>({name:"FunctionToString",setupOnce(){qn=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=en(this),n=Fn.has(M())&&t!==void 0?t:this;return qn.apply(n,e)}}catch{}},setup(e){Fn.set(e,!0)}}),mo=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],ho=(e={})=>{let t;return{name:"EventFilters",setup(n){const r=n.getOptions();t=jn(e,r)},processEvent(n,r,s){if(!t){const o=s.getOptions();t=jn(e,o)}return function(o,a){if(o.type){if(o.type==="transaction"&&function(i,c){if(!(c!=null&&c.length))return!1;const u=i.transaction;return!!u&&mt(u,c)}(o,a.ignoreTransactions))return I&&x.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${ft(o)}`),!0}else{if(function(i,c){return c!=null&&c.length?Ar(i).some(u=>mt(u,c)):!1}(o,a.ignoreErrors))return I&&x.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${ft(o)}`),!0;if(function(i){var c,u;return(u=(c=i.exception)==null?void 0:c.values)!=null&&u.length?!i.message&&!i.exception.values.some(l=>l.stacktrace||l.type&&l.type!=="Error"||l.value):!1}(o))return I&&x.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${ft(o)}`),!0;if(function(i,c){if(!(c!=null&&c.length))return!1;const u=Kt(i);return!!u&&mt(u,c)}(o,a.denyUrls))return I&&x.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${ft(o)}.
Url: ${Kt(o)}`),!0;if(!function(i,c){if(!(c!=null&&c.length))return!0;const u=Kt(i);return!u||mt(u,c)}(o,a.allowUrls))return I&&x.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${ft(o)}.
Url: ${Kt(o)}`),!0}return!1}(n,t)?null:n}}},go=(e={})=>({...ho(e),name:"InboundFilters"});function jn(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:mo],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function Kt(e){var t,n;try{const r=[...((t=e.exception)==null?void 0:t.values)??[]].reverse().find(o=>{var a,i,c;return((a=o.mechanism)==null?void 0:a.parent_id)===void 0&&((c=(i=o.stacktrace)==null?void 0:i.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let a=o.length-1;a>=0;a--){const i=o[a];if(i&&i.filename!=="<anonymous>"&&i.filename!=="[native code]")return i.filename||null}return null}(s):null}catch{return I&&x.error(`Cannot extract url for event ${ft(e)}`),null}}function vo(e,t,n,r,s,o){var i;if(!((i=s.exception)!=null&&i.values)||!o||!ue(o.originalException,Error))return;const a=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;a&&(s.exception.values=He(e,t,r,o.originalException,n,s.exception.values,a,0))}function He(e,t,n,r,s,o,a,i){if(o.length>=n+1)return o;let c=[...o];if(ue(r[s],Error)){Un(a,i);const u=e(t,r[s]),l=c.length;Hn(u,s,l,i),c=He(e,t,n,r[s],s,[u,...c],u,l)}return Array.isArray(r.errors)&&r.errors.forEach((u,l)=>{if(ue(u,Error)){Un(a,i);const m=e(t,u),d=c.length;Hn(m,`errors[${l}]`,d,i),c=He(e,t,n,u,s,[m,...c],m,d)}}),c}function Un(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function Hn(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function yo(){"console"in j&&$s.forEach(function(e){e in j.console&&X(j.console,e,function(t){return vn[e]=t,function(...n){V("console",{args:n,level:e});const r=vn[e];r==null||r.apply(j.console,n)}})})}function _o(e){return e==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}const bo=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{if(function(n,r){return r?!!(function(s,o){const a=s.message,i=o.message;return!(!a&&!i||a&&!i||!a&&i||a!==i||!zn(s,o)||!Bn(s,o))}(n,r)||function(s,o){const a=Wn(o),i=Wn(s);return!(!a||!i||a.type!==i.type||a.value!==i.value||!zn(s,o)||!Bn(s,o))}(n,r)):!1}(t,e))return I&&x.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}};function Bn(e,t){let n=yn(e),r=yn(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],a=n[s];if(o.filename!==a.filename||o.lineno!==a.lineno||o.colno!==a.colno||o.function!==a.function)return!1}return!0}function zn(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function Wn(e){var t;return((t=e.exception)==null?void 0:t.values)&&e.exception.values[0]}const Eo="thismessage:/";function qr(e){return"isRelative"in e}function Fr(e,t){const n=e.indexOf("://")<=0&&e.indexOf("//")!==0,r=n?Eo:void 0;try{if("canParse"in URL&&!URL.canParse(e,r))return;const s=new URL(e,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function So(e){if(qr(e))return e.pathname;const t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}function wt(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function wo(e,t,n,r,s="auto.http.browser"){if(!e.fetchData)return;const{method:o,url:a}=e.fetchData,i=Ft()&&t(a);if(e.endTimestamp&&i){const m=e.fetchData.__span;if(!m)return;const d=r[m];return void(d&&(function(p,f){var h;if(f.response){Er(p,f.response.status);const E=((h=f.response)==null?void 0:h.headers)&&f.response.headers.get("content-length");if(E){const S=parseInt(E);S>0&&p.setAttribute("http.response_content_length",S)}}else f.error&&p.setStatus({code:qt,message:"internal_error"});p.end()}(d,e),delete r[m]))}const c=!!J(),u=i&&c?gt(function(m,d,p){const f=Fr(m);return{name:f?`${d} ${So(f)}`:d,attributes:To(m,f,d,p)}}(a,o,s)):new ae;if(e.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(e.fetchData.url)){const m=e.args[0],d=e.args[1]||{},p=function(f,h,E){const S=Mr({span:E}),_=S["sentry-trace"],k=S.baggage;if(!_)return;const g=h.headers||(Sr(f)?f.headers:void 0);if(g){if(function(v){return typeof Headers<"u"&&ue(v,Headers)}(g)){const v=new Headers(g);if(v.get("sentry-trace")||v.set("sentry-trace",_),k){const T=v.get("baggage");T?Vt(T)||v.set("baggage",`${T},${k}`):v.set("baggage",k)}return v}if(Array.isArray(g)){const v=[...g];g.find(b=>b[0]==="sentry-trace")||v.push(["sentry-trace",_]);const T=g.find(b=>b[0]==="baggage"&&Vt(b[1]));return k&&!T&&v.push(["baggage",k]),v}{const v="sentry-trace"in g?g["sentry-trace"]:void 0,T="baggage"in g?g.baggage:void 0,b=T?Array.isArray(T)?[...T]:[T]:[],N=T&&(Array.isArray(T)?T.find(y=>Vt(y)):Vt(T));return k&&!N&&b.push(k),{...g,"sentry-trace":v??_,baggage:b.length>0?b.join(","):void 0}}}return{...S}}(m,d,Ft()&&c?u:void 0);p&&(e.args[1]=d,d.headers=p)}const l=M();if(l){const m={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};l.emit("beforeOutgoingRequestSpan",u,m)}return u}function Vt(e){return e.split(",").some(t=>t.trim().startsWith(Cs))}function To(e,t,n,r){const s={url:e,type:"fetch","http.method":n,[W]:r,[he]:"http.client"};return t&&(qr(t)||(s["http.url"]=t.href,s["server.address"]=t.host),t.search&&(s["http.query"]=t.search),t.hash&&(s["http.fragment"]=t.hash)),s}function Gn(e){return e===void 0?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}const Ht=j;function jr(){if(!("fetch"in Ht))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Be(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function Ur(e,t){const n="fetch";it(n,e),ct(n,()=>Hr(void 0,t))}function Hr(e,t=!1){t&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!jr())return!1;if(Be(Ht.fetch))return!0;let n=!1;const r=Ht.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Be(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){I&&x.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||X(j,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:a}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[l,m]=c;return{url:Xn(l),method:ze(m,"method")?String(m.method).toUpperCase():"GET"}}const u=c[0];return{url:Xn(u),method:ze(u,"method")?String(u.method).toUpperCase():"GET"}}(r),i={args:r,fetchData:{method:o,url:a},startTimestamp:1e3*tt(),virtualError:s,headers:ko(r)};return e||V("fetch",{...i}),n.apply(j,r).then(async c=>(e?e(c):V("fetch",{...i,endTimestamp:1e3*tt(),response:c}),c),c=>{if(V("fetch",{...i,endTimestamp:1e3*tt(),error:c}),wr(c)&&c.stack===void 0&&(c.stack=s.stack,Ut(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(i.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function xo(e){let t;try{t=e.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),a=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let i=!0;for(;i;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),i=!1)}catch{i=!1}finally{clearTimeout(c)}}clearTimeout(a),o.releaseLock(),s.cancel().then(null,()=>{})}})(t,()=>{V("fetch-body-resolved",{endTimestamp:1e3*tt(),response:e})})}function ze(e,t){return!!e&&typeof e=="object"&&!!e[t]}function Xn(e){return typeof e=="string"?e:e?ze(e,"url")?e.url:e.toString?e.toString():"":""}function ko(e){const[t,n]=e;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Sr(t))return new Headers(t.headers)}catch{}}const L=j;let We=0;function Jn(){return We>0}function kt(e,t={}){if(!function(r){return typeof r=="function"}(e))return e;try{const r=e.__sentry_wrapped__;if(r)return typeof r=="function"?r:e;if(en(e))return e}catch{return e}const n=function(...r){try{const s=r.map(o=>kt(o,t));return e.apply(this,s)}catch(s){throw We++,setTimeout(()=>{We--}),As(o=>{var a;o.addEventProcessor(i=>(t.mechanism&&(Fe(i,void 0),Tt(i,t.mechanism)),i.extra={...i.extra,arguments:r},i)),a=s,F().captureException(a,void 0)}),s}};try{for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}catch{}Os(n,e),Ut(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>e.name})}catch{}return n}function Ge(){const e=zt(),{referrer:t}=L.document||{},{userAgent:n}=L.navigator||{};return{url:e,headers:{...t&&{Referer:t},...n&&{"User-Agent":n}}}}function rn(e,t){const n=sn(e,t),r={type:$o(t),value:Co(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Po(e,t,n,r){const s=M(),o=s==null?void 0:s.getOptions().normalizeDepth,a=function(u){for(const l in u)if(Object.prototype.hasOwnProperty.call(u,l)){const m=u[l];if(m instanceof Error)return m}}(t),i={__serialized__:Is(t,o)};if(a)return{exception:{values:[rn(e,a)]},extra:i};const c={exception:{values:[{type:nn(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:Oo(t,{isUnhandledRejection:r})}]},extra:i};if(n){const u=sn(e,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function ke(e,t){return{exception:{values:[rn(e,t)]}}}function sn(e,t){const n=t.stacktrace||t.stack||"",r=function(o){return o&&Ro.test(o.message)?1:0}(t),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(t);try{return e(n,r,s)}catch{}return[]}const Ro=/Minified React error #\d+;/i;function Br(e){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&e instanceof WebAssembly.Exception}function $o(e){const t=e==null?void 0:e.name;return!t&&Br(e)?e.message&&Array.isArray(e.message)&&e.message.length==2?e.message[0]:"WebAssembly.Exception":t}function Co(e){const t=e==null?void 0:e.message;return Br(e)?Array.isArray(e.message)&&e.message.length==2?e.message[1]:"wasm exception":t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function Xe(e,t,n,r,s){let o;if(Tr(t)&&t.error)return ke(e,t.error);if(_n(t)||Ds(t)){const a=t;if("stack"in t)o=ke(e,t);else{const i=a.name||(_n(a)?"DOMError":"DOMException"),c=a.message?`${i}: ${a.message}`:i;o=Je(e,c,n,r),Fe(o,c)}return"code"in a&&(o.tags={...o.tags,"DOMException.code":`${a.code}`}),o}return wr(t)?ke(e,t):qe(t)||nn(t)?(o=Po(e,t,n,s),Tt(o,{synthetic:!0}),o):(o=Je(e,t,n,r),Fe(o,`${t}`),Tt(o,{synthetic:!0}),o)}function Je(e,t,n,r){const s={};if(r&&n){const o=sn(e,n);o.length&&(s.exception={values:[{value:t,stacktrace:{frames:o}}]}),Tt(s,{synthetic:!0})}if(br(t)){const{__sentry_template_string__:o,__sentry_template_values__:a}=t;return s.logentry={message:o,params:a},s}return s.message=t,s}function Oo(e,{isUnhandledRejection:t}){const n=Ls(e),r=t?"promise rejection":"exception";return Tr(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:nn(e)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class Ao extends no{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t};Nr(n,"browser",["browser"],L.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,a=o==null?void 0:o.enableLogs;n.sendClientReports&&L.document&&L.document.addEventListener("visibilitychange",()=>{L.document.visibilityState==="hidden"&&(this._flushOutcomes(),a&&xe(r))}),a&&(r.on("flush",()=>{xe(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{xe(r)},5e3)})),s&&(r.on("postprocessEvent",uo),r.on("beforeSendSession",lo))}eventFromException(t,n){return function(r,s,o,a){const i=Xe(r,s,(o==null?void 0:o.syntheticException)||void 0,a);return Tt(i),i.level="error",o!=null&&o.event_id&&(i.event_id=o.event_id),vt(i)}(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return function(s,o,a="info",i,c){const u=Je(s,o,(i==null?void 0:i.syntheticException)||void 0,c);return u.level=a,i!=null&&i.event_id&&(u.event_id=i.event_id),vt(u)}(this._options.stackParser,t,n,r,this._options.attachStacktrace)}_prepareEvent(t,n,r,s){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r,s)}}const on=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Pt=(e,t,n,r)=>{let s,o;return a=>{t.value>=0&&(a||r)&&(o=t.value-(s||0),(o||s===void 0)&&(s=t.value,t.delta=o,t.rating=((i,c)=>i>c[1]?"poor":i>c[0]?"needs-improvement":"good")(t.value,n),e(t)))}},C=j,Bt=(e=!0)=>{var n,r;const t=(r=(n=C.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},Wt=()=>{const e=Bt();return(e==null?void 0:e.activationStart)||0},Rt=(e,t)=>{var s,o;const n=Bt();let r="navigate";return n&&((s=C.document)!=null&&s.prerendering||Wt()>0?r="prerender":(o=C.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:t===void 0?-1:t,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},yt=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{t(s.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch{}},$t=e=>{const t=n=>{var r;n.type!=="pagehide"&&((r=C.document)==null?void 0:r.visibilityState)!=="hidden"||e(n)};C.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},ge=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let Lt=-1;const le=e=>{C.document.visibilityState==="hidden"&&Lt>-1&&(Lt=e.type==="visibilitychange"?e.timeStamp:0,Do())},Do=()=>{removeEventListener("visibilitychange",le,!0),removeEventListener("prerenderingchange",le,!0)},ve=()=>(C.document&&Lt<0&&(Lt=C.document.visibilityState!=="hidden"||C.document.prerendering?1/0:0,addEventListener("visibilitychange",le,!0),addEventListener("prerenderingchange",le,!0)),{get firstHiddenTime(){return Lt}}),Gt=e=>{var t;(t=C.document)!=null&&t.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},Io=[1800,3e3],Lo=[.1,.25],No=(e,t={})=>{((n,r={})=>{Gt(()=>{const s=ve(),o=Rt("FCP");let a;const i=yt("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(i.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-Wt(),0),o.entries.push(u),a(!0)))})});i&&(a=Pt(n,o,Io,r.reportAllChanges))})})(ge(()=>{const n=Rt("CLS",0);let r,s=0,o=[];const a=c=>{c.forEach(u=>{if(!u.hadRecentInput){const l=o[0],m=o[o.length-1];s&&l&&m&&u.startTime-m.startTime<1e3&&u.startTime-l.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},i=yt("layout-shift",a);i&&(r=Pt(e,n,Lo,t.reportAllChanges),$t(()=>{a(i.takeRecords()),r(!0)}),setTimeout(r,0))}))},Mo=[100,300],qo=(e,t={})=>{Gt(()=>{const n=ve(),r=Rt("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},a=c=>{c.forEach(o)},i=yt("first-input",a);s=Pt(e,r,Mo,t.reportAllChanges),i&&$t(ge(()=>{a(i.takeRecords()),i.disconnect()}))})};let zr=0,Pe=1/0,Zt=0;const Fo=e=>{e.forEach(t=>{t.interactionId&&(Pe=Math.min(Pe,t.interactionId),Zt=Math.max(Zt,t.interactionId),zr=Zt?(Zt-Pe)/7+1:0)})};let Ye;const jo=()=>{"interactionCount"in performance||Ye||(Ye=yt("event",Fo,{type:"event",buffered:!0,durationThreshold:0}))},nt=[],Re=new Map,Uo=()=>(Ye?zr:performance.interactionCount||0)-0,Ho=[],Bo=e=>{var r;if(Ho.forEach(s=>s(e)),!e.interactionId&&e.entryType!=="first-input")return;const t=nt[nt.length-1],n=Re.get(e.interactionId);if(n||nt.length<10||t&&e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(e);else{const s={id:e.interactionId,latency:e.duration,entries:[e]};Re.set(s.id,s),nt.push(s)}nt.sort((s,o)=>o.latency-s.latency),nt.length>10&&nt.splice(10).forEach(s=>Re.delete(s.id))}},Wr=e=>{var r;const t=C.requestIdleCallback||C.setTimeout;let n=-1;return e=ge(e),((r=C.document)==null?void 0:r.visibilityState)==="hidden"?e():(n=t(e),$t(e)),n},zo=[200,500],Wo=(e,t={})=>{"PerformanceEventTiming"in C&&"interactionId"in PerformanceEventTiming.prototype&&Gt(()=>{jo();const n=Rt("INP");let r;const s=a=>{Wr(()=>{a.forEach(Bo);const i=(()=>{const c=Math.min(nt.length-1,Math.floor(Uo()/50));return nt[c]})();i&&i.latency!==n.value&&(n.value=i.latency,n.entries=i.entries,r())})},o=yt("event",s,{durationThreshold:t.durationThreshold!=null?t.durationThreshold:40});r=Pt(e,n,zo,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),$t(()=>{s(o.takeRecords()),r(!0)}))})},Go=[2500,4e3],Yn={},Xo=(e,t={})=>{Gt(()=>{const n=ve(),r=Rt("LCP");let s;const o=i=>{t.reportAllChanges||(i=i.slice(-1)),i.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-Wt(),0),r.entries=[c],s())})},a=yt("largest-contentful-paint",o);if(a){s=Pt(e,r,Go,t.reportAllChanges);const i=ge(()=>{Yn[r.id]||(o(a.takeRecords()),a.disconnect(),Yn[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{C.document&&addEventListener(c,()=>Wr(i),{once:!0,capture:!0})}),$t(i)}})},Jo=[800,1800],Ke=e=>{var t,n;(t=C.document)!=null&&t.prerendering?Gt(()=>Ke(e)):((n=C.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>Ke(e),!0):setTimeout(e,0)},Yo=(e,t={})=>{const n=Rt("TTFB"),r=Pt(e,n,Jo,t.reportAllChanges);Ke(()=>{const s=Bt();s&&(n.value=Math.max(s.responseStart-Wt(),0),n.entries=[s],r(!0))})},Nt={},pe={};let Gr,Xr,Jr,Yr,Kr;function Vr(e,t=!1){return Mt("cls",e,Ko,Gr,t)}function Dt(e,t){return Zr(e,t),pe[e]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),yt(n,s=>{Ct(n,{entries:s})},r)}(e),pe[e]=!0),Qr(e,t)}function Ct(e,t){const n=Nt[e];if(n!=null&&n.length)for(const r of n)try{r(t)}catch(s){on&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${at(r)}
Error:`,s)}}function Ko(){return No(e=>{Ct("cls",{metric:e}),Gr=e},{reportAllChanges:!0})}function Vo(){return qo(e=>{Ct("fid",{metric:e}),Xr=e})}function Zo(){return Xo(e=>{Ct("lcp",{metric:e}),Jr=e},{reportAllChanges:!0})}function Qo(){return Yo(e=>{Ct("ttfb",{metric:e}),Yr=e})}function ta(){return Wo(e=>{Ct("inp",{metric:e}),Kr=e})}function Mt(e,t,n,r,s=!1){let o;return Zr(e,t),pe[e]||(o=n(),pe[e]=!0),r&&t({metric:r}),Qr(e,t,s?o:void 0)}function Zr(e,t){Nt[e]=Nt[e]||[],Nt[e].push(t)}function Qr(e,t,n){return()=>{n&&n();const r=Nt[e];if(!r)return;const s=r.indexOf(t);s!==-1&&r.splice(s,1)}}function $e(e){return typeof e=="number"&&isFinite(e)}function rt(e,t,n,{...r}){const s=H(e).start_timestamp;return s&&s>t&&typeof e.updateStartTime=="function"&&e.updateStartTime(t),Ns(e,()=>{const o=gt({startTime:t,...r});return o&&o.end(n),o})}function ts(e){var E;const t=M();if(!t)return;const{name:n,transaction:r,attributes:s,startTime:o}=e,{release:a,environment:i,sendDefaultPii:c}=t.getOptions(),u=t.getIntegrationByName("Replay"),l=u==null?void 0:u.getReplayId(),m=F(),d=m.getUser(),p=d!==void 0?d.email||d.id||d.ip_address:void 0;let f;try{f=m.getScopeData().contexts.profile.profile_id}catch{}const h={release:a,environment:i,user:p||void 0,profile_id:f||void 0,replay_id:l||void 0,transaction:r,"user_agent.original":(E=C.navigator)==null?void 0:E.userAgent,"client.address":c?"{{auto}}":void 0,...s};return gt({name:n,attributes:h,startTime:o,experimental:{standalone:!0}})}function an(){return C.addEventListener&&C.performance}function q(e){return e/1e3}function es(e){let t="unknown",n="unknown",r="";for(const s of e){if(s==="/"){[t,n]=e.split("/");break}if(!isNaN(Number(s))){t=r==="h"?"http":r,n=e.split(r)[1];break}r+=s}return r===e&&(t=r),{name:t,version:n}}function ea(){let e,t,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,t&&function(a,i,c){var f;on&&x.log(`Sending CLS span (${a})`);const u=q((K()||0)+((i==null?void 0:i.startTime)||0)),l=F().getScopeData().transactionName,m=i?xt((f=i.sources[0])==null?void 0:f.node):"Layout shift",d={[W]:"auto.http.browser.cls",[he]:"ui.webvital.cls",[ce]:(i==null?void 0:i.duration)||0,"sentry.pageload.span_id":c},p=ts({name:m,transaction:l,attributes:d,startTime:u});p&&(p.addEvent("cls",{[kr]:"",[xr]:a}),p.end(u))}(n,e,t),o())}const o=Vr(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(n=a.value,e=i)},!0);$t(()=>{s()}),setTimeout(()=>{const a=M();if(!a)return;const i=a.on("startNavigationSpan",()=>{s(),i==null||i()}),c=J();if(c){const u=ht(c);H(u).op==="pageload"&&(t=u.spanContext().spanId)}},0)}const na=2147483647;let G,bt,Kn=0,B={};function ra({recordClsStandaloneSpans:e}){const t=an();if(t&&K()){t.mark&&C.performance.mark("sentry-tracing-init");const n=Mt("fid",({metric:a})=>{const i=a.entries[a.entries.length-1];if(!i)return;const c=q(K()),u=q(i.startTime);B.fid={value:a.value,unit:"millisecond"},B["mark.fid"]={value:c+u,unit:"second"}},Vo,Xr),r=function(a,i=!1){return Mt("lcp",a,Zo,Jr,i)}(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(B.lcp={value:a.value,unit:"millisecond"},G=i)},!0),s=function(a){return Mt("ttfb",a,Qo,Yr)}(({metric:a})=>{a.entries[a.entries.length-1]&&(B.ttfb={value:a.value,unit:"millisecond"})}),o=e?ea():Vr(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(B.cls={value:a.value,unit:""},bt=i)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function sa(e,t){const n=an(),r=K();if(!(n!=null&&n.getEntries)||!r)return;const s=q(r),o=n.getEntries(),{op:a,start_timestamp:i}=H(e);if(o.slice(Kn).forEach(c=>{const u=q(c.startTime),l=q(Math.max(0,c.duration));if(!(a==="navigation"&&i&&s+u<i))switch(c.entryType){case"navigation":(function(m,d,p){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(f=>{Qt(m,d,f,p)}),Qt(m,d,"secureConnection",p,"TLS/SSL"),Qt(m,d,"fetch",p,"cache"),Qt(m,d,"domainLookup",p,"DNS"),function(f,h,E){const S=E+q(h.requestStart),_=E+q(h.responseEnd),k=E+q(h.responseStart);h.responseEnd&&(rt(f,S,_,{op:"browser.request",name:h.name,attributes:{[W]:"auto.ui.browser.metrics"}}),rt(f,k,_,{op:"browser.response",name:h.name,attributes:{[W]:"auto.ui.browser.metrics"}}))}(m,d,p)})(e,c,s);break;case"mark":case"paint":case"measure":{(function(p,f,h,E,S){const _=Bt(!1),k=q(_?_.requestStart:0),g=S+Math.max(h,k),v=S+h,T=v+E,b={[W]:"auto.resource.browser.metrics"};if(g!==v&&(b["sentry.browser.measure_happened_before_request"]=!0,b["sentry.browser.measure_start_time"]=g),f.detail)if(typeof f.detail=="object")for(const[N,y]of Object.entries(f.detail))if(y&&jt(y))b[`sentry.browser.measure.detail.${N}`]=y;else try{b[`sentry.browser.measure.detail.${N}`]=JSON.stringify(y)}catch{}else if(jt(f.detail))b["sentry.browser.measure.detail"]=f.detail;else try{b["sentry.browser.measure.detail"]=JSON.stringify(f.detail)}catch{}g<=T&&rt(p,g,T,{name:f.name,op:f.entryType,attributes:b})})(e,c,u,l,s);const m=ve(),d=c.startTime<m.firstHiddenTime;c.name==="first-paint"&&d&&(B.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&d&&(B.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(m,d,p,f,h,E){if(d.initiatorType==="xmlhttprequest"||d.initiatorType==="fetch")return;const S=wt(p),_={[W]:"auto.resource.browser.metrics"};Ce(_,d,"transferSize","http.response_transfer_size"),Ce(_,d,"encodedBodySize","http.response_content_length"),Ce(_,d,"decodedBodySize","http.decoded_response_content_length");const k=d.deliveryType;k!=null&&(_["http.response_delivery_type"]=k);const g=d.renderBlockingStatus;g&&(_["resource.render_blocking_status"]=g),S.protocol&&(_["url.scheme"]=S.protocol.split(":").pop()),S.host&&(_["server.address"]=S.host),_["url.same_origin"]=p.includes(C.location.origin);const{name:v,version:T}=es(d.nextHopProtocol);_["network.protocol.name"]=v,_["network.protocol.version"]=T;const b=E+f,N=b+h;rt(m,b,N,{name:p.replace(C.location.origin,""),op:d.initiatorType?`resource.${d.initiatorType}`:"resource.other",attributes:_})})(e,c,c.name,u,l,s)}}),Kn=Math.max(o.length-1,0),function(c){const u=C.navigator;if(!u)return;const l=u.connection;l&&(l.effectiveType&&c.setAttribute("effectiveConnectionType",l.effectiveType),l.type&&c.setAttribute("connectionType",l.type),$e(l.rtt)&&(B["connection.rtt"]={value:l.rtt,unit:"millisecond"})),$e(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),$e(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(e),a==="pageload"){(function(u){const l=Bt(!1);if(!l)return;const{responseStart:m,requestStart:d}=l;d<=m&&(u["ttfb.requestTime"]={value:m-d,unit:"millisecond"})})(B);const c=B["mark.fid"];c&&B.fid&&(rt(e,c.value,c.value+q(B.fid.value),{name:"first input delay",op:"ui.action",attributes:{[W]:"auto.ui.browser.metrics"}}),delete B["mark.fid"]),"fcp"in B&&t.recordClsOnPageloadSpan||delete B.cls,Object.entries(B).forEach(([u,l])=>{Ms(u,l.value,l.unit)}),e.setAttribute("performance.timeOrigin",s),e.setAttribute("performance.activationStart",Wt()),function(u){G&&(G.element&&u.setAttribute("lcp.element",xt(G.element)),G.id&&u.setAttribute("lcp.id",G.id),G.url&&u.setAttribute("lcp.url",G.url.trim().slice(0,200)),G.loadTime!=null&&u.setAttribute("lcp.loadTime",G.loadTime),G.renderTime!=null&&u.setAttribute("lcp.renderTime",G.renderTime),u.setAttribute("lcp.size",G.size)),bt!=null&&bt.sources&&bt.sources.forEach((l,m)=>u.setAttribute(`cls.source.${m+1}`,xt(l.node)))}(e)}G=void 0,bt=void 0,B={}}function Qt(e,t,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),a=t[o],i=t[`${n}Start`];i&&a&&rt(e,r+q(i),r+q(a),{op:`browser.${s}`,name:t.name,attributes:{[W]:"auto.ui.browser.metrics",...n==="redirect"&&t.redirectCount!=null?{"http.redirect_count":t.redirectCount}:{}}})}function Ce(e,t,n,r){const s=t[n];s!=null&&s<na&&(e[r]=s)}const oa=1e3;let Vn,Oe,Ae,te;function aa(){if(!C.document)return;const e=V.bind(null,"dom"),t=Zn(e,!0);C.document.addEventListener("click",t,!1),C.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{var o,a;const r=C,s=(o=r[n])==null?void 0:o.prototype;(a=s==null?void 0:s.hasOwnProperty)!=null&&a.call(s,"addEventListener")&&(X(s,"addEventListener",function(i){return function(c,u,l){if(c==="click"||c=="keypress")try{const m=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},d=m[c]=m[c]||{refCount:0};if(!d.handler){const p=Zn(e);d.handler=p,i.call(this,c,p,l)}d.refCount++}catch{}return i.call(this,c,u,l)}}),X(s,"removeEventListener",function(i){return function(c,u,l){if(c==="click"||c=="keypress")try{const m=this.__sentry_instrumentation_handlers__||{},d=m[c];d&&(d.refCount--,d.refCount<=0&&(i.call(this,c,d.handler,l),d.handler=void 0,delete m[c]),Object.keys(m).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return i.call(this,c,u,l)}}))})}function Zn(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,a){return o==="keypress"&&(!(a!=null&&a.tagName)||a.tagName!=="INPUT"&&a.tagName!=="TEXTAREA"&&!a.isContentEditable)}(n.type,r))return;Ut(n,"_sentryCaptured",!0),r&&!r._sentryId&&Ut(r,"_sentryId",It());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==Oe)return!1;try{if(!o.target||o.target._sentryId!==Ae)return!1}catch{}return!0})(n)||(e({event:n,name:s,global:t}),Oe=n.type,Ae=r?r._sentryId:void 0),clearTimeout(Vn),Vn=C.setTimeout(()=>{Ae=void 0,Oe=void 0},oa)}}function cn(e){const t="history";it(t,e),ct(t,ia)}function ia(){function e(t){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=te,o=function(a){try{return new URL(a,C.location.origin).toString()}catch{return a}}(String(r));if(te=o,s===o)return t.apply(this,n);V("history",{from:s,to:o})}return t.apply(this,n)}}C.addEventListener("popstate",()=>{const t=C.location.href,n=te;te=t,n!==t&&V("history",{from:n,to:t})}),"history"in Ht&&Ht.history&&(X(C.history,"pushState",e),X(C.history,"replaceState",e))}const se={};function Qn(e){se[e]=void 0}const Et="__sentry_xhr_v3__";function ns(e){it("xhr",e),ct("xhr",ca)}function ca(){if(!C.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const s=new Error,o=1e3*tt(),a=At(r[0])?r[0].toUpperCase():void 0,i=function(u){if(At(u))return u;try{return u.toString()}catch{}}(r[1]);if(!a||!i)return t.apply(n,r);n[Et]={method:a,url:i,request_headers:{}},a==="POST"&&i.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[Et];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}V("xhr",{endTimestamp:1e3*tt(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,l,m)=>(c(),u.apply(l,m))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,l,m){const[d,p]=m,f=l[Et];return f&&At(d)&&At(p)&&(f.request_headers[d.toLowerCase()]=p),u.apply(l,m)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const s=n[Et];return s?(r[0]!==void 0&&(s.body=r[0]),V("xhr",{startTimestamp:1e3*tt(),xhr:n}),t.apply(n,r)):t.apply(n,r)}})}const De=[],oe=new Map;function ua(){if(an()&&K()){const e=Mt("inp",({metric:t})=>{if(t.value==null)return;const n=t.entries.find(d=>d.duration===t.value&&tr[d.name]);if(!n)return;const{interactionId:r}=n,s=tr[n.name],o=q(K()+n.startTime),a=q(t.value),i=J(),c=i?ht(i):void 0,u=(r!=null?oe.get(r):void 0)||c,l=u?H(u).description:F().getScopeData().transactionName,m=ts({name:xt(n.target),transaction:l,attributes:{[W]:"auto.http.browser.inp",[he]:`ui.interaction.${s}`,[ce]:n.duration},startTime:o});m&&(m.addEvent("inp",{[kr]:"millisecond",[xr]:t.value}),m.end(o+a))},ta,Kr);return()=>{e()}}return()=>{}}const tr={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function da(e,t=function(n){const r=se[n];if(r)return r;let s=C[n];if(Be(s))return se[n]=s.bind(C);const o=C.document;if(o&&typeof o.createElement=="function")try{const a=o.createElement("iframe");a.hidden=!0,o.head.appendChild(a);const i=a.contentWindow;i!=null&&i[n]&&(s=i[n]),o.head.removeChild(a)}catch(a){on&&x.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,a)}return s&&(se[n]=s.bind(C))}("fetch")){let n=0,r=0;return co(e,function(s){const o=s.body.length;n+=o,r++;const a={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return Qn("fetch"),de("No fetch implementation available");try{return t(e.url,a).then(i=>(n-=o,r--,{statusCode:i.status,headers:{"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits"),"retry-after":i.headers.get("Retry-After")}}))}catch(i){return Qn("fetch"),n-=o,r--,de(i)}})}function Ie(e,t,n,r){const s={filename:e,function:t==="<anonymous>"?St:t,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const la=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,pa=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,fa=/\((\S*)(?::(\d+))(?::(\d+))\)/,ma=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,ha=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,ga=qs([30,e=>{const t=la.exec(e);if(t){const[,r,s,o]=t;return Ie(r,St,+s,+o)}const n=pa.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=fa.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=er(n[1]||St,n[2]);return Ie(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{const t=ma.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const s=ha.exec(t[3]);s&&(t[1]=t[1]||"eval",t[3]=s[1],t[4]=s[2],t[5]="")}let n=t[3],r=t[1]||St;return[r,n]=er(r,n),Ie(n,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),er=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:St,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},Y=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,nr=1024,va=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(n){var r;t.console&&function(s){const o="console";it(o,s),ct(o,yo)}(function(s){return function(o){if(M()!==s)return;const a={category:"console",data:{arguments:o.args,logger:"console"},level:_o(o.level),message:bn(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;a.message=`Assertion failed: ${bn(o.args.slice(1)," ")||"console.assert"}`,a.data.arguments=o.args.slice(1)}pt(a,{input:o.args,level:o.level})}}(n)),t.dom&&(r=function(s,o){return function(a){if(M()!==s)return;let i,c,u=typeof o=="object"?o.serializeAttribute:void 0,l=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;l&&l>nr&&(Y&&x.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${l} was configured. Sentry will use 1024 instead.`),l=nr),typeof u=="string"&&(u=[u]);try{const d=a.event,p=function(f){return!!f&&!!f.target}(d)?d.target:d;i=xt(p,{keyAttrs:u,maxStringLength:l}),c=Pr(p)}catch{i="<unknown>"}if(i.length===0)return;const m={category:`ui.${a.name}`,message:i};c&&(m.data={"ui.component_name":c}),pt(m,{event:a.event,name:a.name,global:a.global})}}(n,t.dom),it("dom",r),ct("dom",aa)),t.xhr&&ns(function(s){return function(o){if(M()!==s)return;const{startTimestamp:a,endTimestamp:i}=o,c=o.xhr[Et];if(!a||!i||!c)return;const{method:u,url:l,status_code:m,body:d}=c,p={method:u,url:l,status_code:m},f={xhr:o.xhr,input:d,startTimestamp:a,endTimestamp:i},h={category:"xhr",data:p,type:"http",level:Gn(m)};s.emit("beforeOutgoingRequestBreadcrumb",h,f),pt(h,f)}}(n)),t.fetch&&Ur(function(s){return function(o){if(M()!==s)return;const{startTimestamp:a,endTimestamp:i}=o;if(i&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:a,endTimestamp:i},l={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",l,u),pt(l,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const l={input:o.args,response:c,startTimestamp:a,endTimestamp:i},m={category:"fetch",data:u,type:"http",level:Gn(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",m,l),pt(m,l)}}}(n)),t.history&&cn(function(s){return function(o){if(M()!==s)return;let a=o.from,i=o.to;const c=wt(L.location.href);let u=a?wt(a):void 0;const l=wt(i);u!=null&&u.path||(u=c),c.protocol===l.protocol&&c.host===l.host&&(i=l.relative),c.protocol===u.protocol&&c.host===u.host&&(a=u.relative),pt({category:"navigation",data:{from:a,to:i}})}}(n)),t.sentry&&n.on("beforeSendEvent",function(s){return function(o){M()===s&&pt({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:ft(o)},{event:o})}}(n))}}},ya=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],_a=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&X(L,"setTimeout",rr),t.setInterval&&X(L,"setInterval",rr),t.requestAnimationFrame&&X(L,"requestAnimationFrame",ba),t.XMLHttpRequest&&"XMLHttpRequest"in L&&X(XMLHttpRequest.prototype,"send",Ea);const n=t.eventTarget;n&&(Array.isArray(n)?n:ya).forEach(Sa)}}};function rr(e){return function(...t){const n=t[0];return t[0]=kt(n,{mechanism:{data:{function:at(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function ba(e){return function(t){return e.apply(this,[kt(t,{mechanism:{data:{function:"requestAnimationFrame",handler:at(e)},handled:!1,type:"instrument"}})])}}function Ea(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&X(n,r,function(s){const o={mechanism:{data:{function:r,handler:at(s)},handled:!1,type:"instrument"}},a=en(s);return a&&(o.mechanism.data.handler=at(a)),kt(s,o)})}),e.apply(this,t)}}function Sa(e){var r,s;const t=L,n=(r=t[e])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(X(n,"addEventListener",function(o){return function(a,i,c){try{typeof i.handleEvent=="function"&&(i.handleEvent=kt(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:at(i),target:e},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[a,kt(i,{mechanism:{data:{function:"addEventListener",handler:at(i),target:e},handled:!1,type:"instrument"}}),c])}}),X(n,"removeEventListener",function(o){return function(a,i,c){try{const u=i.__sentry_wrapped__;u&&o.call(this,a,u,c)}catch{}return o.call(this,a,i,c)}}))}const wa=()=>({name:"BrowserSession",setupOnce(){L.document!==void 0?(Rn({ignoreDuration:!0}),$n(),cn(({from:e,to:t})=>{e!==void 0&&e!==t&&(Rn({ignoreDuration:!0}),$n())})):Y&&x.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),Ta=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(function(r){Rr(s=>{const{stackParser:o,attachStacktrace:a}=or();if(M()!==r||Jn())return;const{msg:i,url:c,line:u,column:l,error:m}=s,d=function(p,f,h,E){const S=p.exception=p.exception||{},_=S.values=S.values||[],k=_[0]=_[0]||{},g=k.stacktrace=k.stacktrace||{},v=g.frames=g.frames||[],T=E,b=h,N=At(f)&&f.length>0?f:zt();return v.length===0&&v.push({colno:T,filename:N,function:St,in_app:!0,lineno:b}),p}(Xe(o,m||i,void 0,a,!1),c,u,l);d.level="error",Pn(d,{originalException:m,mechanism:{handled:!1,type:"onerror"}})})}(n),sr("onerror")),t.onunhandledrejection&&(function(r){$r(s=>{const{stackParser:o,attachStacktrace:a}=or();if(M()!==r||Jn())return;const i=function(u){if(jt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=jt(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:Xe(o,i,void 0,a,!0);c.level="error",Pn(c,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),sr("onunhandledrejection"))}}};function sr(e){Y&&x.log(`Global Handler attached: ${e}`)}function or(){const e=M();return(e==null?void 0:e.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const xa=()=>({name:"HttpContext",preprocessEvent(e){var r;if(!L.navigator&&!L.location&&!L.document)return;const t=Ge(),n={...t.headers,...(r=e.request)==null?void 0:r.headers};e.request={...t,...e.request,headers:n}}}),ka=(e={})=>{const t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){vo(rn,o.getOptions().stackParser,n,t,r,s)}}};function Pa(e){const t={};for(const n of Object.getOwnPropertyNames(e)){const r=n;e[r]!==void 0&&(t[r]=e[r])}return t}function Ra(e={}){const t=function(r={}){var s;return{defaultIntegrations:[go(),fo(),_a(),va(),Ta(),ka(),bo(),xa(),wa()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(s=L.SENTRY_RELEASE)==null?void 0:s.id,sendClientReports:!0,...Pa(r)}}(e);if(!t.skipBrowserExtensionCheck&&function(){var u;const r=L.window!==void 0&&L;if(!r)return!1;const s=r[r.chrome?"chrome":"browser"],o=(u=s==null?void 0:s.runtime)==null?void 0:u.id,a=zt()||"",i=!!o&&L===L.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(l=>a.startsWith(`${l}//`)),c=r.nw!==void 0;return!!o&&!i&&!c}())return void(Y&&me(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));Y&&!jr()&&x.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...t,stackParser:Fs(t.stackParser||ga),integrations:eo(t),transport:t.transport||da};return ro(Ao,n)}const ar=new WeakMap,Le=new Map,rs={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function $a(e,t){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:a,tracePropagationTargets:i,onRequestSpanStart:c}={...rs,...t},u=typeof o=="function"?o:d=>!0,l=d=>function(p,f){const h=zt();if(h){let E,S;try{E=new URL(p,h),S=new URL(h).origin}catch{return!1}const _=E.origin===S;return f?mt(E.toString(),f)||_&&mt(E.pathname,f):_}{const E=!!p.match(/^\/(?!\/)/);return f?mt(p,f):E}}(d,i),m={};n&&(e.addEventProcessor(d=>(d.type==="transaction"&&d.spans&&d.spans.forEach(p=>{if(p.op==="http.client"){const f=Le.get(p.span_id);f&&(p.timestamp=f/1e3,Le.delete(p.span_id))}}),d)),s&&function(d){const p="fetch-body-resolved";it(p,d),ct(p,()=>Hr(xo))}(d=>{if(d.response){const p=ar.get(d.response);p&&d.endTimestamp&&Le.set(p,d.endTimestamp)}}),Ur(d=>{const p=wo(d,u,l,m);if(d.response&&d.fetchData.__span&&ar.set(d.response,d.fetchData.__span),p){const f=cr(d.fetchData.url),h=f?wt(f).host:void 0;p.setAttributes({"http.url":f,"server.address":h}),a&&ir(p),c==null||c(p,{headers:d.headers})}})),r&&ns(d=>{var f;const p=function(h,E,S,_){const k=h.xhr,g=k==null?void 0:k[Et];if(!k||k.__sentry_own_request__||!g)return;const{url:v,method:T}=g,b=Ft()&&E(v);if(h.endTimestamp&&b){const R=k.__sentry_xhr_span_id__;if(!R)return;const A=_[R];return void(A&&g.status_code!==void 0&&(Er(A,g.status_code),A.end(),delete _[R]))}const N=cr(v),y=wt(N||v),O=(P=v,P.split(/[?#]/,1)[0]),D=!!J(),w=b&&D?gt({name:`${T} ${O}`,attributes:{url:v,type:"xhr","http.method":T,"http.url":N,"server.address":y==null?void 0:y.host,[W]:"auto.http.browser",[he]:"http.client",...(y==null?void 0:y.search)&&{"http.query":y==null?void 0:y.search},...(y==null?void 0:y.hash)&&{"http.fragment":y==null?void 0:y.hash}}}):new ae;var P;k.__sentry_xhr_span_id__=w.spanContext().spanId,_[k.__sentry_xhr_span_id__]=w,S(v)&&function(R,A){const{"sentry-trace":U,baggage:z}=Mr({span:A});U&&function(dt,st,ot){var un;const Z=(un=dt.__sentry_xhr_v3__)==null?void 0:un.request_headers;if(!(Z!=null&&Z["sentry-trace"]))try{if(dt.setRequestHeader("sentry-trace",st),ot){const ye=Z==null?void 0:Z.baggage;ye&&ye.split(",").some(os=>os.trim().startsWith("sentry-"))||dt.setRequestHeader("baggage",ot)}}catch{}}(R,U,z)}(k,Ft()&&D?w:void 0);const $=M();return $&&$.emit("beforeOutgoingRequestSpan",w,h),w}(d,u,l,m);if(p){let h;a&&ir(p);try{h=new Headers((f=d.xhr.__sentry_xhr_v3__)==null?void 0:f.request_headers)}catch{}c==null||c(p,{headers:h})}})}function ir(e){const{url:t}=H(e).data;if(!t||typeof t!="string")return;const n=Dt("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(t)&&(function(o){const{name:a,version:i}=es(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",i],["network.protocol.name",a]),K()?[...c,["http.request.redirect_start",Q(o.redirectStart)],["http.request.fetch_start",Q(o.fetchStart)],["http.request.domain_lookup_start",Q(o.domainLookupStart)],["http.request.domain_lookup_end",Q(o.domainLookupEnd)],["http.request.connect_start",Q(o.connectStart)],["http.request.secure_connection_start",Q(o.secureConnectionStart)],["http.request.connection_end",Q(o.connectEnd)],["http.request.request_start",Q(o.requestStart)],["http.request.response_start",Q(o.responseStart)],["http.request.response_end",Q(o.responseEnd)]]:c}(s).forEach(o=>e.setAttribute(...o)),setTimeout(n))})})}function Q(e=0){return((K()||performance.timeOrigin)+e)/1e3}function cr(e){try{return new URL(e,L.location.origin).href}catch{return}}const Ca=3600,ur="sentry_previous_trace",Oa="sentry.previous_trace";function Aa(e,{linkPreviousTrace:t,consistentTraceSampling:n}){const r=t==="session-storage";let s=r?function(){var a;try{const i=(a=L.sessionStorage)==null?void 0:a.getItem(ur);return JSON.parse(i)}catch{return}}():void 0;e.on("spanStart",a=>{if(ht(a)!==a)return;const i=F().getPropagationContext();s=function(c,u,l){const m=H(u);function d(){var h,E;try{return Number((h=l.dsc)==null?void 0:h.sample_rate)??Number((E=m.data)==null?void 0:E[Hs])}catch{return 0}}const p={spanContext:u.spanContext(),startTimestamp:m.start_timestamp,sampleRate:d(),sampleRand:l.sampleRand};if(!c)return p;const f=c.spanContext;return f.traceId===m.trace_id?c:(Date.now()/1e3-c.startTimestamp<=Ca&&(Y&&x.info(`Adding previous_trace ${f} link to span ${{op:m.op,...u.spanContext()}}`),u.addLink({context:f,attributes:{[Us]:"previous_trace"}}),u.setAttribute(Oa,`${f.traceId}-${f.spanId}-${Ne(f)?1:0}`)),p)}(s,a,i),r&&function(c){try{L.sessionStorage.setItem(ur,JSON.stringify(c))}catch(u){Y&&x.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&e.on("beforeSampling",a=>{if(!s)return;const i=F(),c=i.getPropagationContext();o&&c.parentSpanId?o=!1:(i.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String(Ne(s.spanContext))},sampleRand:s.sampleRand}),a.parentSampled=Ne(s.spanContext),a.parentSampleRate=s.sampleRate,a.spanAttributes={...a.spanAttributes,[js]:s.sampleRate})})}function Ne(e){return e.traceFlags===1}const Da={...re,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...rs};let dr=!1;const Ia=(e={})=>{dr&&me(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),dr=!0;const t=L.document;wn||(wn=!0,Rr(je),$r(je));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:a},beforeStartSpan:i,idleTimeout:c,finalTimeout:u,childSpanTimeout:l,markBackgroundSpan:m,traceFetch:d,traceXHR:p,trackFetchStreamPerformance:f,shouldCreateSpanForRequest:h,enableHTTPTimings:E,instrumentPageLoad:S,instrumentNavigation:_,linkPreviousTrace:k,consistentTraceSampling:g,onRequestSpanStart:v}={...Da,...e},T=ra({recordClsStandaloneSpans:a||!1});n&&ua(),s&&j.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(y=>{const O=J();if(O)for(const D of y.getEntries()){if(!D.scripts[0])continue;const w=q(K()+D.startTime),{start_timestamp:P,op:$}=H(O);if($==="navigation"&&P&&w<P)continue;const R=q(D.duration),A={[W]:"auto.ui.browser.metrics"},U=D.scripts[0],{invoker:z,invokerType:dt,sourceURL:st,sourceFunctionName:ot,sourceCharPosition:Z}=U;A["browser.script.invoker"]=z,A["browser.script.invoker_type"]=dt,st&&(A["code.filepath"]=st),ot&&(A["code.function"]=ot),Z!==-1&&(A["browser.script.source_char_position"]=Z),rt(O,w,w+R,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:A})}}).observe({type:"long-animation-frame",buffered:!0}):r&&Dt("longtask",({entries:y})=>{const O=J();if(!O)return;const{op:D,start_timestamp:w}=H(O);for(const P of y){const $=q(K()+P.startTime),R=q(P.duration);D==="navigation"&&w&&$<w||rt(O,$,$+R,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[W]:"auto.ui.browser.metrics"}})}}),o&&Dt("event",({entries:y})=>{const O=J();if(O){for(const D of y)if(D.name==="click"){const w=q(K()+D.startTime),P=q(D.duration),$={name:xt(D.target),op:`ui.interaction.${D.name}`,startTime:w,attributes:{[W]:"auto.ui.browser.metrics"}},R=Pr(D.target);R&&($.attributes["ui.component_name"]=R),rt(O,w,w+P,$)}}});const b={name:void 0,source:void 0};function N(y,O){const D=O.op==="pageload",w=i?i(O):O,P=w.attributes||{};O.name!==w.name&&(P[Ot]="custom",w.attributes=P),b.name=w.name,b.source=P[Ot];const $=Tn(w,{idleTimeout:c,finalTimeout:u,childSpanTimeout:l,disableAutoFinish:D,beforeSpanEnd:A=>{T(),sa(A,{recordClsOnPageloadSpan:!a}),pr(y,void 0);const U=F(),z=U.getPropagationContext();U.setPropagationContext({...z,traceId:$.spanContext().traceId,sampled:zs($),dsc:fe(A)})}});function R(){t&&["interactive","complete"].includes(t.readyState)&&y.emit("idleSpanEnableAutoFinish",$)}pr(y,$),D&&t&&(t.addEventListener("readystatechange",()=>{R()}),R())}return{name:"BrowserTracing",afterAllSetup(y){let O=zt();function D(){const w=ee(y);w&&!H(w).timestamp&&(Y&&x.log(`[Tracing] Finishing current active span with op: ${H(w).op}`),w.setAttribute(ie,"cancelled"),w.end())}if(y.on("startNavigationSpan",w=>{if(M()!==y)return;D(),_t().setPropagationContext({traceId:En(),sampleRand:Math.random()});const P=F();P.setPropagationContext({traceId:En(),sampleRand:Math.random()}),P.setSDKProcessingMetadata({normalizedRequest:void 0}),N(y,{op:"navigation",...w})}),y.on("startPageLoadSpan",(w,P={})=>{if(M()!==y)return;D();const $=P.sentryTrace||lr("sentry-trace"),R=P.baggage||lr("baggage"),A=Bs($,R),U=F();U.setPropagationContext(A),U.setSDKProcessingMetadata({normalizedRequest:Ge()}),N(y,{op:"pageload",...w})}),k!=="off"&&Aa(y,{linkPreviousTrace:k,consistentTraceSampling:g}),L.location){if(S){const w=K();(function(P,$,R){P.emit("startPageLoadSpan",$,R),F().setTransactionName($.name),ee(P)})(y,{name:L.location.pathname,startTime:w?w/1e3:void 0,attributes:{[Ot]:"url",[W]:"auto.pageload.browser"}})}_&&cn(({to:w,from:P})=>{if(P===void 0&&(O==null?void 0:O.indexOf(w))!==-1)return void(O=void 0);O=void 0;const $=Fr(w);(function(R,A){R.emit("startNavigationSpan",A),F().setTransactionName(A.name),ee(R)})(y,{name:($==null?void 0:$.pathname)||L.location.pathname,attributes:{[Ot]:"url",[W]:"auto.navigation.browser"}}),F().setSDKProcessingMetadata({normalizedRequest:{...Ge(),url:w}})})}m&&(L.document?L.document.addEventListener("visibilitychange",()=>{const w=J();if(!w)return;const P=ht(w);if(L.document.hidden&&P){const $="cancelled",{op:R,status:A}=H(P);Y&&x.log(`[Tracing] Transaction: ${$} -> since tab moved to the background, op: ${R}`),A||P.setStatus({code:qt,message:$}),P.setAttribute("sentry.cancellation_reason","document.hidden"),P.end()}}):Y&&x.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(w,P,$,R,A){const U=L.document;let z;const dt=()=>{const st="ui.action.click",ot=ee(w);if(ot){const Z=H(ot).op;if(["navigation","pageload"].includes(Z))return void(Y&&x.warn(`[Tracing] Did not create ${st} span because a pageload or navigation span is in progress.`))}z&&(z.setAttribute(ie,"interactionInterrupted"),z.end(),z=void 0),A.name?z=Tn({name:A.name,op:st,attributes:{[Ot]:A.source||"url"}},{idleTimeout:P,finalTimeout:$,childSpanTimeout:R}):Y&&x.warn(`[Tracing] Did not create ${st} transaction because _latestRouteName is missing.`)};U&&addEventListener("click",dt,{once:!1,capture:!0})}(y,c,u,l,b),n&&function(){const w=({entries:P})=>{const $=J(),R=$&&ht($);P.forEach(A=>{if(!function(z){return"duration"in z}(A)||!R)return;const U=A.interactionId;if(U!=null&&!oe.has(U)){if(De.length>10){const z=De.shift();oe.delete(z)}De.push(U),oe.set(U,R)}})};Dt("event",w),Dt("first-input",w)}(),$a(y,{traceFetch:d,traceXHR:p,trackFetchStreamPerformance:f,tracePropagationTargets:y.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:h,enableHTTPTimings:E,onRequestSpanStart:v})}}};function lr(e){const t=L.document,n=t==null?void 0:t.querySelector(`meta[name=${e}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const ss="_sentry_idleSpan";function ee(e){return e[ss]}function pr(e,t){Ut(e,ss,t)}function La(e){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let t=0,n=performance.now(),r=60;const s=[];let o=0;const a=e.lowFramerateThreshold,i=e.slowInpThreshold;if(requestAnimationFrame(function c(u){const l=u-n;if(t++,l>1e3){r=1e3*t/l,t=0,n=u,s.push(r),s.length>10&&s.shift();const m=s.reduce((d,p)=>d+p,0)/s.length;if(r<a){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${m.toFixed(1)} fps`);const d=gt({name:"slow_framerate",op:"performance.monitoring",attributes:{"performance.fps":r,"performance.avg_fps":m,"performance.threshold":a,"performance.is_critical":r<15,"webview.url":window.location.href}});d.setStatus({code:2,message:`Slow framerate: ${r.toFixed(1)} fps`}),d.end()}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const l=u.getEntries().filter(p=>"interactionId"in p&&"duration"in p&&p.startTime>0&&p.duration<1e6);if(l.length===0)return;l.sort((p,f)=>f.duration-p.duration);const m=Math.floor(.98*l.length),d=l[Math.min(m,l.length-1)].duration;if(d>i){console.error(`[Augment Performance] Slow INP detected: ${d.toFixed(1)} ms`);let p=null;const f=l[0];f&&"target"in f&&(p=f.target,console.error("[Augment Performance] Slow interaction target:",p,f));const h=gt({name:"slow_inp",op:"performance.monitoring",attributes:{"performance.inp":d,"performance.threshold":i,"performance.target":p?String(p):void 0,"webview.url":window.location.href}});h.setStatus({code:2,message:`Slow INP: ${d.toFixed(1)} ms`}),h.end(),d>o&&(o=d)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const fr=16,mr=200;function hr(){var e;return((e=window.augmentFlags)==null?void 0:e.enablePerformanceMonitoring)??!1}let gr=!1;(function(){var n,r;const e=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var t;(t={enabled:hr(),lowFramerateThreshold:fr,slowInpThreshold:mr}).enabled&&La({lowFramerateThreshold:t.lowFramerateThreshold||fr,slowInpThreshold:t.slowInpThreshold||mr}),hr()&&!e&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var t,n;if(!((n=(t=window.augmentFlags)==null?void 0:t.sentry)!=null&&n.enabled))return;const e=window.augmentFlags.sentry;if(e)if(gr)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};Nr(s,"svelte"),Ra(s)})({dsn:e.dsn,release:e.release,environment:e.environment,tracesSampleRate:e.tracesSampleRate||0,replaysSessionSampleRate:e.replaysSessionSampleRate||0,replaysOnErrorSampleRate:e.replaysOnErrorSampleRate||0,sampleRate:e.errorSampleRate||0,sendDefaultPii:e.sendDefaultPii!==void 0&&e.sendDefaultPii,integrations:(()=>{const r=[];return e.tracesSampleRate&&e.tracesSampleRate>0&&r.push(Ia()),r})(),beforeSend:r=>{var s;return(s=e.release)!=null&&s.endsWith("@999.999.999")?null:r}}),e.tags&&Object.entries(e.tags).forEach(([r,s])=>{(function(o,a){_t().setTag(o,a)})(r,String(s))}),gr=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();
