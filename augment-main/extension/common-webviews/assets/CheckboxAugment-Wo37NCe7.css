.c-checkbox-augment{width:var(--checkbox-size, 16px);height:var(--checkbox-size, 16px);flex-shrink:0;border-radius:3px;border:1px solid var(--checkbox-border, var(--ds-color-neutral-a7));background-color:var(--checkbox-bg, transparent);transition:background-color .15s ease-in-out,border-color .15s ease-in-out;position:relative;cursor:pointer;--ring-color: var(--checkbox-ring, var(--ds-color-accent-a5))}.c-checkbox-augment:focus-visible,.c-checkbox-augment.c-checkbox-augment--active{outline:2px solid var(--ring-color);outline-offset:2px}.c-checkbox-augment:hover,.c-checkbox-augment.c-checkbox-augment--hover{background-color:var(--checkbox-hover-bg, var(--checkbox-bg));border-color:var(--checkbox-hover-border, var(--checkbox-border))}.c-checkbox-augment:disabled{cursor:not-allowed;color:var(--ds-color-neutral-a8);--checkbox-border: var(--checkbox-disabled-border);--checkbox-bg: var(--checkbox-disabled-bg, var(--ds-color-neutral-a3));--checkbox-hover-border: var(--checkbox-border)}.c-checkbox-augment__indicator{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;pointer-events:none}.c-checkbox-augment[data-state=checked]:not(:disabled){background-color:var(--checkbox-checked-bg, var(--checkbox-bg));border-color:var(--checkbox-checked-border, var(--checkbox-border));color:var(--checkbox-checked-color, white)}.c-checkbox-augment[data-state=checked]:not(:disabled):hover,.c-checkbox-augment[data-state=checked]:not(:disabled).checkbox-hover{background-color:var( --checkbox-checked-hover-bg, var(--checkbox-checked-bg, var(--checkbox-bg)) );border-color:var( --checkbox-checked-hover-border, var(--checkbox-checked-border, var(--checkbox-border)) )}.c-checkbox-augment[data-state=indeterminate]:not(:disabled){background-color:var(--checkbox-indeterminate-bg, var(--checkbox-bg));border-color:var(--checkbox-indeterminate-border, var(--checkbox-border));color:var(--checkbox-indeterminate-color)}.c-checkbox-augment[data-state=indeterminate]:not(:disabled):hover,.c-checkbox-augment[data-state=indeterminate]:not(:disabled).checkbox-hover{background-color:var( --checkbox-indeterminate-hover-bg, var(--checkbox-indeterminate-bg, var(--checkbox-bg)) );border-color:var( --checkbox-indeterminate-hover-border, var(--checkbox-indeterminate-border, var(--checkbox-border)) )}.c-checkbox-augment--size-1{--checkbox-size: 14px}.c-checkbox-augment--size-2{--checkbox-size: 16px}.c-checkbox-augment--size-3{--checkbox-size: 20px}.c-checkbox-augment--variant-primary{--checkbox-border: var(--ds-color-neutral-a7);--checkbox-bg: transparent;--checkbox-disabled-border: var(--ds-color-neutral-a6);--checkbox-hover-border: var(--ds-color-neutral-a8);--checkbox-checked-bg: var(--ds-color-accent-9);--checkbox-checked-border: transparent;--checkbox-checked-hover-bg: var(--ds-color-accent-10);--checkbox-indeterminate-bg: var(--ds-color-accent-a2);--checkbox-indeterminate-border: var(--ds-color-accent-a7);--checkbox-indeterminate-color: var(--ds-color-accent-11);--checkbox-indeterminate-hover-border: var(--ds-color-accent-a8);--checkbox-indeterminate-hover-bg: var(--ds-color-accent-a3)}.c-checkbox-augment--variant-soft{--checkbox-bg: var(--ds-color-accent-a5);--checkbox-border: transparent;--checkbox-disabled-border: transparent;--checkbox-hover-bg: var(--ds-color-accent-a6);--checkbox-checked-bg: var(--ds-color-accent-a5);--checkbox-checked-color: var(--ds-color-accent-a11);--checkbox-checked-hover-bg: var(--ds-color-accent-a6);--checkbox-indeterminate-bg: transparent;--checkbox-indeterminate-border: var(--ds-color-accent-a6);--checkbox-indeterminate-color: var(--ds-color-accent-11);--checkbox-indeterminate-hover-border: var(--ds-color-accent-a7)}
