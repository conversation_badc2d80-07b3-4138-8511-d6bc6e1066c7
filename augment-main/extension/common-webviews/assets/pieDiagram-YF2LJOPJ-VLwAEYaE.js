import{p as E}from"./chunk-TMUBEWPD-BzL6NEZ_.js";import{T as y,O,aF as L,_ as m,g as N,s as P,a as V,b as G,o as I,p as _,l as F,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-cgapx9im.js";import{p as Y}from"./gitGraph-YCYPL57B-D0pfzQ-l.js";import{d as B}from"./arc-BwU2Fz0B.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./GuardedIcon-BFT2yJIo.js";import"./index-BzB60MCy.js";import"./preload-helper-Dv6uf1Os.js";import"./await-D3vig32v.js";import"./chat-model-context-39sqbIF3.js";import"./IconButtonAugment-CR0fVrwD.js";import"./partner-mcp-utils-DYgwDbFd.js";import"./index-DON3DCoY.js";import"./async-messaging-Bp70swAv.js";import"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./CardAugment-DVbbQqkH.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./BaseTextInput-BaUpeUef.js";import"./trash-ByjpApta.js";import"./index-C_brRns6.js";import"./expand-CPL6rEzo.js";import"./toggleHighContrast-C7wSWUJK.js";import"./index-CWns8XM2.js";import"./ButtonAugment-CWDjQYWT.js";import"./MaterialIcon-D9dP7dAZ.js";import"./lodash-DkRojHDE.js";import"./Filespan-9fd1tyrF.js";import"./OpenFileButton-CVOPVJP1.js";import"./index-B528snJk.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";import"./SuccessfulButton-Btt1yYx9.js";import"./CopyButton-DSjqZJL1.js";import"./copy-oYDqgVJ5.js";import"./LanguageIcon-CJlkgv5n.js";import"./CollapseButtonAugment-Cnr_pz_w.js";import"./ellipsis-DBzJOEA0.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-b0Ugp2il.js";import"./message-broker-BygIEqPd.js";import"./file-type-utils-Zb3vtfL9.js";import"./TextAreaAugment-DIwZ8JEM.js";import"./clock-B1EQOiug.js";import"./augment-logo-CNPb11gr.js";import"./index-BuQDLh4_.js";import"./branch-BXbTQdeh.js";import"./index-JMsuML6t.js";import"./CalloutAugment-C9yL-4XM.js";import"./_baseUniq-BrcrwDU6.js";import"./_basePickBy-C7hsU9lQ.js";import"./clone-DvxDEQG-.js";import"./init-g68aIKmP.js";function tt(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function et(t){return t}var rt=X.pie,R={sections:new Map,showData:!1},M=R.sections,z=R.showData,at=structuredClone(rt),W={getConfig:m(()=>structuredClone(at),"getConfig"),clear:m(()=>{M=new Map,z=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:P,getAccDescription:N,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),F.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},it=m((t,r)=>{E(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Y("pie",t);F.debug(r),it(r,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const r=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function a(e){var i,s,n,T,g,l=(e=L(e)).length,v=0,A=new Array(l),d=new Array(l),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/l,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<l;++i)(g=d[A[i]=i]=+p(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-l*b)/v:0;i<l;++i,f=T)s=A[i],T=f+((g=d[s])>0?g*n:0)+b,d[s]={data:e[s],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return a.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),a):p},a.sortValues=function(e){return arguments.length?(u=e,c=null,a):u},a.sort=function(e){return arguments.length?(c=e,u=null,a):c},a.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),a):w},a.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),a):S},a.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),a):$},a}().value(p=>p.value)(r)},"createPieArcs"),me={parser:nt,db:W,renderer:{draw:m((t,r,p,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,a=450,e=a,i=J(r),s=i.append("g");s.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,l=Math.min(e,a)/2-40,v=B().innerRadius(0).outerRadius(l),A=B().innerRadius(l*g).outerRadius(l*g);s.append("circle").attr("cx",0).attr("cy",0).attr("r",l+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);s.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),s.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),s.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=s.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,a,D,S.useMaxWidth)},"draw")},styles:ot};export{me as diagram};
