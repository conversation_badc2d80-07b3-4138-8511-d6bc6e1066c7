import{x as s,y as k,S as D,V as E,u as w,C as H,E as L,b as a,v as R,N as X,a5 as F,A as h,G as v,H as f,z as J,I as $,a8 as K,P as x,D as M,m as O,X as Q,ac as b}from"./GuardedIcon-BFT2yJIo.js";import{C as U}from"./copy-oYDqgVJ5.js";import{S as W}from"./SuccessfulButton-Btt1yYx9.js";var Y=k("<div><!></div>");function Z(u,t){let n=s(t,"marginRight",3,0);var r=Y(),d=w(r);D(d,()=>t.children??E),H(()=>L(r,1,`c-icon-size c-icon-size--size-${t.size??""} c-icon-size--margin-right-${n()??""}`,"svelte-1x5zy5h")),a(u,r)}var _=k('<span class="c-copy-button svelte-tq93gm"><!></span>');function it(u,t){const n=R(t);X(t,!1);let r=s(t,"size",8,1),d=s(t,"iconSize",24,()=>{}),S=s(t,"variant",8,"ghost-block"),N=s(t,"color",8,"neutral"),l=s(t,"text",24,()=>{}),P=s(t,"tooltip",8,"Copy"),I=s(t,"stickyColor",8,!1),T=s(t,"onCopy",8,async()=>{if(l()!==void 0){b(g,!0);try{await Promise.all([navigator.clipboard.writeText(typeof l()=="string"?l():await l()()),(e=250,new Promise(i=>setTimeout(i,e,p)))])}finally{b(g,!1)}var e,p;return"success"}}),V=s(t,"tooltipNested",24,()=>{}),g=O(!1);F();var m=_(),j=w(m);const q=M(()=>({neutral:P(),success:"Copied!"}));W(j,{get defaultColor(){return N()},get size(){return r()},get variant(){return S()},get loading(){return x(g)},get stickyColor(){return I()},get tooltip(){return x(q)},stateVariant:{success:"soft"},onClick:T(),icon:h(()=>!n.text),get tooltipNested(){return V()},children:(e,p)=>{var i=v(),y=f(i);$(y,t,"text",{},null),a(e,i)},$$slots:{default:!0,iconLeft:(e,p)=>{Z(e,{get size(){return d()},children:(i,y)=>{var z=v(),A=f(z),G=o=>{var c=v(),C=f(c);$(C,t,"icon",{},null),a(o,c)},B=o=>{K(o,{name:"clipboard",children:(c,C)=>{U(c,{})},$$slots:{default:!0}})};J(A,o=>{h(()=>n.icon)?o(G):o(B,!1)}),a(i,z)},$$slots:{default:!0}})}}}),a(u,m),Q()}export{it as C,Z as I};
