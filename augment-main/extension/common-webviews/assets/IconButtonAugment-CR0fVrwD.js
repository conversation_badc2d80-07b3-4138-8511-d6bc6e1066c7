import{C as F,az as at,aV as nt,aW as st,aX as ot,aY as y,aE as it,A as rt,aw as lt,ad as ct,aT as dt,K as ut,ao as z,aZ as C,a_ as ht,a$ as gt,b0 as vt,N as J,au as bt,x as r,y as P,a as mt,z as wt,b as $,X as K,O as ft,aN as $t,P as k,R as S,Q as pt,u as _,H as A,aO as yt,B as zt,S as j,V as H,G,Y as Ct,r as B,ap as Q,E as kt}from"./GuardedIcon-BFT2yJIo.js";function Pt(n,t,e=!1,a=!1,s=!1){var l=n,d="";F(()=>{var h=at;if(d!==(d=t()??"")&&(h.nodes_start!==null&&(nt(h.nodes_start,h.nodes_end),h.nodes_start=h.nodes_end=null),d!=="")){var g=d+"";e?g=`<svg>${g}</svg>`:a&&(g=`<math>${g}</math>`);var u=st(g);if((e||a)&&(u=y(u)),ot(y(u),u.lastChild),e||a)for(;y(u);)l.before(y(u));else l.before(u)}})}function Gt(n,t,e){it(()=>{var a=rt(()=>t(n,e==null?void 0:e())||{});if(e&&(a!=null&&a.update)){var s=!1,l={};lt(()=>{var d=e();ct(d),s&&dt(l,d)&&(l=d,a.update(d))}),s=!0}if(a!=null&&a.destroy)return()=>a.destroy()})}function c(n,t){var l;var e=(l=n.$$events)==null?void 0:l[t.type],a=ut(e)?e.slice():e==null?[]:[e];for(var s of a)s.call(this,t)}var N=(n=>(n.vscode="vscode",n.jetbrains="jetbrains",n.web="web",n))(N||{});const q="data-vscode-theme-kind";function St(){return self.acquireVsCodeApi!==void 0}function Y(){ht(function(){const n=document.body.getAttribute(q);if(n)return At[n]}()),gt(function(){const n=document.body.getAttribute(q);if(n)return jt[n]}())}function _t(){if(self.acquireVsCodeApi===void 0)throw new Error("acquireVsCodeAPI not available");return function(){new MutationObserver(Y).observe(document.body,{attributeFilter:[q],attributes:!0}),Y()}(),{...self.acquireVsCodeApi(),clientType:N.vscode}}const At={"vscode-dark":z.dark,"vscode-high-contrast":z.dark,"vscode-light":z.light,"vscode-high-contrast-light":z.light},jt={"vscode-dark":C.regular,"vscode-light":C.regular,"vscode-high-contrast":C.highContrast,"vscode-high-contrast-light":C.highContrast};function Ht(){return window.augment_intellij!==void 0}function V(n,t,e={},a){return vt({name:n,op:t,attributes:e},s=>{try{const l=a(s);return s==null||s.setStatus({code:1}),l}catch(l){throw s==null||s.setStatus({code:2,message:l instanceof Error?l.message:"Unknown error"}),l}})}function Tt(){var n;if(St())return _t();if(Ht())return function(){const t=window.augment_intellij;if(t===void 0||t.setState===void 0||t.getState===void 0||t.postMessage===void 0)throw new Error("Augment IntelliJ host not available");window.augment=window.augment||{};let e=!1;return window.augment.host={clientType:N.jetbrains,setState:a=>V("hostSetState","hostOperation",{hostClientType:"jetbrains",hostInitialized:e},s=>{e||(console.error("Host not initialized"),s==null||s.setStatus({code:2,message:"Host not initialized"})),t.setState(a)}),getState:()=>V("hostGetState","hostOperation",{hostClientType:"jetbrains",hostInitialized:e},a=>(e||(console.error("Host not initialized"),a==null||a.setStatus({code:2,message:"Host not initialized"})),t.getState())),postMessage:a=>V("hostPostMessage","hostOperation",{hostClientType:"jetbrains",hostInitialized:e,messageType:(a==null?void 0:a.type)??"unknown"},s=>{e||(console.error("Host not initialized"),s==null||s.setStatus({code:2,message:"Host not initialized"})),t.postMessage(a)}),initialize:async()=>{await t.initializationPromise,e=!0}},window.augment.host}();if(!((n=window.augment)!=null&&n.host))throw new Error("Augment host not available");return window.augment.host}function xt(){var n;return(n=window.augment)!=null&&n.host||(window.augment=window.augment||{},window.augment.host=Tt()),window.augment.host}const Bt=xt();function R(n){return String(n).replace(".","_")}var Et=P('<div class="c-base-btn__loading svelte-295ep0"><!></div> <span class="c-base-btn__hidden-content svelte-295ep0"><!></span>',1),It=P("<button><!></button>");function Ot(n,t){J(t,!0);const e=bt();let a=r(t,"size",3,2),s=r(t,"variant",3,"solid"),l=r(t,"color",3,"accent"),d=r(t,"disabled",3,!1),h=r(t,"readonly",3,!1),g=r(t,"highContrast",3,!1),u=r(t,"loading",3,!1),o=r(t,"alignment",3,"center"),p=r(t,"radius",3,"medium"),m=B(t,["$$slots","$$events","$$legacy","size","variant","color","disabled","readonly","highContrast","loading","alignment","radius","children"]),i=S(()=>t.class),X=S(()=>Ct(m,["class"]));var w=It();mt(w,(v,b,f,x,E,I,O,M,Z,D,L,tt,et)=>({...v,...b,class:f,disabled:d()||h()||u(),onclick:x,onkeyup:E,onkeydown:I,onmousedown:O,onmouseover:M,onfocus:Z,onmouseleave:D,onblur:L,oncontextmenu:tt,...k(X),[pt]:et}),[()=>ft(l()),()=>$t(p()),()=>`c-base-btn c-base-btn--size-${R(a())} c-base-btn--${s()} c-base-btn--${l()} ${k(i)} c-base-btn--alignment-${o()}`,()=>e("click"),()=>e("keyup"),()=>e("keydown"),()=>e("mousedown"),()=>e("mouseover"),()=>e("focus"),()=>e("mouseleave"),()=>e("blur"),()=>e("contextmenu"),()=>({"c-base-btn--highContrast":g(),"c-base-btn--loading":u(),"c-base-btn--readonly":h()})],"svelte-295ep0");var T=_(w),U=v=>{var b=Et(),f=A(b),x=_(f);const E=S(()=>function(M){switch(M){case 0:case .5:case 1:return 1;case 2:case 3:return 2;case 4:return 3}}(a()));yt(x,{get size(){return k(E)}});var I=zt(f,2),O=_(I);j(O,()=>t.children??H),$(v,b)},W=v=>{var b=G(),f=A(b);j(f,()=>t.children??H),$(v,b)};wt(T,v=>{u()?v(U):v(W,!1)}),$(n,w),K()}var Mt=P("<div><!></div>");function Vt(n,t){J(t,!0);let e=r(t,"size",3,2),a=r(t,"variant",3,"solid"),s=r(t,"color",3,"accent"),l=r(t,"highContrast",3,!1),d=r(t,"disabled",3,!1),h=r(t,"radius",3,"medium"),g=r(t,"loading",3,!1),u=B(t,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","class","loading","children"]);var o=Mt(),p=_(o);const m=S(()=>d()||void 0);Ot(p,Q({get size(){return e()},get variant(){return a()},get color(){return s()},get highContrast(){return l()},get disabled(){return k(m)},get radius(){return h()},get loading(){return g()},get class(){return t.class}},()=>u,{$$events:{click(i){c.call(this,t,i)},keyup(i){c.call(this,t,i)},keydown(i){c.call(this,t,i)},mousedown(i){c.call(this,t,i)},mouseover(i){c.call(this,t,i)},focus(i){c.call(this,t,i)},mouseleave(i){c.call(this,t,i)},blur(i){c.call(this,t,i)},contextmenu(i){c.call(this,t,i)}},children:(i,X)=>{var w=G(),T=A(w);j(T,()=>t.children??H),$(i,w)},$$slots:{default:!0}})),F(i=>kt(o,1,i,"svelte-1mz435m"),[()=>`c-icon-btn c-icon-btn--size-${R(e())}`]),$(n,o),K()}function Nt(n,t){let e=r(t,"size",3,2),a=r(t,"variant",3,"solid"),s=r(t,"color",3,"neutral"),l=r(t,"highContrast",3,!1),d=r(t,"disabled",3,!1),h=r(t,"radius",3,"medium"),g=r(t,"loading",3,!1),u=B(t,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","loading","children"]);Vt(n,Q({get size(){return e()},get variant(){return a()},get color(){return s()},get highContrast(){return l()},get disabled(){return d()},get radius(){return h()},get loading(){return g()}},()=>u,{$$events:{click(o){c.call(this,t,o)},keyup(o){c.call(this,t,o)},keydown(o){c.call(this,t,o)},mousedown(o){c.call(this,t,o)},mouseover(o){c.call(this,t,o)},focus(o){c.call(this,t,o)},mouseleave(o){c.call(this,t,o)},blur(o){c.call(this,t,o)},contextmenu(o){c.call(this,t,o)}},children:(o,p)=>{var m=G(),i=A(m);j(i,()=>t.children??H),$(o,m)},$$slots:{default:!0}}))}export{Ot as B,Vt as C,N as H,Nt as I,Gt as a,c as b,Bt as c,Ht as d,xt as g,Pt as h,St as i};
