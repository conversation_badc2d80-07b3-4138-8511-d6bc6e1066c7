import{at as st,N as rt,au as ct,aq as it,x as m,G as F,H as S,z as _,b as f,X as lt,y as D,u as C,P as V,R as W,B as j,S as I,V as H,C as X,a0 as dt,as as ut,T as vt,a7 as ft,a1 as mt}from"./GuardedIcon-BFT2yJIo.js";import{a as yt}from"./IconButtonAugment-CR0fVrwD.js";import{C as pt,s as J}from"./CardAugment-DVbbQqkH.js";import{F as B}from"./focusTrapStack-CDv9v5kQ.js";function Q(e){return[...e.querySelectorAll("*")].filter(t=>t.tabIndex>=0)}const K=(e,t={})=>{let{enabled:a=!0,initialFocus:d=null,restoreFocusOnClose:u=!0}=t,n=null,o=null,q=null;function g(){const i=Q(e);return[i[0]||e,i[i.length-1]||e]}function L(){if(!a)return;B.add(e),n=document.activeElement;const i=d||g()[0];i&&i.focus();const b=y=>{if(y.key!=="Tab"||!B.isActive(e))return;const r=document.activeElement;if(!e.contains(r))return;const[p,s]=g();y.shiftKey?r===p&&(y.preventDefault(),s.focus()):r===s&&(y.preventDefault(),p.focus())},A=y=>{if(!B.isActive(e))return;const r=y.target;if(e===r)return void(o==null?void 0:o.focus());if(e.contains(r))return void(o=r);if(Q(e).length===0)return;const[p]=g(),s=o||p;r!==s&&(s===e&&e.tabIndex<0||(document.body.contains(r)&&(o=s,s.focus()),r&&r!==document.body||(o=s,s.focus())))};document.addEventListener("keydown",b),document.addEventListener("focusin",A),q=()=>{document.removeEventListener("keydown",b),document.removeEventListener("focusin",A)}}function $(){B.remove(e),q&&(q(),q=null),u&&B.isEmpty()&&n&&typeof n.focus=="function"&&n.focus()}return a&&L(),{update(i={}){const b=a;a=i.enabled??a,d=i.initialFocus??d,u=i.restoreFocusOnClose??u,!b&&a?L():b&&!a&&$()},destroy(){$()}}};function ht(e,t,a,d,u){var n,o;t()||(a("cancel"),(n=d())==null||n()),a("backdropClick"),(o=u())==null||o()}function wt(e,t,a,d,u){var n,o;e.key!=="Escape"||t()||(e.preventDefault(),a("cancel"),(n=d())==null||n()),a("keydown",e),(o=u())==null||o(e)}var bt=D('<div class="c-modal-header svelte-1hwqfwo"><!></div>'),kt=D('<div class="c-modal-body svelte-1hwqfwo"><!></div>'),Et=D('<div class="c-modal-footer svelte-1hwqfwo"><!></div>'),_t=D('<div class="c-modal-content svelte-1hwqfwo"><!> <!> <!></div>'),qt=D('<div class="c-modal-backdrop svelte-1hwqfwo" role="presentation"><div class="c-modal svelte-1hwqfwo" role="dialog" aria-modal="true" tabindex="0"><!></div></div>');function At(e,t){rt(t,!0);const a=ct(),d=it();let u=m(t,"show",3,!1),n=m(t,"title",3,""),o=m(t,"maxWidth",3,"400px"),q=m(t,"preventBackdropClose",3,!1),g=m(t,"preventEscapeClose",3,!1),L=m(t,"ariaLabelledBy",3,"modal-title"),$=m(t,"oncancel",19,()=>{}),i=m(t,"onbackdropClick",19,()=>{}),b=m(t,"onkeydown",19,()=>{});var A=F(),y=S(A),r=p=>{var s=qt();s.__click=[ht,q,d,$,i],s.__keydown=[wt,g,d,$,b];var x=C(s),U=W(()=>J(a("click")));x.__click=function(...k){var h;(h=V(U))==null||h.apply(this,k)};var Y=W(()=>J(a("keydown")));x.__keydown=function(...k){var h;(h=V(Y))==null||h.apply(this,k)};var Z=C(x);pt(Z,{variant:"soft",size:3,children:(k,h)=>{var M=_t(),N=C(M),tt=l=>{var w=bt(),z=C(w),O=c=>{var v=F(),E=S(v);I(E,()=>t.header??H),f(c,v)},T=(c,v)=>{var E=G=>{vt(G,{get id(){return L()},size:3,weight:"bold",color:"primary",children:(nt,xt)=>{var R=ft();X(()=>mt(R,n())),f(nt,R)},$$slots:{default:!0}})};_(c,G=>{n()&&G(E)},v)};_(z,c=>{t.header?c(O):c(T,!1)}),f(l,w)};_(N,l=>{(n()||t.header)&&l(tt)});var P=j(N,2),et=l=>{var w=kt(),z=C(w),O=c=>{var v=F(),E=S(v);I(E,()=>t.body),f(c,v)},T=c=>{var v=F(),E=S(v);I(E,()=>t.children??H),f(c,v)};_(z,c=>{t.body?c(O):c(T,!1)}),f(l,w)};_(P,l=>{(t.body||t.children)&&l(et)});var at=j(P,2),ot=l=>{var w=Et(),z=C(w);I(z,()=>t.footer??H),f(l,w)};_(at,l=>{t.footer&&l(ot)}),f(k,M)},$$slots:{default:!0}}),yt(x,(k,h)=>K==null?void 0:K(k,h),()=>({enabled:u()})),X(()=>{dt(x,"aria-labelledby",L()),ut(x,`max-width: ${o()??""}`)}),f(p,s)};_(y,p=>{u()&&p(r)}),f(e,A),lt()}st(["click","keydown"]);export{At as M,K as t};
