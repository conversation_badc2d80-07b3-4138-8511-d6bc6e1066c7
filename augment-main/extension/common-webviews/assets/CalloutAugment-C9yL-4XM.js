import{N as G,x as a,y as v,a as I,O as Q,P as h,Q as R,R as f,T as S,H as T,z as V,B as X,S as g,V as z,u as o,b as i,X as Y,Y as k,r as q}from"./GuardedIcon-BFT2yJIo.js";var A=v('<div class="c-callout-icon svelte-149tvwv"><!></div>'),D=v('<!> <div class="c-callout-body svelte-149tvwv"><!></div>',1),E=v("<div><!></div>");function J(C,s){G(s,!0);let e=a(s,"color",3,"info"),w=a(s,"variant",3,"soft"),r=a(s,"size",3,2),y=a(s,"highContrast",3,!1),b=q(s,["$$slots","$$events","$$legacy","color","variant","size","highContrast","icon","children"]);const p=r();let x=f(()=>s.class),B=f(()=>k(b,["class"]));var t=E();I(t,(l,n)=>({...l,class:`c-callout c-callout--${e()} c-callout--${w()} c-callout--size-${r()} ${h(x)}`,...h(B),[R]:n}),[()=>Q(e()),()=>({"c-callout--highContrast":y()})],"svelte-149tvwv");var H=o(t);S(H,{get size(){return p},children:(l,n)=>{var $=D(),d=T($),N=c=>{var u=A(),j=o(u);g(j,()=>s.icon??z),i(c,u)};V(d,c=>{s.icon&&c(N)});var O=X(d,2),P=o(O);g(P,()=>s.children??z),i(l,$)},$$slots:{default:!0}}),i(C,t),Y()}export{J as C};
