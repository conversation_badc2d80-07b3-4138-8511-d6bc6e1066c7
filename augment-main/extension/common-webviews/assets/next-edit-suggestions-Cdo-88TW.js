const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-DZedQK9e.css","./NextEditSuggestions-B7QsGEsE.js","./GuardedIcon-BFT2yJIo.js","./GuardedIcon-DzY30p2i.css","./next-edit-types-904A5ehg.js","./IconButtonAugment-CR0fVrwD.js","./IconButtonAugment-tJfihrx1.css","./IconFilePath-C_5A8mMD.js","./LanguageIcon-CJlkgv5n.js","./LanguageIcon-D78BqCXT.css","./index-JMsuML6t.js","./IconFilePath-BVaLv7mP.css","./async-messaging-Bp70swAv.js","./Drawer-DMvrLFVH.js","./index-BzB60MCy.js","./ellipsis-DBzJOEA0.js","./Drawer-u8LRIFRf.css","./keypress-DD1aQVr0.js","./VSCodeCodicon-4Sfbv3fq.js","./VSCodeCodicon-DVaocTud.css","./monaco-render-utils-DfwV7QLY.js","./toggleHighContrast-C7wSWUJK.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./index-CWns8XM2.js","./index-BlHvDt2c.css","./isObjectLike-D6mfjXx_.js","./ButtonAugment-CWDjQYWT.js","./ButtonAugment-Fl6YbyF2.css","./NextEditSuggestions-Q98kphIR.css"])))=>i.map(i=>d[i]);
import{N as R,x as h,ag as J,ah as Q,ai as Y,ax as Z,G as ee,H as ae,y as $,a8 as O,u as g,B as v,T as y,a7 as w,C as b,a1 as _,b as s,aj as te,aO as ie,P as D,X as x,ac as ne,ap as oe,a5 as re,aP as se}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{_ as G}from"./preload-helper-Dv6uf1Os.js";import{a as le}from"./await-D3vig32v.js";import{t as T,a as C}from"./index-BzB60MCy.js";import{A as q}from"./augment-logo-CNPb11gr.js";import{M as ge}from"./index-CWns8XM2.js";const F={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};var de=$('<div class="l-component svelte-1foy1hj"><!></div>'),me=$('<code class="svelte-1foy1hj"> </code>'),ce=$('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container l-loader-error-message svelte-1foy1hj"><!> <!></div></div>'),he=$('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container svelte-1foy1hj"><!> <!></div></div>');se(function(B,H){R(H,!1);const N=async()=>(await G(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await G(async()=>{const{default:I}=await import("./NextEditSuggestions-B7QsGEsE.js");return{default:I}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]),import.meta.url)).default);re(),ge.Root(B,{children:(I,ue)=>{(function(V,t){R(t,!0);let W=h(t,"minDisplayTime",3,1e3),A=h(t,"title",3,"Augment Code"),K=h(t,"randomize",3,!0),j=h(t,"retryCount",3,3),P=h(t,"loadingMessages",19,()=>F.messages),L=h(t,"errorMessages",19,()=>F.errors),S=h(t,"errorMessage",3,"An error occurred while loading. Please try again later."),f=P().slice(1),z=J(Q(P()[0])),p="loading",E=new AbortController;Y(()=>E.abort()),Z(async function(){f.length===0&&(f=[...p==="retry"?L():P()]),ne(z,p==="error"?S():f.splice(p!=="retry"&&K()?Math.floor(Math.random()*f.length):0,1)[0]??"",!0)});var M=ee(),U=ae(M);le(U,async function d(i=0){try{const[n]=await Promise.all([t.loader(),(e=W(),a=E.signal,new Promise(l=>{const u=setTimeout(l,e);a&&a.addEventListener("abort",()=>{clearTimeout(u),l()})}))]);return n}catch(n){if(console.error("Failed to load component",n),p="retry",i===0&&(f=[...L()]),j()&&i<=j())return await d(i+1);throw p="error",new Error("Failed to load component after retrying. Please try again later.")}var e,a},d=>{var i=he(),e=g(i),a=g(e);O(a,{name:"sparkles",children:(m,o)=>{q(m)},$$slots:{default:!0}});var n=v(a,2);y(n,{size:2,children:(m,o)=>{var r=w();b(()=>_(r,A())),s(m,r)},$$slots:{default:!0}});var l=v(e,2),u=g(l);ie(u,{});var k=v(u,2);y(k,{size:1,color:"secondary",children:(m,o)=>{var r=w();b(()=>_(r,D(z))),s(m,r)},$$slots:{default:!0}}),T(3,i,()=>C),s(d,i)},(d,i)=>{var e=de(),a=g(e);te(a,()=>D(i),(n,l)=>{l(n,oe(()=>t.componentProps))}),T(3,e,()=>C),s(d,e)},(d,i)=>{var e=ce(),a=g(e),n=g(a);O(n,{name:"sparkles",children:(o,r)=>{q(o)},$$slots:{default:!0}});var l=v(n,2);y(l,{size:3,children:(o,r)=>{var c=w();b(()=>_(c,A())),s(o,c)},$$slots:{default:!0}});var u=v(a,2),k=g(u);y(k,{size:3,children:(o,r)=>{var c=w("An Error Occurred.");s(o,c)},$$slots:{default:!0}});var m=v(k,2);y(m,{size:1,children:(o,r)=>{var c=me(),X=g(c);b(()=>_(X,S())),s(o,c)},$$slots:{default:!0}}),T(3,e,()=>C),s(d,e)}),s(V,M),x()})(I,{loader:N,componentProps:{}})},$$slots:{default:!0}}),x()},{target:document.getElementById("app")});
