import{N as $,x as t,ar as d,y as u,C as w,E as k,as as x,P as i,R as r,ag as y,a0 as L,a1 as j,b as G,X as I,u as N,ac as l}from"./GuardedIcon-BFT2yJIo.js";var R=u("<span> </span>");function C(n,a){$(a,!0);let c=t(a,"class",3,""),m=t(a,"iconName",3,""),o=t(a,"fill",3,!1),g=t(a,"grade",3,"normal"),h=t(a,"title",19,()=>{}),v=r(()=>o()?"1":"0"),b=r(()=>o()?"700":"400"),e=y(void 0);d(()=>{switch(g()){case"low":l(e,"-25");break;case"normal":l(e,"0");break;case"high":l(e,"200")}});var s=R(),f=N(s);w(()=>{k(s,1,`material-symbols-outlined ${c()}`,"svelte-htlsjs"),x(s,`font-variation-settings: 'FILL' ${i(v)??""}, 'wght' ${i(b)??""}, 'GRAD' ${i(e)??""};`),L(s,"title",h()),j(f,m())}),G(n,s),I()}export{C as M};
