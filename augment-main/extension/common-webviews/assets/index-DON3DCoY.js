var gu=Object.defineProperty;var yu=(e,t,n)=>t in e?gu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var y=(e,t,n)=>yu(e,typeof t!="symbol"?t+"":t,n);import{a as vu}from"./async-messaging-Bp70swAv.js";import{aR as Jn,W as w,w as bn,f as Ji,b as j,b3 as Mr,N as pe,x as F,al as Rs,ak as J,a3 as Ye,a4 as Mt,a5 as be,F as _u,ay as bu,y as Ne,I as ue,u as Le,C as Lt,E as Dt,aa as Ee,X as he,a6 as Ge,ad as _t,ab as Eu,ac as nt,m as rt,P as H,v as Su,t as Q,ap as et,H as ke,z as Qs,A as En,B as ea,T as Qi,G as Me,R as ta,D as Lr,da as na,Y as ku,a as Tu,a8 as wu,bk as ra,Z as Iu,ai as Nu,S as Cu,V as xu,$ as Ru,J as Au}from"./GuardedIcon-BFT2yJIo.js";import{E as se,S as Te,R as ye,P as le,C as Ou}from"./chat-types-BDRYChZT.js";import{n as Pu}from"./file-paths-BcSg4gks.js";import{a as Mu,c as Lu,_ as Du,i as As}from"./isObjectLike-D6mfjXx_.js";import{a as Dr,b as Fu,B as Uu,h as $u}from"./IconButtonAugment-CR0fVrwD.js";import{c as Bu,e as pn,f as ju,C as Gu,R as qu,b as dt,a as hn,g as sa}from"./CardAugment-DVbbQqkH.js";import{F as aa}from"./focusTrapStack-CDv9v5kQ.js";import{T as Vu}from"./TextFieldAugment-DfCJMerV.js";function Os(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var v;function Yu(){let e=0,t=0;for(let r=0;r<28;r+=7){let s=this.buf[this.pos++];if(e|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let r=3;r<=31;r+=7){let s=this.buf[this.pos++];if(t|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function rr(e,t,n){for(let a=0;a<28;a+=7){const i=e>>>a,o=!(!(i>>>7)&&t==0),l=255&(o?128|i:i);if(n.push(l),!o)return}const r=e>>>28&15|(7&t)<<4,s=!!(t>>3);if(n.push(255&(s?128|r:r)),s){for(let a=3;a<31;a+=7){const i=t>>>a,o=!!(i>>>7),l=255&(o?128|i:i);if(n.push(l),!o)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(v||(v={}));const mn=4294967296;function ia(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let r=0,s=0;function a(i,o){const l=Number(e.slice(i,o));s*=n,r=r*n+l,r>=mn&&(s+=r/mn|0,r%=mn)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?eo(r,s):Ps(r,s)}function oa(e,t){if({lo:e,hi:t}=function(l,u){return{lo:l>>>0,hi:u>>>0}}(e,t),t<=2097151)return String(mn*t+e);const n=16777215&(e>>>24|t<<8),r=t>>16&65535;let s=(16777215&e)+6777216*n+6710656*r,a=n+8147497*r,i=2*r;const o=1e7;return s>=o&&(a+=Math.floor(s/o),s%=o),a>=o&&(i+=Math.floor(a/o),a%=o),i.toString()+ua(a)+ua(s)}function Ps(e,t){return{lo:0|e,hi:0|t}}function eo(e,t){return t=~t,e?e=1+~e:t+=1,Ps(e,t)}const ua=e=>{const t=String(e);return"0000000".slice(t.length)+t};function la(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function Ku(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var ca={};const B=Hu();function Hu(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof ca!="object"||ca.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),s=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const i=typeof a=="bigint"?a:BigInt(a);if(i>n||i<t)throw new Error(`invalid int64: ${a}`);return i},uParse(a){const i=typeof a=="bigint"?a:BigInt(a);if(i>s||i<r)throw new Error(`invalid uint64: ${a}`);return i},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,i)=>(e.setInt32(0,a,!0),e.setInt32(4,i,!0),e.getBigInt64(0,!0)),uDec:(a,i)=>(e.setInt32(0,a,!0),e.setInt32(4,i,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),da(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),pa(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),da(t),ia(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),pa(t),ia(t)),dec:(t,n)=>function(r,s){let a=Ps(r,s);const i=2147483648&a.hi;i&&(a=eo(a.lo,a.hi));const o=oa(a.lo,a.hi);return i?"-"+o:o}(t,n),uDec:(t,n)=>oa(t,n)}}function da(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function pa(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function Vt(e,t){switch(e){case v.STRING:return"";case v.BOOL:return!1;case v.DOUBLE:case v.FLOAT:return 0;case v.INT64:case v.UINT64:case v.SFIXED64:case v.FIXED64:case v.SINT64:return t?"0":B.zero;case v.BYTES:return new Uint8Array(0);default:return 0}}const De=Symbol.for("reflect unsafe local");function to(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(r=>r.localName===n)}function Zu(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(r,s){switch(r){case v.BOOL:return s===!1;case v.STRING:return s==="";case v.BYTES:return s instanceof Uint8Array&&!s.byteLength;default:return s==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function Ft(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function no(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function ro(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function Xe(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Fr(e,t){var n,r,s,a;if(Xe(e)&&De in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const i=t,o=e.field();return i.listKind==o.listKind&&i.scalar===o.scalar&&((n=i.message)===null||n===void 0?void 0:n.typeName)===((r=o.message)===null||r===void 0?void 0:r.typeName)&&((s=i.enum)===null||s===void 0?void 0:s.typeName)===((a=o.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Ur(e,t){var n,r,s,a;if(Xe(e)&&De in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const i=t,o=e.field();return i.mapKey===o.mapKey&&i.mapKind==o.mapKind&&i.scalar===o.scalar&&((n=i.message)===null||n===void 0?void 0:n.typeName)===((r=o.message)===null||r===void 0?void 0:r.typeName)&&((s=i.enum)===null||s===void 0?void 0:s.typeName)===((a=o.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Ms(e,t){return Xe(e)&&De in e&&"desc"in e&&Xe(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function so(e){const t=e.fields[0];return ao(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function ao(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const Wu=999,zu=998,Jt=2;function Se(e,t){if(Os(t,e))return t;const n=function(r){let s;if(function(a){switch(a.file.edition){case Wu:return!1;case zu:return!0;default:return a.fields.some(i=>i.presence!=Jt&&i.fieldKind!="message"&&!i.oneof)}}(r)){const a=ma.get(r);let i,o;if(a)({prototype:i,members:o}=a);else{i={},o=new Set;for(const l of r.members)l.kind!="oneof"&&(l.fieldKind!="scalar"&&l.fieldKind!="enum"||l.presence!=Jt&&(o.add(l),i[l.localName]=sr(l)));ma.set(r,{prototype:i,members:o})}s=Object.create(i),s.$typeName=r.typeName;for(const l of r.members)if(!o.has(l)){if(l.kind=="field"&&(l.fieldKind=="message"||(l.fieldKind=="scalar"||l.fieldKind=="enum")&&l.presence!=Jt))continue;s[l.localName]=sr(l)}}else{s={$typeName:r.typeName};for(const a of r.members)a.kind!="oneof"&&a.presence!=Jt||(s[a.localName]=sr(a))}return s}(e);return t!==void 0&&function(r,s,a){for(const i of r.members){let o,l=a[i.localName];if(l!=null){if(i.kind=="oneof"){const u=to(a,i);if(!u)continue;o=u,l=no(a,u)}else o=i;switch(o.fieldKind){case"message":l=Ls(o,l);break;case"scalar":l=io(o,l);break;case"list":l=Ju(o,l);break;case"map":l=Xu(o,l)}ro(s,o,l)}}}(e,n,t),n}function io(e,t){return e.scalar==v.BYTES?Ds(t):t}function Xu(e,t){if(Xe(t)){if(e.scalar==v.BYTES)return ha(t,Ds);if(e.mapKind=="message")return ha(t,n=>Ls(e,n))}return t}function Ju(e,t){if(Array.isArray(t)){if(e.scalar==v.BYTES)return t.map(Ds);if(e.listKind=="message")return t.map(n=>Ls(e,n))}return t}function Ls(e,t){if(e.fieldKind=="message"&&!e.oneof&&so(e.message))return io(e.message.fields[0],t);if(Xe(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Os(t,e.message))return Se(e.message,t)}return t}function Ds(e){return Array.isArray(e)?new Uint8Array(e):e}function ha(e,t){const n={};for(const r of Object.entries(e))n[r[0]]=t(r[1]);return n}const Qu=Symbol(),ma=new WeakMap;function sr(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Qu;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?Vt(e.scalar,e.longAsString):e.enum.values[0].number}class bt extends Error{constructor(t,n,r="FieldValueInvalidError"){super(n),this.name=r,this.field=()=>t}}const ar=Symbol.for("@bufbuild/protobuf/text-encoding");function Fs(){if(globalThis[ar]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[ar]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[ar]}var G;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(G||(G={}));const oo=34028234663852886e22,uo=-34028234663852886e22,lo=4294967295,co=2147483647,po=-2147483648;class ho{constructor(t=Fs().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let s=0;s<this.chunks.length;s++)t+=this.chunks[s].length;let n=new Uint8Array(t),r=0;for(let s=0;s<this.chunks.length;s++)n.set(this.chunks[s],r),r+=this.chunks[s].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(fa(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return ir(t),la(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(r){if(typeof r=="string"){const s=r;if(r=Number(r),Number.isNaN(r)&&s!=="NaN")throw new Error("invalid float32: "+s)}else if(typeof r!="number")throw new Error("invalid float32: "+typeof r);if(Number.isFinite(r)&&(r>oo||r<uo))throw new Error("invalid float32: "+r)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){fa(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){ir(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return ir(t),la(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=B.enc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=B.uEnc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}int64(t){let n=B.enc(t);return rr(n.lo,n.hi,this.buf),this}sint64(t){const n=B.enc(t),r=n.hi>>31;return rr(n.lo<<1^r,(n.hi<<1|n.lo>>>31)^r,this.buf),this}uint64(t){const n=B.uEnc(t);return rr(n.lo,n.hi,this.buf),this}}class el{constructor(t,n=Fs().decodeUtf8){this.decodeUtf8=n,this.varint64=Yu,this.uint32=Ku,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,r=7&t;if(n<=0||r<0||r>5)throw new Error("illegal tag: field no "+n+" wire type "+r);return[n,r]}skip(t,n){let r=this.pos;switch(t){case G.Varint:for(;128&this.buf[this.pos++];);break;case G.Bit64:this.pos+=4;case G.Bit32:this.pos+=4;break;case G.LengthDelimited:let s=this.uint32();this.pos+=s;break;case G.StartGroup:for(;;){const[a,i]=this.tag();if(i===G.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(i,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(r,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return B.dec(...this.varint64())}uint64(){return B.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,B.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return B.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return B.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function ir(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>co||e<po)throw new Error("invalid int32: "+e)}function fa(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>lo||e<0)throw new Error("invalid uint32: "+e)}function ga(e,t,n){const r=Us(e,n);if(r!==!0)return new bt(e,`list item #${t+1}: ${Sn(e,n,r)}`)}function Us(e,t){return e.scalar!==void 0?mo(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):Ms(t,e.message)}function mo(e,t){switch(t){case v.DOUBLE:return typeof e=="number";case v.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>oo||e<uo)||`${e.toFixed()} out of range`);case v.INT32:case v.SFIXED32:case v.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>co||e<po)||`${e.toFixed()} out of range`);case v.FIXED32:case v.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>lo||e<0)||`${e.toFixed()} out of range`);case v.BOOL:return typeof e=="boolean";case v.STRING:return typeof e=="string"&&(Fs().checkUtf8(e)||"invalid UTF8");case v.BYTES:return e instanceof Uint8Array;case v.INT64:case v.SFIXED64:case v.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return B.parse(e),!0}catch{return`${e} out of range`}return!1;case v.FIXED64:case v.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return B.uParse(e),!0}catch{return`${e} out of range`}return!1}}function Sn(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${kn(t)}`,e.scalar!==void 0?`expected ${function(r){switch(r){case v.STRING:return"string";case v.BOOL:return"boolean";case v.INT64:case v.SINT64:case v.SFIXED64:return"bigint (int64)";case v.UINT64:case v.FIXED64:return"bigint (uint64)";case v.BYTES:return"Uint8Array";case v.DOUBLE:return"number (float64)";case v.FLOAT:return"number (float32)";case v.FIXED32:case v.UINT32:return"number (uint32)";case v.INT32:case v.SFIXED32:case v.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${fo(e.message)}`+n}function kn(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Fr(e)?go(e.field()):Ur(e)?yo(e.field()):Ms(e)?fo(e.desc):Os(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function fo(e){return`ReflectMessage (${e.typeName})`}function go(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${v[e.scalar]})`}}function yo(e){switch(e.mapKind){case"message":return`ReflectMap (${v[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${v[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${v[e.mapKey]}, ${v[e.scalar]})`}}function Qn(e,t,n=!0){return new vo(e,t,n)}class vo{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,r)=>n.number-r.number)}constructor(t,n,r=!0){this.lists=new Map,this.maps=new Map,this.check=r,this.desc=t,this.message=this[De]=n??Se(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return It(this.message,t),to(this.message,t)}isSet(t){return It(this.message,t),Zu(this.message,t)}clear(t){It(this.message,t),function(n,r){const s=r.localName;if(r.oneof){const a=r.oneof.localName;n[a].case===s&&(n[a]={case:void 0})}else if(r.presence!=2)delete n[s];else switch(r.fieldKind){case"map":n[s]={};break;case"list":n[s]=[];break;case"enum":n[s]=r.enum.values[0].number;break;case"scalar":n[s]=Vt(r.scalar,r.longAsString)}}(this.message,t)}get(t){It(this.message,t);const n=no(this.message,t);switch(t.fieldKind){case"list":let r=this.lists.get(t);return r&&r[De]===n||this.lists.set(t,r=new tl(t,n,this.check)),r;case"map":let s=this.maps.get(t);return s&&s[De]===n||this.maps.set(t,s=new nl(t,n,this.check)),s;case"message":return Bs(t,n,this.check);case"scalar":return n===void 0?Vt(t.scalar,!1):js(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(It(this.message,t),this.check){const s=function(a,i){const o=a.fieldKind=="list"?Fr(i,a):a.fieldKind=="map"?Ur(i,a):Us(a,i);if(o===!0)return;let l;switch(a.fieldKind){case"list":l=`expected ${go(a)}, got ${kn(i)}`;break;case"map":l=`expected ${yo(a)}, got ${kn(i)}`;break;default:l=Sn(a,i,o)}return new bt(a,l)}(t,n);if(s)throw s}let r;r=t.fieldKind=="message"?$s(t,n):Ur(n)||Fr(n)?n[De]:Gs(t,n),ro(this.message,t,r)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function It(e,t){if(t.parent.typeName!==e.$typeName)throw new bt(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class tl{field(){return this._field}get size(){return this._arr.length}constructor(t,n,r){this._field=t,this._arr=this[De]=n,this.check=r}get(t){const n=this._arr[t];return n===void 0?void 0:or(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new bt(this._field,`list item #${t+1}: out of range`);if(this.check){const r=ga(this._field,t,n);if(r)throw r}this._arr[t]=ya(this._field,n)}add(t){if(this.check){const n=ga(this._field,this._arr.length,t);if(n)throw n}this._arr.push(ya(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield or(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,or(this._field,this._arr[t],this.check)]}}class nl{constructor(t,n,r=!0){this.obj=this[De]=n??{},this.check=r,this._field=t}field(){return this._field}set(t,n){if(this.check){const r=function(s,a,i){const o=mo(a,s.mapKey);if(o!==!0)return new bt(s,`invalid map key: ${Sn({scalar:s.mapKey},a,o)}`);const l=Us(s,i);return l!==!0?new bt(s,`map entry ${kn(a)}: ${Sn(s,i,l)}`):void 0}(this._field,t,n);if(r)throw r}return this.obj[Qt(t)]=function(r,s){return r.mapKind=="message"?$s(r,s):Gs(r,s)}(this._field,n),this}delete(t){const n=Qt(t),r=Object.prototype.hasOwnProperty.call(this.obj,n);return r&&delete this.obj[n],r}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[Qt(t)];return n!==void 0&&(n=ur(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,Qt(t))}*keys(){for(const t of Object.keys(this.obj))yield va(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[va(t[0],this._field.mapKey),ur(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield ur(this._field,t,this.check)}forEach(t,n){for(const r of this.entries())t.call(n,r[1],r[0],this)}}function $s(e,t){return Ms(t)?ao(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?bo(t.message):t.message:t}function Bs(e,t,n){return t!==void 0&&(so(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:js(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&Xe(t)&&(t=_o(t))),new vo(e.message,t,n)}function ya(e,t){return e.listKind=="message"?$s(e,t):Gs(e,t)}function or(e,t,n){return e.listKind=="message"?Bs(e,t,n):js(e,t)}function ur(e,t,n){return e.mapKind=="message"?Bs(e,t,n):t}function Qt(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function va(e,t){switch(t){case v.STRING:return e;case v.INT32:case v.FIXED32:case v.UINT32:case v.SFIXED32:case v.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case v.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case v.UINT64:case v.FIXED64:try{return B.uParse(e)}catch{}break;default:try{return B.parse(e)}catch{}}return e}function js(e,t){switch(e.scalar){case v.INT64:case v.SFIXED64:case v.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=B.parse(t));break;case v.FIXED64:case v.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=B.uParse(t))}return t}function Gs(e,t){switch(e.scalar){case v.INT64:case v.SFIXED64:case v.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=B.parse(t));break;case v.FIXED64:case v.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=B.uParse(t))}return t}function _o(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(Xe(e))for(const[n,r]of Object.entries(e))t.fields[n]=So(r);return t}function bo(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Eo(r);return t}function Eo(e){switch(e.kind.case){case"structValue":return bo(e.kind.value);case"listValue":return e.kind.value.values.map(Eo);case"nullValue":case void 0:return null;default:return e.kind.value}}function So(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const r of e)n.values.push(So(r));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:_o(e)}}return t}function rl(e){const t=function(){if(!pt){pt=[];const l=function(u){return en||(en="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),en.slice(0,-2).concat("-","_")),en}();for(let u=0;u<l.length;u++)pt[l[u].charCodeAt(0)]=u;pt[45]=l.indexOf("+"),pt[95]=l.indexOf("/")}return pt}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let r,s=new Uint8Array(n),a=0,i=0,o=0;for(let l=0;l<e.length;l++){if(r=t[e.charCodeAt(l)],r===void 0)switch(e[l]){case"=":i=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(i){case 0:o=r,i=1;break;case 1:s[a++]=o<<2|(48&r)>>4,o=r,i=2;break;case 2:s[a++]=(15&o)<<4|(60&r)>>2,o=r,i=3;break;case 3:s[a++]=(3&o)<<6|r,i=0}}if(i==1)throw Error("invalid base64 string");return s.subarray(0,a)}let en,pt;function Tn(e){let t=!1;const n=[];for(let r=0;r<e.length;r++){let s=e.charAt(r);switch(s){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(s),t=!1;break;default:t&&(t=!1,s=s.toUpperCase()),n.push(s)}}return n.join("")}const sl=new Set(["constructor","toString","toJSON","valueOf"]);function Yt(e){return sl.has(e)?e+"$":e}function qs(e){for(const t of e.field)Ft(t,"jsonName")||(t.jsonName=Tn(t.name));e.nestedType.forEach(qs)}function al(e,t){switch(e){case v.STRING:return t;case v.BYTES:{const n=function(r){const s=[],a={tail:r,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(i){if(this.tail.length>=i){const o=this.tail.substring(0,i);return this.tail=this.tail.substring(i),o}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":s.push(a.c.charCodeAt(0));break;case"b":s.push(8);break;case"f":s.push(12);break;case"n":s.push(10);break;case"r":s.push(13);break;case"t":s.push(9);break;case"v":s.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const i=a.c,o=a.take(2);if(o===!1)return!1;const l=parseInt(i+o,8);if(Number.isNaN(l))return!1;s.push(l);break}case"x":{const i=a.c,o=a.take(2);if(o===!1)return!1;const l=parseInt(i+o,16);if(Number.isNaN(l))return!1;s.push(l);break}case"u":{const i=a.c,o=a.take(4);if(o===!1)return!1;const l=parseInt(i+o,16);if(Number.isNaN(l))return!1;const u=new Uint8Array(4);new DataView(u.buffer).setInt32(0,l,!0),s.push(u[0],u[1],u[2],u[3]);break}case"U":{const i=a.c,o=a.take(8);if(o===!1)return!1;const l=B.uEnc(i+o),u=new Uint8Array(8),p=new DataView(u.buffer);p.setInt32(0,l.lo,!0),p.setInt32(4,l.hi,!0),s.push(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7]);break}}}else s.push(a.c.charCodeAt(0));return new Uint8Array(s)}(t);if(n===!1)throw new Error(`cannot parse ${v[e]} default value: ${t}`);return n}case v.INT64:case v.SFIXED64:case v.SINT64:return B.parse(t);case v.UINT64:case v.FIXED64:return B.uParse(t);case v.DOUBLE:case v.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case v.BOOL:return t==="true";case v.INT32:case v.UINT32:case v.SINT32:case v.FIXED32:case v.SFIXED32:return parseInt(t,10)}}function*$r(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*$r(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*$r(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function ko(...e){const t=function(){const n=new Map,r=new Map,s=new Map;return{kind:"registry",types:n,extendees:r,[Symbol.iterator]:()=>n.values(),get files(){return s.values()},addFile(a,i,o){if(s.set(a.proto.name,a),!i)for(const l of $r(a))this.add(l);if(o)for(const l of a.dependencies)this.addFile(l,i,o)},add(a){if(a.kind=="extension"){let i=r.get(a.extendee.typeName);i||r.set(a.extendee.typeName,i=new Map),i.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>s.get(a),getMessage(a){const i=n.get(a);return(i==null?void 0:i.kind)=="message"?i:void 0},getEnum(a){const i=n.get(a);return(i==null?void 0:i.kind)=="enum"?i:void 0},getExtension(a){const i=n.get(a);return(i==null?void 0:i.kind)=="extension"?i:void 0},getExtensionFor(a,i){var o;return(o=r.get(a.typeName))===null||o===void 0?void 0:o.get(i)},getService(a){const i=n.get(a);return(i==null?void 0:i.kind)=="service"?i:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)Sa(n,t);return t}if("$typeName"in e[0]){let a=function(i){const o=[];for(const l of i.dependency){if(t.getFile(l)!=null||s.has(l))continue;const u=r(l);if(!u)throw new Error(`Unable to resolve ${l}, imported by ${i.name}`);"kind"in u?t.addFile(u,!1,!0):(s.add(u.name),o.push(u))}return o.concat(...o.map(a))};const n=e[0],r=e[1],s=new Set;for(const i of[n,...a(n)].reverse())Sa(i,t)}else for(const n of e)for(const r of n.files)t.addFile(r);return t}const il=998,ol=999,ul=9,Ut=10,At=11,ll=12,_a=14,Br=3,cl=2,ba=1,dl=0,lr=1,Ea=2,pl=3,hl=1,ml=2,fl=1,To={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2,defaultSymbolVisibility:1},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1}};function Sa(e,t){var n,r;const s={kind:"file",proto:e,deprecated:(r=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&r!==void 0&&r,edition:vl(e),name:e.name.replace(/\.proto$/,""),dependencies:_l(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,i={get:o=>a.get(o),add(o){var l;me(((l=o.proto.options)===null||l===void 0?void 0:l.mapEntry)===!0),a.set(o.typeName,o)}};for(const o of e.enumType)wo(o,s,void 0,t);for(const o of e.messageType)Io(o,s,void 0,t,i);for(const o of e.service)gl(o,s,t);jr(s,t);for(const o of a.values())Gr(o,t,i);for(const o of s.messages)Gr(o,t,i),jr(o,t);t.addFile(s,!0)}function jr(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const r=qr(n,e,t);e.extensions.push(r),t.add(r)}break;case"message":for(const n of e.proto.extension){const r=qr(n,e,t);e.nestedExtensions.push(r),t.add(r)}for(const n of e.nestedMessages)jr(n,t)}}function Gr(e,t,n){const r=e.proto.oneofDecl.map(a=>function(i,o){return{kind:"oneof",proto:i,deprecated:!1,parent:o,fields:[],name:i.name,localName:Yt(Tn(i.name)),toString(){return`oneof ${o.typeName}.${this.name}`}}}(a,e)),s=new Set;for(const a of e.proto.field){const i=bl(a,r),o=qr(a,e,t,i,n);e.fields.push(o),e.field[o.localName]=o,i===void 0?e.members.push(o):(i.fields.push(o),s.has(i)||(s.add(i),e.members.push(i)))}for(const a of r.filter(i=>s.has(i)))e.oneofs.push(a);for(const a of e.nestedMessages)Gr(a,t,n)}function wo(e,t,n,r){var s,a,i,o,l;const u=function(c,d){const h=(m=c,(m.substring(0,1)+m.substring(1).replace(/[A-Z]/g,g=>"_"+g)).toLowerCase()+"_");var m;for(const g of d){if(!g.name.toLowerCase().startsWith(h))return;const f=g.name.substring(h.length);if(f.length==0||/^\d/.test(f))return}return h}(e.name,e.value),p={kind:"enum",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:er(e,n,t),value:{},values:[],sharedPrefix:u,toString(){return`enum ${this.typeName}`}};p.open=function(c){var d;return fl==Et("enumType",{proto:c.proto,parent:(d=c.parent)!==null&&d!==void 0?d:c.file})}(p),r.add(p);for(const c of e.value){const d=c.name;p.values.push(p.value[c.number]={kind:"enum_value",proto:c,deprecated:(o=(i=c.options)===null||i===void 0?void 0:i.deprecated)!==null&&o!==void 0&&o,parent:p,name:d,localName:Yt(u==null?d:d.substring(u.length)),number:c.number,toString:()=>`enum value ${p.typeName}.${d}`})}((l=n==null?void 0:n.nestedEnums)!==null&&l!==void 0?l:t.enums).push(p)}function Io(e,t,n,r,s){var a,i,o,l;const u={kind:"message",proto:e,deprecated:(i=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&i!==void 0&&i,file:t,parent:n,name:e.name,typeName:er(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((o=e.options)===null||o===void 0?void 0:o.mapEntry)===!0?s.add(u):(((l=n==null?void 0:n.nestedMessages)!==null&&l!==void 0?l:t.messages).push(u),r.add(u));for(const p of e.enumType)wo(p,t,u,r);for(const p of e.nestedType)Io(p,t,u,r,s)}function gl(e,t,n){var r,s;const a={kind:"service",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,file:t,name:e.name,typeName:er(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const i of e.method){const o=yl(i,a,n);a.methods.push(o),a.method[o.localName]=o}}function yl(e,t,n){var r,s,a,i;let o;o=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const l=n.getMessage(Oe(e.inputType)),u=n.getMessage(Oe(e.outputType));me(l,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),me(u,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const p=e.name;return{kind:"rpc",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,parent:t,name:p,localName:Yt(p.length?Yt(p[0].toLowerCase()+p.substring(1)):p),methodKind:o,input:l,output:u,idempotency:(i=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&i!==void 0?i:dl,toString:()=>`rpc ${t.typeName}.${p}`}}function qr(e,t,n,r,s){var a,i,o;const l=s===void 0,u={kind:"field",proto:e,deprecated:(i=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&i!==void 0&&i,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:El(e,r,l,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(l){const h=t.kind=="file"?t:t.file,m=t.kind=="file"?void 0:t,g=er(e,m,h);u.kind="extension",u.file=h,u.parent=m,u.oneof=void 0,u.typeName=g,u.jsonName=`[${g}]`,u.toString=()=>`extension ${g}`;const f=n.getMessage(Oe(e.extendee));me(f,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),u.extendee=f}else{const h=t;me(h.kind=="message"),u.parent=h,u.oneof=r,u.localName=r?Tn(e.name):Yt(Tn(e.name)),u.jsonName=e.jsonName,u.toString=()=>`field ${h.typeName}.${e.name}`}const p=e.label,c=e.type,d=(o=e.options)===null||o===void 0?void 0:o.jstype;if(p===Br){const h=c==At?s==null?void 0:s.get(Oe(e.typeName)):void 0;if(h){u.fieldKind="map";const{key:m,value:g}=function(f){const _=f.fields.find(b=>b.number===1),S=f.fields.find(b=>b.number===2);return me(_&&_.fieldKind=="scalar"&&_.scalar!=v.BYTES&&_.scalar!=v.FLOAT&&_.scalar!=v.DOUBLE&&S&&S.fieldKind!="list"&&S.fieldKind!="map"),{key:_,value:S}}(h);return u.mapKey=m.scalar,u.mapKind=g.fieldKind,u.message=g.message,u.delimitedEncoding=!1,u.enum=g.enum,u.scalar=g.scalar,u}switch(u.fieldKind="list",c){case At:case Ut:u.listKind="message",u.message=n.getMessage(Oe(e.typeName)),me(u.message),u.delimitedEncoding=ka(e,t);break;case _a:u.listKind="enum",u.enum=n.getEnum(Oe(e.typeName)),me(u.enum);break;default:u.listKind="scalar",u.scalar=c,u.longAsString=d==ba}return u.packed=function(m,g){if(m.label!=Br)return!1;switch(m.type){case ul:case ll:case Ut:case At:return!1}const f=m.options;return f&&Ft(f,"packed")?f.packed:hl==Et("repeatedFieldEncoding",{proto:m,parent:g})}(e,t),u}switch(c){case At:case Ut:u.fieldKind="message",u.message=n.getMessage(Oe(e.typeName)),me(u.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.delimitedEncoding=ka(e,t),u.getDefaultValue=()=>{};break;case _a:{const h=n.getEnum(Oe(e.typeName));me(h!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.fieldKind="enum",u.enum=n.getEnum(Oe(e.typeName)),u.getDefaultValue=()=>Ft(e,"defaultValue")?function(m,g){const f=m.values.find(_=>_.name===g);if(!f)throw new Error(`cannot parse ${m} default value: ${g}`);return f.number}(h,e.defaultValue):void 0;break}default:u.fieldKind="scalar",u.scalar=c,u.longAsString=d==ba,u.getDefaultValue=()=>Ft(e,"defaultValue")?al(c,e.defaultValue):void 0}return u}function vl(e){switch(e.syntax){case"":case"proto2":return il;case"proto3":return ol;case"editions":if(e.edition in To)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function _l(e,t){return e.dependency.map(n=>{const r=t.getFile(n);if(!r)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return r})}function er(e,t,n){let r;return r=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,r}function Oe(e){return e.startsWith(".")?e.substring(1):e}function bl(e,t){if(!Ft(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return me(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function El(e,t,n,r){if(e.label==cl)return pl;if(e.label==Br)return Ea;if(t||e.proto3Optional||n)return lr;const s=Et("fieldPresence",{proto:e,parent:r});return s!=Ea||e.type!=At&&e.type!=Ut?s:lr}function ka(e,t){return e.type==Ut||ml==Et("messageEncoding",{proto:e,parent:t})}function Et(e,t){var n,r;const s=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(s){const a=s[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return Et(e,(r=t.parent)!==null&&r!==void 0?r:t.file);const a=To[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return Et(e,t.parent)}function me(e,t){if(!e)throw new Error(t)}function Sl(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],optionDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(No),enumType:n.enumType.map(Co)}))}(e);return t.messageType.forEach(qs),ko(t,()=>{}).getFile(t.name)}function No(e){var t,n,r,s,a,i,o,l;return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(kl))!==null&&n!==void 0?n:[],extension:[],nestedType:(s=(r=e.nestedType)===null||r===void 0?void 0:r.map(No))!==null&&s!==void 0?s:[],enumType:(i=(a=e.enumType)===null||a===void 0?void 0:a.map(Co))!==null&&i!==void 0?i:[],extensionRange:(l=(o=e.extensionRange)===null||o===void 0?void 0:o.map(p=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},p)))!==null&&l!==void 0?l:[],oneofDecl:[],reservedRange:[],reservedName:[]})}function kl(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?Tl(e.options):void 0}))}function Tl(e){var t,n,r;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(r=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&r!==void 0?r:[],uninterpretedOption:[]}))}function Co(e){return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(n=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},n))})}function Vs(e,t,...n){return n.reduce((r,s)=>r.nestedMessages[s],e.messages[t])}const wl=Vs(Sl({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"option_dependency",number:15,type:9,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3},{name:"visibility",number:11,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3},{name:"visibility",number:6,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}},{name:"default_symbol_visibility",number:8,type:14,label:1,typeName:".google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility",options:{retention:2,targets:[1],editionDefaults:[{value:"EXPORT_ALL",edition:900},{value:"EXPORT_TOP_LEVEL",edition:1001}]}}],nestedType:[{name:"VisibilityFeature",enumType:[{name:"DefaultSymbolVisibility",value:[{name:"DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",number:0},{name:"EXPORT_ALL",number:1},{name:"EXPORT_TOP_LEVEL",number:2},{name:"LOCAL_ALL",number:3},{name:"STRICT",number:4}]}]}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]},{name:"SymbolVisibility",value:[{name:"VISIBILITY_UNSET",number:0},{name:"VISIBILITY_LOCAL",number:1},{name:"VISIBILITY_EXPORT",number:2}]}]}),1);var Ta,wa,Ia,Na,Ca,xa,Ra,Aa,Oa,Pa,Ma,La,Da,Fa,Ua,$a,Ba,ja,Ga,qa;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(Ta||(Ta={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(wa||(wa={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(Ia||(Ia={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(Na||(Na={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(Ca||(Ca={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(xa||(xa={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(Ra||(Ra={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Aa||(Aa={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(Oa||(Oa={})),function(e){e[e.DEFAULT_SYMBOL_VISIBILITY_UNKNOWN=0]="DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",e[e.EXPORT_ALL=1]="EXPORT_ALL",e[e.EXPORT_TOP_LEVEL=2]="EXPORT_TOP_LEVEL",e[e.LOCAL_ALL=3]="LOCAL_ALL",e[e.STRICT=4]="STRICT"}(Pa||(Pa={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(Ma||(Ma={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(La||(La={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(Da||(Da={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(Fa||(Fa={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(Ua||(Ua={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}($a||($a={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(Ba||(Ba={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(ja||(ja={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(Ga||(Ga={})),function(e){e[e.VISIBILITY_UNSET=0]="VISIBILITY_UNSET",e[e.VISIBILITY_LOCAL=1]="VISIBILITY_LOCAL",e[e.VISIBILITY_EXPORT=2]="VISIBILITY_EXPORT"}(qa||(qa={}));const Va={readUnknownFields:!0};function tr(e,t,n){const r=Qn(e,void 0,!1);return xo(r,new el(t),function(s){return s?Object.assign(Object.assign({},Va),s):Va}(n),!1,t.byteLength),r.message}function xo(e,t,n,r,s){var a;const i=r?t.len:t.pos+s;let o,l;const u=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<i&&([o,l]=t.tag(),!r||l!=G.EndGroup);){const p=e.findNumber(o);if(p)Il(e,t,p,l,n);else{const c=t.skip(l,o);n.readUnknownFields&&u.push({no:o,wireType:l,data:c})}}if(r&&(l!=G.EndGroup||o!==s))throw new Error("invalid end group tag");u.length>0&&e.setUnknown(u)}function Il(e,t,n,r,s){var a;switch(n.fieldKind){case"scalar":e.set(n,ht(t,n.scalar));break;case"enum":const i=ht(t,v.INT32);if(n.enum.open)e.set(n,i);else if(n.enum.values.some(o=>o.number===i))e.set(n,i);else if(s.readUnknownFields){const o=new ho().int32(i).finish(),l=(a=e.getUnknown())!==null&&a!==void 0?a:[];l.push({no:n.number,wireType:r,data:o}),e.setUnknown(l)}break;case"message":e.set(n,cr(t,s,n,e.get(n)));break;case"list":(function(o,l,u,p){var c;const d=u.field();if(d.listKind==="message")return void u.add(cr(o,p,d));const h=(c=d.scalar)!==null&&c!==void 0?c:v.INT32;if(!(l==G.LengthDelimited&&h!=v.STRING&&h!=v.BYTES))return void u.add(ht(o,h));const g=o.uint32()+o.pos;for(;o.pos<g;)u.add(ht(o,h))})(t,r,e.get(n),s);break;case"map":(function(o,l,u){const p=l.field();let c,d;const h=o.pos+o.uint32();for(;o.pos<h;){const[m]=o.tag();switch(m){case 1:c=ht(o,p.mapKey);break;case 2:switch(p.mapKind){case"scalar":d=ht(o,p.scalar);break;case"enum":d=o.int32();break;case"message":d=cr(o,u,p)}}}if(c===void 0&&(c=Vt(p.mapKey,!1)),d===void 0)switch(p.mapKind){case"scalar":d=Vt(p.scalar,!1);break;case"enum":d=p.enum.values[0].number;break;case"message":d=Qn(p.message,void 0,!1)}l.set(c,d)})(t,e.get(n),s)}}function cr(e,t,n,r){const s=n.delimitedEncoding,a=r??Qn(n.message,void 0,!1);return xo(a,e,t,s,s?n.number:e.uint32()),a}function ht(e,t){switch(t){case v.STRING:return e.string();case v.BOOL:return e.bool();case v.DOUBLE:return e.double();case v.FLOAT:return e.float();case v.INT32:return e.int32();case v.INT64:return e.int64();case v.UINT64:return e.uint64();case v.FIXED64:return e.fixed64();case v.BYTES:return e.bytes();case v.FIXED32:return e.fixed32();case v.SFIXED32:return e.sfixed32();case v.SFIXED64:return e.sfixed64();case v.SINT64:return e.sint64();case v.UINT32:return e.uint32();case v.SINT32:return e.sint32()}}function $t(e,t){var n;const r=tr(wl,rl(e));return r.messageType.forEach(qs),r.dependency=(n=t==null?void 0:t.map(a=>a.proto.name))!==null&&n!==void 0?n:[],ko(r,a=>t==null?void 0:t.find(i=>i.proto.name===a)).getFile(r.name)}const Vr=$t("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),Nl=Vs(Vr,0),Cl=3,Ya={writeUnknownFields:!0};function mt(e,t,n){return wn(new ho,function(r){return r?Object.assign(Object.assign({},Ya),r):Ya}(n),Qn(e,t)).finish()}function wn(e,t,n){var r;for(const s of n.sortedFields)if(n.isSet(s))xl(e,t,n,s);else if(s.presence==Cl)throw new Error(`cannot encode ${s} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:s,wireType:a,data:i}of(r=n.getUnknown())!==null&&r!==void 0?r:[])e.tag(s,a).raw(i);return e}function xl(e,t,n,r){var s;switch(r.fieldKind){case"scalar":case"enum":In(e,n.desc.typeName,r.name,(s=r.scalar)!==null&&s!==void 0?s:v.INT32,r.number,n.get(r));break;case"list":(function(a,i,o,l){var u;if(o.listKind=="message"){for(const c of l)Ka(a,i,o,c);return}const p=(u=o.scalar)!==null&&u!==void 0?u:v.INT32;if(o.packed){if(!l.size)return;a.tag(o.number,G.LengthDelimited).fork();for(const c of l)Ro(a,o.parent.typeName,o.name,p,c);return void a.join()}for(const c of l)In(a,o.parent.typeName,o.name,p,o.number,c)})(e,t,r,n.get(r));break;case"message":Ka(e,t,r,n.get(r));break;case"map":for(const[a,i]of n.get(r))Rl(e,t,r,a,i)}}function In(e,t,n,r,s,a){Ro(e.tag(s,function(i){switch(i){case v.BYTES:case v.STRING:return G.LengthDelimited;case v.DOUBLE:case v.FIXED64:case v.SFIXED64:return G.Bit64;case v.FIXED32:case v.SFIXED32:case v.FLOAT:return G.Bit32;default:return G.Varint}}(r)),t,n,r,a)}function Ka(e,t,n,r){n.delimitedEncoding?wn(e.tag(n.number,G.StartGroup),t,r).tag(n.number,G.EndGroup):wn(e.tag(n.number,G.LengthDelimited).fork(),t,r).join()}function Rl(e,t,n,r,s){var a;switch(e.tag(n.number,G.LengthDelimited).fork(),In(e,n.parent.typeName,n.name,n.mapKey,1,r),n.mapKind){case"scalar":case"enum":In(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:v.INT32,2,s);break;case"message":wn(e.tag(2,G.LengthDelimited).fork(),t,s).join()}e.join()}function Ro(e,t,n,r,s){try{switch(r){case v.STRING:e.string(s);break;case v.BOOL:e.bool(s);break;case v.DOUBLE:e.double(s);break;case v.FLOAT:e.float(s);break;case v.INT32:e.int32(s);break;case v.INT64:e.int64(s);break;case v.UINT64:e.uint64(s);break;case v.FIXED64:e.fixed64(s);break;case v.BYTES:e.bytes(s);break;case v.FIXED32:e.fixed32(s);break;case v.SFIXED32:e.sfixed32(s);break;case v.SFIXED64:e.sfixed64(s);break;case v.SINT64:e.sint64(s);break;case v.UINT32:e.uint32(s);break;case v.SINT32:e.sint32(s)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}function Ha(e,t,n){let r=!1;return n||(n=Se(Nl),r=!0),n.value=mt(e,t),n.typeUrl=`type.googleapis.com/${t.$typeName}`,r?n:void 0}function Za(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(Wa(e.typeUrl));return n&&function(r,s){return r.typeUrl!==""&&(typeof s=="string"?s:s.typeName)===Wa(r.typeUrl)}(e,n)?tr(n,e.value):void 0}function Wa(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}function Al(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}const ve=Vs($t("CjpjbGllbnRzL3NpZGVjYXIvbGlicy90cmFuc3BvcnQvZ3JwYy9wcm90b3MvdHJhbnNwb3J0LnByb3RvEhFhdWdtZW50LnRyYW5zcG9ydCIrCg1NZXRhZGF0YUVudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoDCLVAgoLUnBjRW52ZWxvcGUSEgoKcmVxdWVzdF9pZBgBIAEoCRIUCgxzZXJ2aWNlX25hbWUYAiABKAkSEwoLbWV0aG9kX25hbWUYAyABKAkSJwoHcmVxdWVzdBgEIAEoCzIULmdvb2dsZS5wcm90b2J1Zi5BbnlIABIoCghyZXNwb25zZRgFIAEoCzIULmdvb2dsZS5wcm90b2J1Zi5BbnlIABIjCgVlcnJvchgGIAEoCzISLmdvb2dsZS5ycGMuU3RhdHVzSAASKgoHdGltZW91dBgHIAEoCzIZLmdvb2dsZS5wcm90b2J1Zi5EdXJhdGlvbhIyCghtZXRhZGF0YRgIIAMoCzIgLmF1Z21lbnQudHJhbnNwb3J0Lk1ldGFkYXRhRW50cnkSGAoQcHJvdG9jb2xfdmVyc2lvbhgJIAEoCUIJCgdjb250ZW50SgQIChALSgQICxAMYgZwcm90bzM",[Vr,$t("Ch5nb29nbGUvcHJvdG9idWYvZHVyYXRpb24ucHJvdG8SD2dvb2dsZS5wcm90b2J1ZiIqCghEdXJhdGlvbhIPCgdzZWNvbmRzGAEgASgDEg0KBW5hbm9zGAIgASgFQoMBChNjb20uZ29vZ2xlLnByb3RvYnVmQg1EdXJhdGlvblByb3RvUAFaMWdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2R1cmF0aW9ucGL4AQGiAgNHUEKqAh5Hb29nbGUuUHJvdG9idWYuV2VsbEtub3duVHlwZXNiBnByb3RvMw"),$t("Chdnb29nbGUvcnBjL3N0YXR1cy5wcm90bxIKZ29vZ2xlLnJwYyJOCgZTdGF0dXMSDAoEY29kZRgBIAEoBRIPCgdtZXNzYWdlGAIgASgJEiUKB2RldGFpbHMYAyADKAsyFC5nb29nbGUucHJvdG9idWYuQW55QmEKDmNvbS5nb29nbGUucnBjQgtTdGF0dXNQcm90b1ABWjdnb29nbGUuZ29sYW5nLm9yZy9nZW5wcm90by9nb29nbGVhcGlzL3JwYy9zdGF0dXM7c3RhdHVz+AEBogIDUlBDYgZwcm90bzM",[Vr])]),1);function Nt(e){if(typeof Buffer<"u")return Buffer.from(e).toString("base64");let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return btoa(t)}class Ol{constructor(t){y(this,"queue",[]);y(this,"waiters",[]);y(this,"ended",!1);y(this,"error");y(this,"abortSignal");y(this,"abortListener");this.abortSignal=t,t&&(this.abortListener=()=>{var n;this.fail(new Error(String(((n=this.abortSignal)==null?void 0:n.reason)||"Stream aborted")))},t.addEventListener("abort",this.abortListener,{once:!0}))}push(t){var n;!((n=this.abortSignal)!=null&&n.aborted)&&!this.ended&&(this.waiters.length>0?this.waiters.shift().resolve({value:t,done:!1}):this.queue.push(t))}end(){if(!this.ended)for(this.ended=!0,this.cleanup();this.waiters.length>0;)this.waiters.shift().resolve({value:void 0,done:!0})}fail(t){if(!this.ended)for(this.error=t,this.ended=!0,this.cleanup();this.waiters.length>0;)this.waiters.shift().reject(t)}cleanup(){this.abortListener&&this.abortSignal&&(this.abortSignal.removeEventListener("abort",this.abortListener),this.abortListener=void 0)}async*iterable(){var t;try{for(;;){if((t=this.abortSignal)!=null&&t.aborted)throw new Error("Stream aborted");if(this.queue.length>0){yield this.queue.shift();continue}if(this.ended){if(this.error)throw this.error;return}const n=await new Promise((r,s)=>{this.waiters.push({resolve:r,reject:s})});if(n.done){if(this.error)throw this.error;return}yield n.value}}finally{this.cleanup()}}dispose(){this.end(),this.queue.length=0,this.waiters.length=0}}const Pe=class Pe{constructor(t){y(this,"target");y(this,"pendingRequests",new Map);y(this,"pendingStreams",new Map);y(this,"cleanup");y(this,"serviceRegistries",new Set);y(this,"serviceIndex",new Map);y(this,"registryListenerMap",new WeakMap);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t);for(const r of t.listServiceNames())this.serviceIndex.set(r,t);const n=r=>{this.serviceIndex.set(r,t)};t.addRegistrationListener(n),this.registryListenerMap.set(t,n)}removeServiceRegistry(t){this.serviceRegistries.delete(t);const n=this.registryListenerMap.get(t);n&&(t.removeRegistrationListener(n),this.registryListenerMap.delete(t));for(const[r,s]of this.serviceIndex)s===t&&this.serviceIndex.delete(r)}handleMessage(t){var r;if(!t||typeof t!="object"||!this.isRpcFrame(t))return;const n=t;try{const s=function(i){if(typeof Buffer<"u")return new Uint8Array(Buffer.from(i,"base64"));const o=atob(i),l=new Uint8Array(o.length);for(let u=0;u<o.length;u++)l[u]=o.charCodeAt(u);return l}(n.b64),a=tr(ve,s);switch((r=a.content)==null?void 0:r.case){case"request":this.handleRequest(a);break;case"response":case"error":this.handleResponse(a)}}catch{}}isRpcFrame(t){return"protocol"in t&&t.protocol===Pe.PROTOCOL_NAME&&"b64"in t&&typeof t.b64=="string"}async handleRequest(t){const n=t.serviceName,r=this.serviceIndex.get(n);if(r)try{await r.handleRequest(t,s=>{const a=mt(ve,s),i={protocol:Pe.PROTOCOL_NAME,b64:Nt(a)};this.target.sendMessage(i)})}catch(s){const a=s instanceof Error?s.message:String(s),i={requestId:t.requestId,serviceName:t.serviceName,methodName:t.methodName,content:{case:"error",value:{code:13,message:a}}},o=Se(ve,i),l=mt(ve,o),u={protocol:Pe.PROTOCOL_NAME,b64:Nt(l)};this.target.sendMessage(u)}else{const s={requestId:t.requestId,serviceName:t.serviceName,methodName:t.methodName,content:{case:"error",value:{code:12,message:`No handlers registered for service: ${t.serviceName}`}}},a=Se(ve,s),i=mt(ve,a),o={protocol:Pe.PROTOCOL_NAME,b64:Nt(i)};this.target.sendMessage(o)}}handleResponse(t){var s,a,i;const n=this.pendingStreams.get(t.requestId);if(n){if(((s=t.content)==null?void 0:s.case)==="response"){try{const o=Za(t.content.value,n.outputDesc);o&&n.push(o)}catch(o){n.fail(o),clearTimeout(n.timeout),n.queue&&n.queue.dispose(),this.pendingStreams.delete(t.requestId)}return}if(((a=t.content)==null?void 0:a.case)==="error"){const o=t.content.value;return((o==null?void 0:o.code)??2)===0?n.end():n.fail(new Error((o==null?void 0:o.message)??"Stream error")),clearTimeout(n.timeout),n.queue&&n.queue.dispose(),void this.pendingStreams.delete(t.requestId)}return}const r=this.pendingRequests.get(t.requestId);if(r)if(this.pendingRequests.delete(t.requestId),clearTimeout(r.timeout),((i=t.content)==null?void 0:i.case)!=="error")try{r.resolve(t)}catch(o){const l=o instanceof Error?o.message:String(o);r.reject(new Error(`Failed to process gRPC response: ${l}`))}else{const o=t.content.value;r.reject(new Error((o==null?void 0:o.message)??"Unknown error"))}}sendRequest(t,n){return new Promise((r,s)=>{let a;const i=t.requestId;n&&(a=setTimeout(()=>{this.pendingRequests.delete(i),s(new Error(`gRPC request timed out after ${n}ms: ${t.serviceName}.${t.methodName} (ID: ${i}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(i,{resolve:r,reject:s,timeout:a});const o=mt(ve,t),l={protocol:Pe.PROTOCOL_NAME,b64:Nt(o)};this.target.sendMessage(l)})}async unary(t,n,r,s,a,i){var f;const o=crypto.randomUUID(),l=t.localName,u=t.parent.typeName;if(!u)throw new Error("Service name is required for unary calls");const p=Se(t.input,a??{}),c=Ha(t.input,p);if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${u}.${l} (ID: ${o})`);let d;n&&(d=()=>{const _=this.pendingRequests.get(o);_&&(this.pendingRequests.delete(o),clearTimeout(_.timeout),_.reject(new Error(`gRPC request aborted during execution: ${u}.${l} (ID: ${o})`)))},n.addEventListener("abort",d,{once:!0}));const h=Se(ve,{requestId:o,serviceName:u,methodName:l,content:{case:"request",value:c}});let m;try{m=await this.sendRequest(h,r)}finally{n&&d&&n.removeEventListener("abort",d)}if(((f=m.content)==null?void 0:f.case)!=="response")throw new Error(`Invalid RPC response for ${u}.${l} (ID: ${o})`);const g=Za(m.content.value,t.output);if(!g)throw new Error(`Failed to unpack response for ${u}.${l} (ID: ${o})`);return{stream:!1,method:t,service:t.parent,header:new Headers(s),message:g,trailer:new Headers}}async stream(t,n,r,s,a,i){const o=crypto.randomUUID(),l=t.localName,u=t.parent.typeName;if(!u)throw new Error("Service name is required for streaming calls");let p;if(a){const b=a[Symbol.asyncIterator](),N=await b.next();if(N.done||(p=N.value),b.return)try{await b.return()}catch{}}const c=Se(t.input,p??{}),d=Ha(t.input,c),h=new Ol(n),m={push:b=>h.push(b),end:()=>h.end(),fail:b=>h.fail(b),outputDesc:t.output,timeout:void 0,queue:h};r&&(m.timeout=setTimeout(()=>{this.pendingStreams.has(o)&&(this.pendingStreams.delete(o),h.fail(new Error(`gRPC stream timed out after ${r}ms: ${u}.${l} (ID: ${o}). This may indicate that the server is not responding or the message routing is broken.`)))},r)),this.pendingStreams.set(o,m);const g=Se(ve,{requestId:o,serviceName:u,methodName:l,content:{case:"request",value:d}}),f=mt(ve,g),_={protocol:Pe.PROTOCOL_NAME,b64:Nt(f)};this.target.sendMessage(_);const S=(async function*(){try{for await(const b of h.iterable())yield b}finally{const b=this.pendingStreams.get(o);b&&(clearTimeout(b.timeout),b.queue&&b.queue.dispose(),this.pendingStreams.delete(o))}}).bind(this);return{stream:!0,method:t,service:t.parent,header:new Headers(s),message:S(),trailer:new Headers}}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear();for(const[t,n]of this.pendingStreams){clearTimeout(n.timeout);try{n.end(),n.queue&&n.queue.dispose()}catch{}this.pendingStreams.delete(t)}this.serviceRegistries.clear()}};y(Pe,"PROTOCOL_NAME","com.augmentcode.client.rpc");let Nn=Pe;var Ze;function za(e){const t=Ze[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(Ze||(Ze={}));class Ve extends Error{constructor(t,n=Ze.Unknown,r,s,a){super(function(i,o){return i.length?`[${za(o)}] ${i}`:`[${za(o)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(r??{}),this.details=s??[],this.cause=a}static from(t,n=Ze.Unknown){return t instanceof Ve?t:t instanceof Error?t.name=="AbortError"?new Ve(t.message,Ze.Canceled):new Ve(t.message,n,void 0,void 0,t):new Ve(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===Ve.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:s=>s===t.typeName?t:void 0}:t,r=[];for(const s of this.details){if("desc"in s){n.getMessage(s.desc.typeName)&&r.push(Se(s.desc,s.value));continue}const a=n.getMessage(s.type);if(a)try{r.push(tr(a,s.value))}catch{}}return r}}var Pl=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(s){t[s]=e[s]&&function(a){return new Promise(function(i,o){(function(l,u,p,c){Promise.resolve(c).then(function(d){l({value:d,done:p})},u)})(i,o,(a=e[s](a)).done,a.value)})}}},Kt=function(e){return this instanceof Kt?(this.v=e,this):new Kt(e)},Ml=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,s=n.apply(e,t||[]),a=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(c){return function(d){return Promise.resolve(d).then(c,u)}}),r[Symbol.asyncIterator]=function(){return this},r;function i(c,d){s[c]&&(r[c]=function(h){return new Promise(function(m,g){a.push([c,h,m,g])>1||o(c,h)})},d&&(r[c]=d(r[c])))}function o(c,d){try{(h=s[c](d)).value instanceof Kt?Promise.resolve(h.value.v).then(l,u):p(a[0][2],h)}catch(m){p(a[0][3],m)}var h}function l(c){o("next",c)}function u(c){o("throw",c)}function p(c,d){c(d),a.shift(),a.length&&o(a[0][0],a[0][1])}},Ll=function(e){var t,n;return t={},r("next"),r("throw",function(s){throw s}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(s,a){t[s]=e[s]?function(i){return(n=!n)?{value:Kt(e[s](i)),done:!1}:a?a(i):i}:a}},Ao=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(s){t[s]=e[s]&&function(a){return new Promise(function(i,o){(function(l,u,p,c){Promise.resolve(c).then(function(d){l({value:d,done:p})},u)})(i,o,(a=e[s](a)).done,a.value)})}}},St=function(e){return this instanceof St?(this.v=e,this):new St(e)},Dl=function(e){var t,n;return t={},r("next"),r("throw",function(s){throw s}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(s,a){t[s]=e[s]?function(i){return(n=!n)?{value:St(e[s](i)),done:!1}:a?a(i):i}:a}},Fl=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,s=n.apply(e,t||[]),a=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(c){return function(d){return Promise.resolve(d).then(c,u)}}),r[Symbol.asyncIterator]=function(){return this},r;function i(c,d){s[c]&&(r[c]=function(h){return new Promise(function(m,g){a.push([c,h,m,g])>1||o(c,h)})},d&&(r[c]=d(r[c])))}function o(c,d){try{(h=s[c](d)).value instanceof St?Promise.resolve(h.value.v).then(l,u):p(a[0][2],h)}catch(m){p(a[0][3],m)}var h}function l(c){o("next",c)}function u(c){o("throw",c)}function p(c,d){c(d),a.shift(),a.length&&o(a[0][0],a[0][1])}};function Xa(e,t){return function(n,r){const s={};for(const a of n.methods){const i=r(a);i!=null&&(s[a.localName]=i)}return s}(e,n=>{switch(n.methodKind){case"unary":return function(r,s){return async function(a,i){var o,l;const u=await r.unary(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues);return(o=i==null?void 0:i.onHeader)===null||o===void 0||o.call(i,u.header),(l=i==null?void 0:i.onTrailer)===null||l===void 0||l.call(i,u.trailer),u.message}}(t,n);case"server_streaming":return function(r,s){return function(a,i){return Ja(r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(o){return Ml(this,arguments,function*(){yield Kt(yield*Ll(Pl(o)))})}([a]),i==null?void 0:i.contextValues),i)}}(t,n);case"client_streaming":return function(r,s){return async function(a,i){var o,l,u,p,c,d;const h=await r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues);let m;(c=i==null?void 0:i.onHeader)===null||c===void 0||c.call(i,h.header);let g=0;try{for(var f,_=!0,S=Ao(h.message);!(o=(f=await S.next()).done);_=!0)p=f.value,_=!1,m=p,g++}catch(b){l={error:b}}finally{try{_||o||!(u=S.return)||await u.call(S)}finally{if(l)throw l.error}}if(!m)throw new Ve("protocol error: missing response message",Ze.Unimplemented);if(g>1)throw new Ve("protocol error: received extra messages for client streaming method",Ze.Unimplemented);return(d=i==null?void 0:i.onTrailer)===null||d===void 0||d.call(i,h.trailer),m}}(t,n);case"bidi_streaming":return function(r,s){return function(a,i){return Ja(r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues),i)}}(t,n);default:return null}})}function Ja(e,t){const n=function(){return Fl(this,arguments,function*(){var r,s;const a=yield St(e);(r=t==null?void 0:t.onHeader)===null||r===void 0||r.call(t,a.header),yield St(yield*Dl(Ao(a.message))),(s=t==null?void 0:t.onTrailer)===null||s===void 0||s.call(t,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}async function Qa(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}for(var dr=256,Ul=[];dr--;)Ul[dr]=(dr+256).toString(16).substring(1);function $l(e){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}const Bl={EXPERIMENT_VIEWED:"experiment_viewed",THREAD_CREATION_ATTEMPTED:"thread_creation_attempted",SEND_ACTION_TRIGGERED:"send_action_triggered",CANCEL_ACTION_TRIGGERED:"cancel_action_triggered",RESEND_ACTION_TRIGGERED:"resend_action_triggered",AGENT_EXECUTION_MODE_TOGGLED:"agent_execution_mode_toggled",MESSAGE_SEND_ERROR_DISPLAYED:"message_send_error_displayed",MESSAGE_SEND_RETRY_CLICKED:"message_send_retry_clicked",MESSAGE_SEND_TIMING:"message_sent_timing",VSCODE_EXTENSION_STARTUP:"vscode_extension_started_up",NOTIFICATION_DISPLAYED:"notification_displayed",NOTIFICATION_DISMISSED:"notification_dismissed",TOOL_CONNECT_BUTTON_CLICKED:"tool_connect_button_clicked",TOOL_CONNECTED:"tool_connected",TURN_SUMMARY_BUTTON_CLICKED:"turn_summary_button_clicked",SUBSCRIPTION_WARNING_UPGRADE_CLICKED:"subscription_warning_upgrade_clicked",BULK_DELETE_BANNER_SHOWN:"bulk_delete_banner_shown",BULK_DELETE_BANNER_CLICKED:"bulk_delete_banner_clicked",BULK_DELETE_CONFIRMED:"bulk_delete_confirmed",BULK_DELETE_BANNER_DISMISSED:"bulk_delete_banner_dismissed"};var Oo=(e=>(e.chat="chat",e))(Oo||{}),jl=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(jl||{});function ei(e){return e.replace(/^data:.*?;base64,/,"")}async function pr(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=s=>{var a;return t((a=s.target)==null?void 0:a.result)},r.onerror=n,r.readAsDataURL(e)})}async function hr(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,r=>r.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const r=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));r.onmessage=function(s){s.data.error?n(new Error(s.data.error)):t(s.data),r.terminate()},r.onerror=function(s){n(s.error),r.terminate()},r.postMessage(e)})}const ti=Al($t("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMrUBCgtUZXN0U2VydmljZRIzCgpUZXN0TWV0aG9kEhEudGVzdC5UZXN0UmVxdWVzdBoSLnRlc3QuVGVzdFJlc3BvbnNlEjQKC0Vycm9yTWV0aG9kEhEudGVzdC5UZXN0UmVxdWVzdBoSLnRlc3QuVGVzdFJlc3BvbnNlEjsKEFN0cmVhbUhlbGxvV29ybGQSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2UwAWIGcHJvdG8z"),0);var Y=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e.chatAgentEditListHasUpdates="chat-agent-edit-list-has-updates",e))(Y||{}),Yr=(e=>(e.trackAnalyticsEvent="track-analytics-event",e.trackExperimentViewedEvent="track-experiment-viewed-event",e))(Yr||{}),fn=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(fn||{}),gn=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(gn||{}),yn=(e=>(e.loadConversationHistoryRequest="loadConversationHistoryRequest",e.loadConversationHistoryResponse="loadConversationHistoryResponse",e.saveConversationHistoryRequest="saveConversationHistoryRequest",e.saveConversationHistoryResponse="saveConversationHistoryResponse",e.deleteConversationHistoryRequest="deleteConversationHistoryRequest",e.deleteConversationHistoryResponse="deleteConversationHistoryResponse",e.getConversationHistoryMetadataRequest="getConversationHistoryMetadataRequest",e.getConversationHistoryMetadataResponse="getConversationHistoryMetadataResponse",e))(yn||{});async function*Gl(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class ql{constructor(t,n,r,s=5,a=4e3,i){y(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=r,this.maxRetries=s,this.baseDelay=a,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,r=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,i,o=!1,l=!0;for await(const u of s){if(u.status===se.failed){if(u.isRetriable!==!0||r)return yield u;o=!0,l=u.shouldBackoff??!0,a=u.display_error_message,i=u.request_id;break}r=!0,yield u}if(!o)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${a}`),void(yield{request_id:i??this.requestId,seen_state:Te.unseen,status:se.failed,display_error_message:a,isRetriable:!1});if(l){const u=this.baseDelay*2**n;n++;for await(const p of Gl(u))yield{request_id:this.requestId,status:se.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(p/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:se.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){console.error("Unexpected error in chat stream:",s),yield{request_id:this.requestId,seen_state:Te.unseen,status:se.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:Te.unseen,status:se.cancelled}}}var ft=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(ft||{});class Vl{constructor(t){y(this,"getHydratedTask",async t=>{const n={type:ft.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});y(this,"createTask",async(t,n,r,s)=>{const a={type:ft.createTaskRequest,data:{name:t,description:n,parentTaskUuid:r,insertAfterUuid:s}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});y(this,"updateTask",async(t,n,r)=>{const s={type:ft.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:r}};await this._asyncMsgSender.sendToSidecar(s,3e4)});y(this,"setCurrentRootTaskUuid",t=>{const n={type:ft.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});y(this,"updateHydratedTask",async(t,n)=>{const r={type:ft.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});this._asyncMsgSender=t}}var ae=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(ae||{}),Ys=(e=>(e.USER="USER",e.AGENT="AGENT",e))(Ys||{}),Po={},Cn={},xn={};let tn;Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=function(){if(!tn&&(tn=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!tn))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return tn(Yl)};const Yl=new Uint8Array(16);var We={},st={},Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.default=void 0;Rn.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(st,"__esModule",{value:!0}),st.default=void 0;var nn,Kl=(nn=Rn)&&nn.__esModule?nn:{default:nn},Hl=function(e){return typeof e=="string"&&Kl.default.test(e)};st.default=Hl,Object.defineProperty(We,"__esModule",{value:!0}),We.default=void 0,We.unsafeStringify=Mo;var Zl=function(e){return e&&e.__esModule?e:{default:e}}(st);const K=[];for(let e=0;e<256;++e)K.push((e+256).toString(16).slice(1));function Mo(e,t=0){return K[e[t+0]]+K[e[t+1]]+K[e[t+2]]+K[e[t+3]]+"-"+K[e[t+4]]+K[e[t+5]]+"-"+K[e[t+6]]+K[e[t+7]]+"-"+K[e[t+8]]+K[e[t+9]]+"-"+K[e[t+10]]+K[e[t+11]]+K[e[t+12]]+K[e[t+13]]+K[e[t+14]]+K[e[t+15]]}var Wl=function(e,t=0){const n=Mo(e,t);if(!(0,Zl.default)(n))throw TypeError("Stringified UUID is invalid");return n};We.default=Wl,Object.defineProperty(Cn,"__esModule",{value:!0}),Cn.default=void 0;var zl=function(e){return e&&e.__esModule?e:{default:e}}(xn),Xl=We;let ni,mr,fr=0,gr=0;var Jl=function(e,t,n){let r=t&&n||0;const s=t||new Array(16);let a=(e=e||{}).node||ni,i=e.clockseq!==void 0?e.clockseq:mr;if(a==null||i==null){const d=e.random||(e.rng||zl.default)();a==null&&(a=ni=[1|d[0],d[1],d[2],d[3],d[4],d[5]]),i==null&&(i=mr=16383&(d[6]<<8|d[7]))}let o=e.msecs!==void 0?e.msecs:Date.now(),l=e.nsecs!==void 0?e.nsecs:gr+1;const u=o-fr+(l-gr)/1e4;if(u<0&&e.clockseq===void 0&&(i=i+1&16383),(u<0||o>fr)&&e.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");fr=o,gr=l,mr=i,o+=122192928e5;const p=(1e4*(268435455&o)+l)%4294967296;s[r++]=p>>>24&255,s[r++]=p>>>16&255,s[r++]=p>>>8&255,s[r++]=255&p;const c=o/4294967296*1e4&268435455;s[r++]=c>>>8&255,s[r++]=255&c,s[r++]=c>>>24&15|16,s[r++]=c>>>16&255,s[r++]=i>>>8|128,s[r++]=255&i;for(let d=0;d<6;++d)s[r+d]=a[d];return t||(0,Xl.unsafeStringify)(s)};Cn.default=Jl;var An={},Ke={},Ht={};Object.defineProperty(Ht,"__esModule",{value:!0}),Ht.default=void 0;var Ql=function(e){return e&&e.__esModule?e:{default:e}}(st),ec=function(e){if(!(0,Ql.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};Ht.default=ec,Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.URL=Ke.DNS=void 0,Ke.default=function(e,t,n){function r(s,a,i,o){var l;if(typeof s=="string"&&(s=function(p){p=unescape(encodeURIComponent(p));const c=[];for(let d=0;d<p.length;++d)c.push(p.charCodeAt(d));return c}(s)),typeof a=="string"&&(a=(0,nc.default)(a)),((l=a)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let u=new Uint8Array(16+s.length);if(u.set(a),u.set(s,a.length),u=n(u),u[6]=15&u[6]|t,u[8]=63&u[8]|128,i){o=o||0;for(let p=0;p<16;++p)i[o+p]=u[p];return i}return(0,tc.unsafeStringify)(u)}try{r.name=e}catch{}return r.DNS=Lo,r.URL=Do,r};var tc=We,nc=function(e){return e&&e.__esModule?e:{default:e}}(Ht);const Lo="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Ke.DNS=Lo;const Do="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Ke.URL=Do;var On={};function ri(e){return 14+(e+64>>>9<<4)+1}function He(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function nr(e,t,n,r,s,a){return He((i=He(He(t,e),He(r,a)))<<(o=s)|i>>>32-o,n);var i,o}function Z(e,t,n,r,s,a,i){return nr(t&n|~t&r,e,t,s,a,i)}function W(e,t,n,r,s,a,i){return nr(t&r|n&~r,e,t,s,a,i)}function z(e,t,n,r,s,a,i){return nr(t^n^r,e,t,s,a,i)}function X(e,t,n,r,s,a,i){return nr(n^(t|~r),e,t,s,a,i)}Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var rc=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],r=32*t.length,s="0123456789abcdef";for(let a=0;a<r;a+=8){const i=t[a>>5]>>>a%32&255,o=parseInt(s.charAt(i>>>4&15)+s.charAt(15&i),16);n.push(o)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[ri(n)-1]=n;let r=1732584193,s=-271733879,a=-1732584194,i=271733878;for(let o=0;o<t.length;o+=16){const l=r,u=s,p=a,c=i;r=Z(r,s,a,i,t[o],7,-680876936),i=Z(i,r,s,a,t[o+1],12,-389564586),a=Z(a,i,r,s,t[o+2],17,606105819),s=Z(s,a,i,r,t[o+3],22,-1044525330),r=Z(r,s,a,i,t[o+4],7,-176418897),i=Z(i,r,s,a,t[o+5],12,1200080426),a=Z(a,i,r,s,t[o+6],17,-1473231341),s=Z(s,a,i,r,t[o+7],22,-45705983),r=Z(r,s,a,i,t[o+8],7,1770035416),i=Z(i,r,s,a,t[o+9],12,-1958414417),a=Z(a,i,r,s,t[o+10],17,-42063),s=Z(s,a,i,r,t[o+11],22,-1990404162),r=Z(r,s,a,i,t[o+12],7,1804603682),i=Z(i,r,s,a,t[o+13],12,-40341101),a=Z(a,i,r,s,t[o+14],17,-1502002290),s=Z(s,a,i,r,t[o+15],22,1236535329),r=W(r,s,a,i,t[o+1],5,-165796510),i=W(i,r,s,a,t[o+6],9,-1069501632),a=W(a,i,r,s,t[o+11],14,643717713),s=W(s,a,i,r,t[o],20,-373897302),r=W(r,s,a,i,t[o+5],5,-701558691),i=W(i,r,s,a,t[o+10],9,38016083),a=W(a,i,r,s,t[o+15],14,-660478335),s=W(s,a,i,r,t[o+4],20,-405537848),r=W(r,s,a,i,t[o+9],5,568446438),i=W(i,r,s,a,t[o+14],9,-1019803690),a=W(a,i,r,s,t[o+3],14,-187363961),s=W(s,a,i,r,t[o+8],20,1163531501),r=W(r,s,a,i,t[o+13],5,-1444681467),i=W(i,r,s,a,t[o+2],9,-51403784),a=W(a,i,r,s,t[o+7],14,1735328473),s=W(s,a,i,r,t[o+12],20,-1926607734),r=z(r,s,a,i,t[o+5],4,-378558),i=z(i,r,s,a,t[o+8],11,-2022574463),a=z(a,i,r,s,t[o+11],16,1839030562),s=z(s,a,i,r,t[o+14],23,-35309556),r=z(r,s,a,i,t[o+1],4,-1530992060),i=z(i,r,s,a,t[o+4],11,1272893353),a=z(a,i,r,s,t[o+7],16,-155497632),s=z(s,a,i,r,t[o+10],23,-1094730640),r=z(r,s,a,i,t[o+13],4,681279174),i=z(i,r,s,a,t[o],11,-358537222),a=z(a,i,r,s,t[o+3],16,-722521979),s=z(s,a,i,r,t[o+6],23,76029189),r=z(r,s,a,i,t[o+9],4,-640364487),i=z(i,r,s,a,t[o+12],11,-421815835),a=z(a,i,r,s,t[o+15],16,530742520),s=z(s,a,i,r,t[o+2],23,-995338651),r=X(r,s,a,i,t[o],6,-198630844),i=X(i,r,s,a,t[o+7],10,1126891415),a=X(a,i,r,s,t[o+14],15,-1416354905),s=X(s,a,i,r,t[o+5],21,-57434055),r=X(r,s,a,i,t[o+12],6,1700485571),i=X(i,r,s,a,t[o+3],10,-1894986606),a=X(a,i,r,s,t[o+10],15,-1051523),s=X(s,a,i,r,t[o+1],21,-2054922799),r=X(r,s,a,i,t[o+8],6,1873313359),i=X(i,r,s,a,t[o+15],10,-30611744),a=X(a,i,r,s,t[o+6],15,-1560198380),s=X(s,a,i,r,t[o+13],21,1309151649),r=X(r,s,a,i,t[o+4],6,-145523070),i=X(i,r,s,a,t[o+11],10,-1120210379),a=X(a,i,r,s,t[o+2],15,718787259),s=X(s,a,i,r,t[o+9],21,-343485551),r=He(r,l),s=He(s,u),a=He(a,p),i=He(i,c)}return[r,s,a,i]}(function(t){if(t.length===0)return[];const n=8*t.length,r=new Uint32Array(ri(n));for(let s=0;s<n;s+=8)r[s>>5]|=(255&t[s/8])<<s%32;return r}(e),8*e.length))};On.default=rc,Object.defineProperty(An,"__esModule",{value:!0}),An.default=void 0;var sc=Fo(Ke),ac=Fo(On);function Fo(e){return e&&e.__esModule?e:{default:e}}var ic=(0,sc.default)("v3",48,ac.default);An.default=ic;var Pn={},Mn={};Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=void 0;var oc={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};Mn.default=oc,Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.default=void 0;var si=Uo(Mn),uc=Uo(xn),lc=We;function Uo(e){return e&&e.__esModule?e:{default:e}}var cc=function(e,t,n){if(si.default.randomUUID&&!t&&!e)return si.default.randomUUID();const r=(e=e||{}).random||(e.rng||uc.default)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(let s=0;s<16;++s)t[n+s]=r[s];return t}return(0,lc.unsafeStringify)(r)};Pn.default=cc;var Ln={},Dn={};function dc(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function yr(e,t){return e<<t|e>>>32-t}Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.default=void 0;var pc=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const i=unescape(encodeURIComponent(e));e=[];for(let o=0;o<i.length;++o)e.push(i.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const r=e.length/4+2,s=Math.ceil(r/16),a=new Array(s);for(let i=0;i<s;++i){const o=new Uint32Array(16);for(let l=0;l<16;++l)o[l]=e[64*i+4*l]<<24|e[64*i+4*l+1]<<16|e[64*i+4*l+2]<<8|e[64*i+4*l+3];a[i]=o}a[s-1][14]=8*(e.length-1)/Math.pow(2,32),a[s-1][14]=Math.floor(a[s-1][14]),a[s-1][15]=8*(e.length-1)&4294967295;for(let i=0;i<s;++i){const o=new Uint32Array(80);for(let h=0;h<16;++h)o[h]=a[i][h];for(let h=16;h<80;++h)o[h]=yr(o[h-3]^o[h-8]^o[h-14]^o[h-16],1);let l=n[0],u=n[1],p=n[2],c=n[3],d=n[4];for(let h=0;h<80;++h){const m=Math.floor(h/20),g=yr(l,5)+dc(m,u,p,c)+d+t[m]+o[h]>>>0;d=c,c=p,p=yr(u,30)>>>0,u=l,l=g}n[0]=n[0]+l>>>0,n[1]=n[1]+u>>>0,n[2]=n[2]+p>>>0,n[3]=n[3]+c>>>0,n[4]=n[4]+d>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};Dn.default=pc,Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var hc=$o(Ke),mc=$o(Dn);function $o(e){return e&&e.__esModule?e:{default:e}}var fc=(0,hc.default)("v5",80,mc.default);Ln.default=fc;var Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;Fn.default="00000000-0000-0000-0000-000000000000";var Un={};Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var gc=function(e){return e&&e.__esModule?e:{default:e}}(st),yc=function(e){if(!(0,gc.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function Kr(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}Un.default=yc,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return i.default}});var t=p(Cn),n=p(An),r=p(Pn),s=p(Ln),a=p(Fn),i=p(Un),o=p(st),l=p(We),u=p(Ht);function p(c){return c&&c.__esModule?c:{default:c}}}(Po),Kr.prototype.convert=function(e){var t,n,r,s={},a=this.srcAlphabet.length,i=this.dstAlphabet.length,o=e.length,l=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<o;t++)s[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,r=0,t=0;t<o;t++)(n=n*a+s[t])>=i?(s[r++]=parseInt(n/i,10),n%=i):r>0&&(s[r++]=0);o=r,l=this.dstAlphabet.slice(n,n+1).concat(l)}while(r!==0);return l},Kr.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var vc=Kr;function Ot(e,t){var n=new vc(e,t);return function(r){return n.convert(r)}}Ot.BIN="01",Ot.OCT="01234567",Ot.DEC="0123456789",Ot.HEX="0123456789abcdef";var _c=Ot;const{v4:vr,validate:bc}=Po,rn=_c,_r={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},Ec={consistentLength:!0};let br;const ai=(e,t,n)=>{const r=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?r.padStart(n.shortIdLength,n.paddingChar):r},ii=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var Sc=(()=>{const e=(t,n)=>{const r=t||_r.flickrBase58,s={...Ec,...n};if([...new Set(Array.from(r))].length!==r.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const a=(i=r.length,Math.ceil(Math.log(2**128)/Math.log(i)));var i;const o={shortIdLength:a,consistentLength:s.consistentLength,paddingChar:r[0]},l=rn(rn.HEX,r),u=rn(r,rn.HEX),p=()=>ai(vr(),l,o),c={alphabet:r,fromUUID:d=>ai(d,l,o),maxLength:a,generate:p,new:p,toUUID:d=>ii(d,u),uuid:vr,validate:(d,h=!1)=>{if(!d||typeof d!="string")return!1;const m=s.consistentLength?d.length===a:d.length<=a,g=d.split("").every(f=>r.includes(f));return h===!1?m&&g:m&&g&&bc(ii(d,u))}};return Object.freeze(c),c};return e.constants=_r,e.uuid=vr,e.generate=()=>(br||(br=e(_r.flickrBase58).generate),br()),e})();const kc=Jn(Sc),Bo={[ae.NOT_STARTED]:"[ ]",[ae.IN_PROGRESS]:"[/]",[ae.COMPLETE]:"[x]",[ae.CANCELLED]:"[-]"},jo=kc(void 0,{consistentLength:!0});function Tc(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const r=Tc(n,t);if(r)return r}}function Go(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:s=!0}=t;return qo(e,{shallow:n,excludeUuid:r,shortUuid:s}).join(`
`)}function qo(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:s=!0}=t;let a="";r||(a=`UUID:${s?function(o){try{return jo.fromUUID(o)}catch{return o}}(e.uuid):e.uuid} `);const i=`${Bo[e.state]} ${a}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[i]:[i,...(e.subTasksData||[]).map(o=>qo(o,t).map(l=>`-${l}`)).flat()]}function wc(e,t){var r;const n=(r=e.subTasksData)==null?void 0:r.map(s=>wc(s,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(s=>s.uuid))||[],subTasksData:n}}function Eh(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let r=0;for(const u of n)if(u.trim()&&oi(u)===0)try{Hr(u,t),r++}catch{}if(r===0)throw new Error("No root task found");if(r>1)throw new Error(`Multiple root tasks found (${r}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const s=e.split(`
`);function a(){for(;s.length>0;){const u=s.shift(),p=oi(u);try{return{task:Hr(u,t),level:p}}catch{}}}const i=a();if(!i)throw new Error("No root task found");const o=[i.task];let l;for(;l=a();){const u=o[l.level-1];if(!u)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${Go(i.task)}`);u.subTasksData&&u.subTasks||(u.subTasks=[],u.subTasksData=[]),u.subTasksData.push(l.task),u.subTasks.push(l.task.uuid),o[l.level]=l.task,o.splice(l.level+1)}return i.task}function oi(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Hr(e,t={}){const{excludeUuid:n=!1,shortUuid:r=!0}=t;let s=0;for(;s<e.length&&(e[s]===" "||e[s]==="	"||e[s]==="-");)s++;const a=e.substring(s),i=a.match(/^\s*\[([ x\-/?])\]/);if(!i)throw new Error(`Invalid task line: ${e} (missing state)`);const o=i[1],l=Object.entries(Bo).reduce((h,[m,g])=>(h[g.substring(1,2)]=m,h),{})[o]||ae.NOT_STARTED,u=a.substring(i.index+i[0].length).trim();let p,c,d;if(n){const h=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=u.match(h);if(!m){const g=/\b(?:name|NAME):/i.test(u),f=/\b(?:description|DESCRIPTION):/i.test(u);throw!g||!f?new Error(`Invalid task line: ${e} (missing required fields)`):u.toLowerCase().indexOf("name:")<u.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(c=m[1].trim(),d=m[2].trim(),!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);p=crypto.randomUUID()}else{const h=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=u.match(h);if(!m){const g=/\b(?:uuid|UUID):/i.test(u),f=/\b(?:name|NAME):/i.test(u),_=/\b(?:description|DESCRIPTION):/i.test(u);if(!g||!f||!_)throw new Error(`Invalid task line: ${e} (missing required fields)`);const S=u.toLowerCase().indexOf("uuid:"),b=u.toLowerCase().indexOf("name:"),N=u.toLowerCase().indexOf("description:");throw S<b&&b<N?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(p=m[1].trim(),c=m[2].trim(),d=m[3].trim(),!p||!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(p==="NEW_UUID")p=crypto.randomUUID();else if(r)try{p=function(g){try{return jo.toUUID(g)}catch{return g}}(p)}catch{}}return{uuid:p,name:c,description:d,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Ys.USER}}const wt=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:ae.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Ys.USER,...e}),ui=wt({name:"Task 1.1",description:"This is the first sub task",state:ae.IN_PROGRESS}),li=wt({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:ae.NOT_STARTED}),ci=wt({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:ae.IN_PROGRESS}),di=wt({name:"Task 1.2",description:"This is the second sub task",state:ae.COMPLETE,subTasks:[li.uuid,ci.uuid],subTasksData:[li,ci]}),pi=wt({name:"Task 1.3",description:"This is the third sub task",state:ae.CANCELLED}),Sh=Go(wt({name:"Task 1",description:"This is the first task",state:ae.NOT_STARTED,subTasks:[ui.uuid,di.uuid,pi.uuid],subTasksData:[ui,di,pi]}));function Vo(e){const t=e.split(`
`);let n=null;const r={created:[],updated:[],deleted:[]};for(const s of t){const a=s.trim();if(a!=="## Created Tasks")if(a!=="## Updated Tasks")if(a!=="## Deleted Tasks"){if(n&&(a.startsWith("[ ]")||a.startsWith("[/]")||a.startsWith("[x]")||a.startsWith("[-]")))try{const i=Hr(a,{excludeUuid:!1,shortUuid:!0});i&&r[n].push(i)}catch{}}else n="deleted";else n="updated";else n="created"}return r}function kh(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Vo(Yo(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Yo(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),r=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let s=n.length;for(const o of r){const l=n.indexOf(o);l!==-1&&l<s&&(s=l)}const a=n.substring(0,s),i=a.indexOf(`
`);return i===-1?"":a.substring(i+1).trim()}function Th(e){return Vo(Yo(e))}class wh{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:r}=t,s=this.buildTaskContext(n,r);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${s}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:r}=t;return r.targetTaskPath.length>1?`${r.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:r,targetTaskPath:s}=n;let a=`This task is part of a larger project: "${r.name}"`;return r.description&&(a+=`

**Project Description:** ${r.description}`),s.length>1&&(a+=`

**Task Path:** ${s.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(a+=`

**Subtasks:**`,t.subTasksData.forEach((i,o)=>{a+=`
${o+1}. ${i.name} (${i.state})`,i.description&&(a+=` - ${i.description}`)})),a}static parseJsonTaskList(t){try{const n=JSON.parse(t);return Array.isArray(n)?n:this.flattenTaskTree(n)}catch{return[]}}static flattenTaskTree(t,n=[]){if(n.push({uuid:t.uuid,name:t.name,description:t.description,state:t.state,level:t.level}),t.subTasks)for(const r of t.subTasks)this.flattenTaskTree(r,n);return n}}const zs=class zs{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t==null?"":t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,r=!0){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),i=s.match(a);if(i&&i[1])return i[1].toLowerCase()==="true"}return r}static parseString(t,n,r=""){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),i=s.match(a);if(i&&i[1])return i[1].trim()}return r}static updateFrontmatter(t,n,r){const s=t.match(this.frontmatterRegex),a=typeof r!="string"||/^(true|false)$/.test(r.toLowerCase())?String(r):`"${r}"`;if(s){const i=s[1],o=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(i.match(o)){const l=i.replace(o,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${l}---

`)}{const l=`${i.endsWith(`
`)?i:i+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${l}---

`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let r=t;this.hasFrontmatter(r)&&(r=this.extractContent(r));for(const[s,a]of Object.entries(n))r=this.updateFrontmatter(r,s,a);return r}};zs.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let re=zs;const Qe=class Qe{static parseRuleFile(t,n){const r=re.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),s=re.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:s,description:r||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=re.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=re.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return re.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return re.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return re.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return re.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return re.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return ye.ALWAYS_ATTACHED;case"manual":return ye.MANUAL;case"agent_requested":return ye.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case ye.ALWAYS_ATTACHED:return"always_apply";case ye.MANUAL:return"manual";case ye.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return re.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const r=this.mapRuleTypeToString(n);return re.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,r)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const r=this.getAlwaysApplyFrontmatterKey(t),s=this.getDescriptionFrontmatterKey(t);return r?ye.ALWAYS_ATTACHED:s&&s.trim()!==""?ye.AGENT_REQUESTED:ye.MANUAL}};Qe.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",Qe.DESCRIPTION_FRONTMATTER_KEY="description",Qe.TYPE_FRONTMATTER_KEY="type",Qe.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],Qe.DEFAULT_RULE_TYPE=ye.MANUAL;let Zr=Qe;const Ic=".augment",Nc="rules",Cc=".augment-guidelines",xc="AGENTS.md",Ih=[Cc,xc];var Rc=(e=>(e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink",e))(Rc||{});const Xs=class Xs{static setClientWorkspaces(t){this._instance===void 0?this._instance=t:$l().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}};Xs._instance=void 0;let Wr=Xs;const Nh=()=>Wr.getClientWorkspaces();var Ac=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Ac||{}),Oc=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Oc||{});function Pc(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Ko{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Pc(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Mc=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e[e.memory_acceptance=3]="memory_acceptance",e))(Mc||{}),Lc=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Lc||{});class Ho extends Ko{constructor(){super()}static create(){return new Ho}}var Dc=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalTimedOutWaitingForStartupCommand="vs-code-terminal-timed-out-waiting-for-startup-command",e.vsCodeTerminalTimedOutWaitingForTerminalProcessId="vs-code-terminal-timed-out-waiting-for-terminal-process-id",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalReadEmptyOutput="vs-code-terminal-read-empty-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalErrorCreatingBashEnvironment="vs-code-terminal-error-creating-bash-environment",e.vsCodeTerminalErrorAccessingConfiguration="vs-code-terminal-error-accessing-configuration",e.vsCodeTerminalErrorSavingSettings="vs-code-terminal-error-saving-settings",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.vsCodeTerminalScriptCaptureFailToStart="vs-code-terminal-script-capture-fail-to-start",e.vsCodeTerminalScriptCaptureReadOutputFailure="vs-code-terminal-script-capture-read-output-failure",e.vsCodeTerminalScriptCaptureHybridReadOutputException="vs-code-terminal-script-capture-hybrid-read-output-exception",e.vsCodeTerminalScriptCaptureFileShrank="vs-code-terminal-script-capture-file-shrank",e.vsCodeTerminalScriptCaptureReadEmptyOutput="vs-code-terminal-script-capture-read-empty-output",e.vsCodeTerminalScriptCaptureStartupScriptFailure="vs-code-terminal-script-capture-startup-script-failure",e.vsCodeTerminalScriptCaptureFailedAfterRetry="vs-code-terminal-script-capture-failed-after-retry",e.vsCodeTerminalScriptCaptureCommandFilteringFailed="vs-code-terminal-script-capture-command-filtering-failed",e.vsCodeTerminalScriptCapturePlatformCompatibility="vs-code-terminal-script-capture-platform-compatibility",e.vsCodeTerminalScriptCaptureCwdTrackingSetupFailure="vs-code-terminal-script-capture-cwd-tracking-setup-failure",e.vsCodeTerminalScriptCaptureCwdTrackingSetupEmpty="vs-code-terminal-script-capture-cwd-tracking-setup-empty",e.vsCodeTerminalScriptCaptureEndMarkerSetupFailure="vs-code-terminal-script-capture-end-marker-setup-failure",e.vsCodeTerminalScriptCaptureStartMarkerSetupFailure="vs-code-terminal-script-capture-start-marker-setup-failure",e.vsCodeTerminalScriptCaptureMarkerSetupEmpty="vs-code-terminal-script-capture-marker-setup-empty",e.vsCodeTerminalScriptCaptureEndMarkerNotFound="vs-code-terminal-script-capture-end-marker-not-found",e.vsCodeTerminalScriptCapturePidNull="vs-code-terminal-script-capture-pid-null",e.vsCodeTerminalScriptCapturePidDead="vs-code-terminal-script-capture-pid-dead",e.vsCodeTerminalScriptCaptureGetChildProcessException="vs-code-terminal-script-capture-get-child-process-exception",e.vsCodeTerminalScriptCaptureGetProcessStateException="vs-code-terminal-script-capture-get-process-state-exception",e.vsCodeTerminalScriptCaptureFallbackUsed="vs-code-terminal-script-capture-fallback-used",e.vsCodeTerminalScriptCaptureExecutionEventWhileScriptCaptureReady="vs-code-terminal-script-capture-execution-event-while-script-capture-ready",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e.modelSelectionChange="model-selection-change",e))(Dc||{}),Fc=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(Fc||{}),Uc=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Uc||{});class Zo extends Ko{constructor(){super()}static create(){return new Zo}}var $c=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))($c||{}),Bc=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(Bc||{}),jc=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(jc||{}),Gc=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Gc||{}),qc=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(qc||{}),Vc=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(Vc||{}),Yc=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(Yc||{}),Kc=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Kc||{}),Hc=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(Hc||{}),Zc=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(Zc||{}),Wc=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(Wc||{}),zc=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(zc||{}),Xc=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(Xc||{}),Jc=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(Jc||{});function te(e,t){return t in e&&e[t]!==void 0}function Qc(e){return te(e,"file")}function ed(e){return te(e,"recentFile")}function td(e){return te(e,"folder")}function nd(e){return te(e,"sourceFolder")}function Ch(e){return te(e,"sourceFolderGroup")}function xh(e){return te(e,"selection")}function rd(e){return te(e,"externalSource")}function Rh(e){return te(e,"allDefaultContext")}function Ah(e){return te(e,"clearContext")}function Oh(e){return te(e,"userGuidelines")}function Ph(e){return te(e,"agentMemories")}function sd(e){return te(e,"personality")}function ad(e){return te(e,"rule")}function Mh(e){return te(e,"task")}const Lh={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Dh={clearContext:!0,label:"Clear Context",id:"clearContext"},Fh={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Uh={agentMemories:{},label:"Agent Memories",id:"agentMemories"},hi=[{personality:{type:le.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:le.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:le.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:le.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function $h(e){return te(e,"group")}function Bh(e){const t=new Map;return e.forEach(n=>{Qc(n)?t.set("file",[...t.get("file")??[],n]):ed(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):td(n)?t.set("folder",[...t.get("folder")??[],n]):rd(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):nd(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):sd(n)?t.set("personality",[...t.get("personality")??[],n]):ad(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function id(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const r={label:Pu(e.pathName).split("/").filter(s=>s.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const s=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;r.label+=s,r.name+=s,r.id+=s}else if(e.range){const s=`:L${e.range.start}-${e.range.stop}`;r.label+=s,r.name+=s,r.id+=s}return r}function od(e){const t=e.path.split("/"),n=t[t.length-1],r=n.endsWith(".md")?n.slice(0,-3):n,s=`${Ic}/${Nc}/${e.path}`;return{label:r,name:s,id:s}}var P,mi;(function(e){e.assertEqual=t=>{},e.assertIs=function(t){},e.assertNever=function(t){throw new Error},e.arrayToEnum=t=>{const n={};for(const r of t)n[r]=r;return n},e.getValidEnumValues=t=>{const n=e.objectKeys(t).filter(s=>typeof t[t[s]]!="number"),r={};for(const s of n)r[s]=t[s];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(n){return t[n]}),e.objectKeys=typeof Object.keys=="function"?t=>Object.keys(t):t=>{const n=[];for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.push(r);return n},e.find=(t,n)=>{for(const r of t)if(n(r))return r},e.isInteger=typeof Number.isInteger=="function"?t=>Number.isInteger(t):t=>typeof t=="number"&&Number.isFinite(t)&&Math.floor(t)===t,e.joinValues=function(t,n=" | "){return t.map(r=>typeof r=="string"?`'${r}'`:r).join(n)},e.jsonStringifyReplacer=(t,n)=>typeof n=="bigint"?n.toString():n})(P||(P={})),function(e){e.mergeShapes=(t,n)=>({...t,...n})}(mi||(mi={}));const T=P.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),qe=e=>{switch(typeof e){case"undefined":return T.undefined;case"string":return T.string;case"number":return Number.isNaN(e)?T.nan:T.number;case"boolean":return T.boolean;case"function":return T.function;case"bigint":return T.bigint;case"symbol":return T.symbol;case"object":return Array.isArray(e)?T.array:e===null?T.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?T.promise:typeof Map<"u"&&e instanceof Map?T.map:typeof Set<"u"&&e instanceof Set?T.set:typeof Date<"u"&&e instanceof Date?T.date:T.object;default:return T.unknown}},E=P.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class $e extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(a){return a.message},r={_errors:[]},s=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(s);else if(i.code==="invalid_return_type")s(i.returnTypeError);else if(i.code==="invalid_arguments")s(i.argumentsError);else if(i.path.length===0)r._errors.push(n(i));else{let o=r,l=0;for(;l<i.path.length;){const u=i.path[l];l===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(n(i))):o[u]=o[u]||{_errors:[]},o=o[u],l++}}};return s(this),r}static assert(t){if(!(t instanceof $e))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,P.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const s of this.issues)if(s.path.length>0){const a=s.path[0];n[a]=n[a]||[],n[a].push(t(s))}else r.push(t(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}$e.create=e=>new $e(e);const zr=(e,t)=>{let n;switch(e.code){case E.invalid_type:n=e.received===T.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case E.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,P.jsonStringifyReplacer)}`;break;case E.unrecognized_keys:n=`Unrecognized key(s) in object: ${P.joinValues(e.keys,", ")}`;break;case E.invalid_union:n="Invalid input";break;case E.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${P.joinValues(e.options)}`;break;case E.invalid_enum_value:n=`Invalid enum value. Expected ${P.joinValues(e.options)}, received '${e.received}'`;break;case E.invalid_arguments:n="Invalid function arguments";break;case E.invalid_return_type:n="Invalid function return type";break;case E.invalid_date:n="Invalid date";break;case E.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:P.assertNever(e.validation):n=e.validation!=="regex"?`Invalid ${e.validation}`:"Invalid";break;case E.too_small:n=e.type==="array"?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"||e.type==="bigint"?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case E.too_big:n=e.type==="array"?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case E.custom:n="Invalid input";break;case E.invalid_intersection_types:n="Intersection results could not be merged";break;case E.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case E.not_finite:n="Number must be finite";break;default:n=t.defaultError,P.assertNever(e)}return{message:n}};let ud=zr;function k(e,t){const n=ud,r=(s=>{const{data:a,path:i,errorMaps:o,issueData:l}=s,u=[...i,...l.path||[]],p={...l,path:u};if(l.message!==void 0)return{...l,path:u,message:l.message};let c="";const d=o.filter(h=>!!h).slice().reverse();for(const h of d)c=h(p,{data:a,defaultError:c}).message;return{...l,path:u,message:c}})({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===zr?void 0:zr].filter(s=>!!s)});e.common.issues.push(r)}class ee{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const s of n){if(s.status==="aborted")return C;s.status==="dirty"&&t.dirty(),r.push(s.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const s of n){const a=await s.key,i=await s.value;r.push({key:a,value:i})}return ee.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const s of n){const{key:a,value:i}=s;if(a.status==="aborted"||i.status==="aborted")return C;a.status==="dirty"&&t.dirty(),i.status==="dirty"&&t.dirty(),a.value==="__proto__"||i.value===void 0&&!s.alwaysSet||(r[a.value]=i.value)}return{status:t.value,value:r}}}const C=Object.freeze({status:"aborted"}),Xr=e=>({status:"dirty",value:e}),de=e=>({status:"valid",value:e}),fi=e=>e.status==="aborted",gi=e=>e.status==="dirty",kt=e=>e.status==="valid",$n=e=>typeof Promise<"u"&&e instanceof Promise;var I;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(I||(I={}));class Ie{constructor(t,n,r,s){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const yi=(e,t)=>{if(kt(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new $e(e.common.issues);return this._error=n,this._error}}};function R(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:s}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(a,i)=>{const{message:o}=e;return a.code==="invalid_enum_value"?{message:o??i.defaultError}:i.data===void 0?{message:o??r??i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:o??n??i.defaultError}},description:s}}class O{get description(){return this._def.description}_getType(t){return qe(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:qe(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new ee,ctx:{common:t.parent.common,data:t.data,parsedType:qe(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if($n(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){const r={common:{issues:[],async:(n==null?void 0:n.async)??!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:qe(t)},s=this._parseSync({data:t,path:r.path,parent:r});return yi(r,s)}"~validate"(t){var r,s;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:qe(t)};if(!this["~standard"].async)try{const a=this._parseSync({data:t,path:[],parent:n});return kt(a)?{value:a.value}:{issues:n.common.issues}}catch(a){(s=(r=a==null?void 0:a.message)==null?void 0:r.toLowerCase())!=null&&s.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:n}).then(a=>kt(a)?{value:a.value}:{issues:n.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:qe(t)},s=this._parse({data:t,path:r.path,parent:r}),a=await($n(s)?s:Promise.resolve(s));return yi(r,a)}refine(t,n){const r=s=>typeof n=="string"||n===void 0?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,a)=>{const i=t(s),o=()=>a.addIssue({code:E.custom,...r(s)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>!!l||(o(),!1)):!!i||(o(),!1)})}refinement(t,n){return this._refinement((r,s)=>!!t(r)||(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(t){return new tt({schema:this,typeName:x.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return Ue.create(this,this._def)}nullable(){return ot.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return we.create(this)}promise(){return Kn.create(this,this._def)}or(t){return qn.create([this,t],this._def)}and(t){return Vn.create(this,t,this._def)}transform(t){return new tt({...R(this._def),schema:this,typeName:x.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new Hn({...R(this._def),innerType:this,defaultValue:n,typeName:x.ZodDefault})}brand(){return new Jo({typeName:x.ZodBranded,type:this,...R(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new Zn({...R(this._def),innerType:this,catchValue:n,typeName:x.ZodCatch})}describe(t){return new this.constructor({...this._def,description:t})}pipe(t){return Zs.create(this,t)}readonly(){return Wn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const ld=/^c[^\s-]{8,}$/i,cd=/^[0-9a-z]+$/,dd=/^[0-9A-HJKMNP-TV-Z]{26}$/i,pd=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,hd=/^[a-z0-9_-]{21}$/i,md=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,fd=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,gd=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Er;const yd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,vd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,_d=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,bd=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Ed=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Sd=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Wo="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",kd=new RegExp(`^${Wo}$`);function zo(e){let t="[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`),`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function Td(e){let t=`${Wo}T${zo(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function wd(e,t){return!(t!=="v4"&&t||!yd.test(e))||!(t!=="v6"&&t||!_d.test(e))}function Id(e,t){if(!md.test(e))return!1;try{const[n]=e.split(".");if(!n)return!1;const r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(r));return typeof s=="object"&&s!==null&&(!("typ"in s)||(s==null?void 0:s.typ)==="JWT")&&!!s.alg&&(!t||s.alg===t)}catch{return!1}}function Nd(e,t){return!(t!=="v4"&&t||!vd.test(e))||!(t!=="v6"&&t||!bd.test(e))}class Fe extends O{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==T.string){const s=this._getOrReturnCtx(t);return k(s,{code:E.invalid_type,expected:T.string,received:s.parsedType}),C}const n=new ee;let r;for(const s of this._def.checks)if(s.kind==="min")t.data.length<s.value&&(r=this._getOrReturnCtx(t,r),k(r,{code:E.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")t.data.length>s.value&&(r=this._getOrReturnCtx(t,r),k(r,{code:E.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const a=t.data.length>s.value,i=t.data.length<s.value;(a||i)&&(r=this._getOrReturnCtx(t,r),a?k(r,{code:E.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&k(r,{code:E.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")gd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"email",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Er||(Er=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Er.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"emoji",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")pd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"uuid",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")hd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"nanoid",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")ld.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"cuid",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")cd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"cuid2",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")dd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"ulid",code:E.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(t.data)}catch{r=this._getOrReturnCtx(t,r),k(r,{validation:"url",code:E.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"regex",code:E.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?t.data=t.data.trim():s.kind==="includes"?t.data.includes(s.value,s.position)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?t.data=t.data.toLowerCase():s.kind==="toUpperCase"?t.data=t.data.toUpperCase():s.kind==="startsWith"?t.data.startsWith(s.value)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?t.data.endsWith(s.value)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?Td(s).test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?kd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?new RegExp(`^${zo(s)}$`).test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{code:E.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?fd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"duration",code:E.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?wd(t.data,s.version)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"ip",code:E.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?Id(t.data,s.alg)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"jwt",code:E.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?Nd(t.data,s.version)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"cidr",code:E.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?Ed.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"base64",code:E.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?Sd.test(t.data)||(r=this._getOrReturnCtx(t,r),k(r,{validation:"base64url",code:E.invalid_string,message:s.message}),n.dirty()):P.assertNever(s);return{status:n.value,value:t.data}}_regex(t,n,r){return this.refinement(s=>t.test(s),{validation:n,code:E.invalid_string,...I.errToObj(r)})}_addCheck(t){return new Fe({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...I.errToObj(t)})}url(t){return this._addCheck({kind:"url",...I.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...I.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...I.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...I.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...I.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...I.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...I.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...I.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...I.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...I.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...I.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...I.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...I.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,...I.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...I.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...I.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...I.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...I.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...I.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...I.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...I.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...I.errToObj(n)})}nonempty(t){return this.min(1,I.errToObj(t))}trim(){return new Fe({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Fe({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Fe({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}function Cd(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=n>r?n:r;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}Fe.create=e=>new Fe({checks:[],typeName:x.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...R(e)});class Tt extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==T.number){const s=this._getOrReturnCtx(t);return k(s,{code:E.invalid_type,expected:T.number,received:s.parsedType}),C}let n;const r=new ee;for(const s of this._def.checks)s.kind==="int"?P.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),k(n,{code:E.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="multipleOf"?Cd(t.data,s.value)!==0&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),k(n,{code:E.not_finite,message:s.message}),r.dirty()):P.assertNever(s);return{status:r.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,I.toString(n))}gt(t,n){return this.setLimit("min",t,!1,I.toString(n))}lte(t,n){return this.setLimit("max",t,!0,I.toString(n))}lt(t,n){return this.setLimit("max",t,!1,I.toString(n))}setLimit(t,n,r,s){return new Tt({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:I.toString(s)}]})}_addCheck(t){return new Tt({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:I.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:I.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:I.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:I.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:I.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:I.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:I.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:I.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:I.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&P.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}Tt.create=e=>new Tt({checks:[],typeName:x.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...R(e)});class Zt extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==T.bigint)return this._getInvalidInput(t);let n;const r=new ee;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),k(n,{code:E.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):P.assertNever(s);return{status:r.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.bigint,received:n.parsedType}),C}gte(t,n){return this.setLimit("min",t,!0,I.toString(n))}gt(t,n){return this.setLimit("min",t,!1,I.toString(n))}lte(t,n){return this.setLimit("max",t,!0,I.toString(n))}lt(t,n){return this.setLimit("max",t,!1,I.toString(n))}setLimit(t,n,r,s){return new Zt({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:I.toString(s)}]})}_addCheck(t){return new Zt({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:I.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:I.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:I.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:I.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:I.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Zt.create=e=>new Zt({checks:[],typeName:x.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...R(e)});class Jr extends O{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==T.boolean){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.boolean,received:n.parsedType}),C}return de(t.data)}}Jr.create=e=>new Jr({typeName:x.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...R(e)});class Bn extends O{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==T.date){const s=this._getOrReturnCtx(t);return k(s,{code:E.invalid_type,expected:T.date,received:s.parsedType}),C}if(Number.isNaN(t.data.getTime()))return k(this._getOrReturnCtx(t),{code:E.invalid_date}),C;const n=new ee;let r;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(r=this._getOrReturnCtx(t,r),k(r,{code:E.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(r=this._getOrReturnCtx(t,r),k(r,{code:E.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):P.assertNever(s);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Bn({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:I.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:I.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}Bn.create=e=>new Bn({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:x.ZodDate,...R(e)});class vi extends O{_parse(t){if(this._getType(t)!==T.symbol){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.symbol,received:n.parsedType}),C}return de(t.data)}}vi.create=e=>new vi({typeName:x.ZodSymbol,...R(e)});class jn extends O{_parse(t){if(this._getType(t)!==T.undefined){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.undefined,received:n.parsedType}),C}return de(t.data)}}jn.create=e=>new jn({typeName:x.ZodUndefined,...R(e)});class Gn extends O{_parse(t){if(this._getType(t)!==T.null){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.null,received:n.parsedType}),C}return de(t.data)}}Gn.create=e=>new Gn({typeName:x.ZodNull,...R(e)});class _i extends O{constructor(){super(...arguments),this._any=!0}_parse(t){return de(t.data)}}_i.create=e=>new _i({typeName:x.ZodAny,...R(e)});class Qr extends O{constructor(){super(...arguments),this._unknown=!0}_parse(t){return de(t.data)}}Qr.create=e=>new Qr({typeName:x.ZodUnknown,...R(e)});class ze extends O{_parse(t){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.never,received:n.parsedType}),C}}ze.create=e=>new ze({typeName:x.ZodNever,...R(e)});class bi extends O{_parse(t){if(this._getType(t)!==T.undefined){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.void,received:n.parsedType}),C}return de(t.data)}}bi.create=e=>new bi({typeName:x.ZodVoid,...R(e)});class we extends O{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),s=this._def;if(n.parsedType!==T.array)return k(n,{code:E.invalid_type,expected:T.array,received:n.parsedType}),C;if(s.exactLength!==null){const i=n.data.length>s.exactLength.value,o=n.data.length<s.exactLength.value;(i||o)&&(k(n,{code:i?E.too_big:E.too_small,minimum:o?s.exactLength.value:void 0,maximum:i?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(k(n,{code:E.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(k(n,{code:E.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((i,o)=>s.type._parseAsync(new Ie(n,i,n.path,o)))).then(i=>ee.mergeArray(r,i));const a=[...n.data].map((i,o)=>s.type._parseSync(new Ie(n,i,n.path,o)));return ee.mergeArray(r,a)}get element(){return this._def.type}min(t,n){return new we({...this._def,minLength:{value:t,message:I.toString(n)}})}max(t,n){return new we({...this._def,maxLength:{value:t,message:I.toString(n)}})}length(t,n){return new we({...this._def,exactLength:{value:t,message:I.toString(n)}})}nonempty(t){return this.min(1,t)}}function gt(e){if(e instanceof q){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Ue.create(gt(r))}return new q({...e._def,shape:()=>t})}return e instanceof we?new we({...e._def,type:gt(e.element)}):e instanceof Ue?Ue.create(gt(e.unwrap())):e instanceof ot?ot.create(gt(e.unwrap())):e instanceof at?at.create(e.items.map(t=>gt(t))):e}we.create=(e,t)=>new we({type:e,minLength:null,maxLength:null,exactLength:null,typeName:x.ZodArray,...R(t)});class q extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=P.objectKeys(t);return this._cached={shape:t,keys:n},this._cached}_parse(t){if(this._getType(t)!==T.object){const l=this._getOrReturnCtx(t);return k(l,{code:E.invalid_type,expected:T.object,received:l.parsedType}),C}const{status:n,ctx:r}=this._processInputParams(t),{shape:s,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof ze&&this._def.unknownKeys==="strip"))for(const l in r.data)a.includes(l)||i.push(l);const o=[];for(const l of a){const u=s[l],p=r.data[l];o.push({key:{status:"valid",value:l},value:u._parse(new Ie(r,p,r.path,l)),alwaysSet:l in r.data})}if(this._def.catchall instanceof ze){const l=this._def.unknownKeys;if(l==="passthrough")for(const u of i)o.push({key:{status:"valid",value:u},value:{status:"valid",value:r.data[u]}});else if(l==="strict")i.length>0&&(k(r,{code:E.unrecognized_keys,keys:i}),n.dirty());else if(l!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const l=this._def.catchall;for(const u of i){const p=r.data[u];o.push({key:{status:"valid",value:u},value:l._parse(new Ie(r,p,r.path,u)),alwaysSet:u in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const l=[];for(const u of o){const p=await u.key,c=await u.value;l.push({key:p,value:c,alwaysSet:u.alwaysSet})}return l}).then(l=>ee.mergeObjectSync(n,l)):ee.mergeObjectSync(n,o)}get shape(){return this._def.shape()}strict(t){return I.errToObj,new q({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var a,i;const s=((i=(a=this._def).errorMap)==null?void 0:i.call(a,n,r).message)??r.defaultError;return n.code==="unrecognized_keys"?{message:I.errToObj(t).message??s}:{message:s}}}:{}})}strip(){return new q({...this._def,unknownKeys:"strip"})}passthrough(){return new q({...this._def,unknownKeys:"passthrough"})}extend(t){return new q({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new q({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:x.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new q({...this._def,catchall:t})}pick(t){const n={};for(const r of P.objectKeys(t))t[r]&&this.shape[r]&&(n[r]=this.shape[r]);return new q({...this._def,shape:()=>n})}omit(t){const n={};for(const r of P.objectKeys(this.shape))t[r]||(n[r]=this.shape[r]);return new q({...this._def,shape:()=>n})}deepPartial(){return gt(this)}partial(t){const n={};for(const r of P.objectKeys(this.shape)){const s=this.shape[r];t&&!t[r]?n[r]=s:n[r]=s.optional()}return new q({...this._def,shape:()=>n})}required(t){const n={};for(const r of P.objectKeys(this.shape))if(t&&!t[r])n[r]=this.shape[r];else{let s=this.shape[r];for(;s instanceof Ue;)s=s._def.innerType;n[r]=s}return new q({...this._def,shape:()=>n})}keyof(){return Xo(P.objectKeys(this.shape))}}q.create=(e,t)=>new q({shape:()=>e,unknownKeys:"strip",catchall:ze.create(),typeName:x.ZodObject,...R(t)}),q.strictCreate=(e,t)=>new q({shape:()=>e,unknownKeys:"strict",catchall:ze.create(),typeName:x.ZodObject,...R(t)}),q.lazycreate=(e,t)=>new q({shape:e,unknownKeys:"strip",catchall:ze.create(),typeName:x.ZodObject,...R(t)});class qn extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;if(n.common.async)return Promise.all(r.map(async s=>{const a={...n,common:{...n.common,issues:[]},parent:null};return{result:await s._parseAsync({data:n.data,path:n.path,parent:a}),ctx:a}})).then(function(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return n.common.issues.push(...i.ctx.common.issues),i.result;const a=s.map(i=>new $e(i.ctx.common.issues));return k(n,{code:E.invalid_union,unionErrors:a}),C});{let s;const a=[];for(const o of r){const l={...n,common:{...n.common,issues:[]},parent:null},u=o._parseSync({data:n.data,path:n.path,parent:l});if(u.status==="valid")return u;u.status!=="dirty"||s||(s={result:u,ctx:l}),l.common.issues.length&&a.push(l.common.issues)}if(s)return n.common.issues.push(...s.ctx.common.issues),s.result;const i=a.map(o=>new $e(o));return k(n,{code:E.invalid_union,unionErrors:i}),C}}get options(){return this._def.options}}qn.create=(e,t)=>new qn({options:e,typeName:x.ZodUnion,...R(t)});const Be=e=>e instanceof ts?Be(e.schema):e instanceof tt?Be(e.innerType()):e instanceof Yn?[e.value]:e instanceof it?e.options:e instanceof ns?P.objectValues(e.enum):e instanceof Hn?Be(e._def.innerType):e instanceof jn?[void 0]:e instanceof Gn?[null]:e instanceof Ue?[void 0,...Be(e.unwrap())]:e instanceof ot?[null,...Be(e.unwrap())]:e instanceof Jo||e instanceof Wn?Be(e.unwrap()):e instanceof Zn?Be(e._def.innerType):[];class Ks extends O{_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==T.object)return k(n,{code:E.invalid_type,expected:T.object,received:n.parsedType}),C;const r=this.discriminator,s=n.data[r],a=this.optionsMap.get(s);return a?n.common.async?a._parseAsync({data:n.data,path:n.path,parent:n}):a._parseSync({data:n.data,path:n.path,parent:n}):(k(n,{code:E.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),C)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){const s=new Map;for(const a of n){const i=Be(a.shape[t]);if(!i.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const o of i){if(s.has(o))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(o)}`);s.set(o,a)}}return new Ks({typeName:x.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:s,...R(r)})}}function es(e,t){const n=qe(e),r=qe(t);if(e===t)return{valid:!0,data:e};if(n===T.object&&r===T.object){const s=P.objectKeys(t),a=P.objectKeys(e).filter(o=>s.indexOf(o)!==-1),i={...e,...t};for(const o of a){const l=es(e[o],t[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}if(n===T.array&&r===T.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let a=0;a<e.length;a++){const i=es(e[a],t[a]);if(!i.valid)return{valid:!1};s.push(i.data)}return{valid:!0,data:s}}return n===T.date&&r===T.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Vn extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=(a,i)=>{if(fi(a)||fi(i))return C;const o=es(a.value,i.value);return o.valid?((gi(a)||gi(i))&&n.dirty(),{status:n.value,value:o.data}):(k(r,{code:E.invalid_intersection_types}),C)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([a,i])=>s(a,i)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}Vn.create=(e,t,n)=>new Vn({left:e,right:t,typeName:x.ZodIntersection,...R(n)});class at extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==T.array)return k(r,{code:E.invalid_type,expected:T.array,received:r.parsedType}),C;if(r.data.length<this._def.items.length)return k(r,{code:E.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),C;!this._def.rest&&r.data.length>this._def.items.length&&(k(r,{code:E.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const s=[...r.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new Ie(r,a,r.path,i)):null}).filter(a=>!!a);return r.common.async?Promise.all(s).then(a=>ee.mergeArray(n,a)):ee.mergeArray(n,s)}get items(){return this._def.items}rest(t){return new at({...this._def,rest:t})}}at.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new at({items:e,typeName:x.ZodTuple,rest:null,...R(t)})};class Hs extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==T.object)return k(r,{code:E.invalid_type,expected:T.object,received:r.parsedType}),C;const s=[],a=this._def.keyType,i=this._def.valueType;for(const o in r.data)s.push({key:a._parse(new Ie(r,o,r.path,o)),value:i._parse(new Ie(r,r.data[o],r.path,o)),alwaysSet:o in r.data});return r.common.async?ee.mergeObjectAsync(n,s):ee.mergeObjectSync(n,s)}get element(){return this._def.valueType}static create(t,n,r){return new Hs(n instanceof O?{keyType:t,valueType:n,typeName:x.ZodRecord,...R(r)}:{keyType:Fe.create(),valueType:t,typeName:x.ZodRecord,...R(n)})}}class Ei extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==T.map)return k(r,{code:E.invalid_type,expected:T.map,received:r.parsedType}),C;const s=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([o,l],u)=>({key:s._parse(new Ie(r,o,r.path,[u,"key"])),value:a._parse(new Ie(r,l,r.path,[u,"value"]))}));if(r.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const u=await l.key,p=await l.value;if(u.status==="aborted"||p.status==="aborted")return C;u.status!=="dirty"&&p.status!=="dirty"||n.dirty(),o.set(u.value,p.value)}return{status:n.value,value:o}})}{const o=new Map;for(const l of i){const u=l.key,p=l.value;if(u.status==="aborted"||p.status==="aborted")return C;u.status!=="dirty"&&p.status!=="dirty"||n.dirty(),o.set(u.value,p.value)}return{status:n.value,value:o}}}}Ei.create=(e,t,n)=>new Ei({valueType:t,keyType:e,typeName:x.ZodMap,...R(n)});class Wt extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==T.set)return k(r,{code:E.invalid_type,expected:T.set,received:r.parsedType}),C;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&(k(r,{code:E.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&(k(r,{code:E.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const a=this._def.valueType;function i(l){const u=new Set;for(const p of l){if(p.status==="aborted")return C;p.status==="dirty"&&n.dirty(),u.add(p.value)}return{status:n.value,value:u}}const o=[...r.data.values()].map((l,u)=>a._parse(new Ie(r,l,r.path,u)));return r.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(t,n){return new Wt({...this._def,minSize:{value:t,message:I.toString(n)}})}max(t,n){return new Wt({...this._def,maxSize:{value:t,message:I.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Wt.create=(e,t)=>new Wt({valueType:e,minSize:null,maxSize:null,typeName:x.ZodSet,...R(t)});class ts extends O{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}ts.create=(e,t)=>new ts({getter:e,typeName:x.ZodLazy,...R(t)});class Yn extends O{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return k(n,{received:n.data,code:E.invalid_literal,expected:this._def.value}),C}return{status:"valid",value:t.data}}get value(){return this._def.value}}function Xo(e,t){return new it({values:e,typeName:x.ZodEnum,...R(t)})}Yn.create=(e,t)=>new Yn({value:e,typeName:x.ZodLiteral,...R(t)});class it extends O{_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return k(n,{expected:P.joinValues(r),received:n.parsedType,code:E.invalid_type}),C}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return k(n,{received:n.data,code:E.invalid_enum_value,options:r}),C}return de(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return it.create(t,{...this._def,...n})}exclude(t,n=this._def){return it.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}it.create=Xo;class ns extends O{_parse(t){const n=P.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==T.string&&r.parsedType!==T.number){const s=P.objectValues(n);return k(r,{expected:P.joinValues(s),received:r.parsedType,code:E.invalid_type}),C}if(this._cache||(this._cache=new Set(P.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const s=P.objectValues(n);return k(r,{received:r.data,code:E.invalid_enum_value,options:s}),C}return de(t.data)}get enum(){return this._def.values}}ns.create=(e,t)=>new ns({values:e,typeName:x.ZodNativeEnum,...R(t)});class Kn extends O{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==T.promise&&n.common.async===!1)return k(n,{code:E.invalid_type,expected:T.promise,received:n.parsedType}),C;const r=n.parsedType===T.promise?n.data:Promise.resolve(n.data);return de(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}Kn.create=(e,t)=>new Kn({type:e,typeName:x.ZodPromise,...R(t)});class tt extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===x.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=this._def.effect||null,a={addIssue:i=>{k(r,i),i.fatal?n.abort():n.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),s.type==="preprocess"){const i=s.transform(r.data,a);if(r.common.async)return Promise.resolve(i).then(async o=>{if(n.value==="aborted")return C;const l=await this._def.schema._parseAsync({data:o,path:r.path,parent:r});return l.status==="aborted"?C:l.status==="dirty"||n.value==="dirty"?Xr(l.value):l});{if(n.value==="aborted")return C;const o=this._def.schema._parseSync({data:i,path:r.path,parent:r});return o.status==="aborted"?C:o.status==="dirty"||n.value==="dirty"?Xr(o.value):o}}if(s.type==="refinement"){const i=o=>{const l=s.refinement(o,a);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?C:(o.status==="dirty"&&n.dirty(),i(o.value),{status:n.value,value:o.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>o.status==="aborted"?C:(o.status==="dirty"&&n.dirty(),i(o.value).then(()=>({status:n.value,value:o.value}))))}if(s.type==="transform"){if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!kt(i))return C;const o=s.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:o}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>kt(i)?Promise.resolve(s.transform(i.value,a)).then(o=>({status:n.value,value:o})):C)}P.assertNever(s)}}tt.create=(e,t,n)=>new tt({schema:e,typeName:x.ZodEffects,effect:t,...R(n)}),tt.createWithPreprocess=(e,t,n)=>new tt({schema:t,effect:{type:"preprocess",transform:e},typeName:x.ZodEffects,...R(n)});class Ue extends O{_parse(t){return this._getType(t)===T.undefined?de(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Ue.create=(e,t)=>new Ue({innerType:e,typeName:x.ZodOptional,...R(t)});class ot extends O{_parse(t){return this._getType(t)===T.null?de(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}ot.create=(e,t)=>new ot({innerType:e,typeName:x.ZodNullable,...R(t)});class Hn extends O{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===T.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}Hn.create=(e,t)=>new Hn({innerType:e,typeName:x.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...R(t)});class Zn extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return $n(s)?s.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new $e(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new $e(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Zn.create=(e,t)=>new Zn({innerType:e,typeName:x.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...R(t)});class Si extends O{_parse(t){if(this._getType(t)!==T.nan){const n=this._getOrReturnCtx(t);return k(n,{code:E.invalid_type,expected:T.nan,received:n.parsedType}),C}return{status:"valid",value:t.data}}}Si.create=e=>new Si({typeName:x.ZodNaN,...R(e)});class Jo extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class Zs extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?C:s.status==="dirty"?(n.dirty(),Xr(s.value)):this._def.out._parseAsync({data:s.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?C:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(t,n){return new Zs({in:t,out:n,typeName:x.ZodPipeline})}}class Wn extends O{_parse(t){const n=this._def.innerType._parse(t),r=s=>(kt(s)&&(s.value=Object.freeze(s.value)),s);return $n(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}var x;Wn.create=(e,t)=>new Wn({innerType:e,typeName:x.ZodReadonly,...R(t)}),function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(x||(x={}));const sn=Fe.create,jh=Tt.create,Gh=Jr.create,qh=jn.create,Vh=Gn.create,Yh=Qr.create;ze.create;const Kh=we.create,an=q.create,Hh=qn.create,xd=Ks.create;Vn.create,at.create;const Zh=Hs.create,ki=Yn.create,Ti=it.create;Kn.create,Ue.create,ot.create;var Rd=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.webFetch="web-fetch",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(Rd||{}),Ad=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(Ad||{}),Od=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Od||{}),Pd=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(Pd||{}),Md=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(Md||{});an({"tool-name":sn().describe("Tool name to apply permission rule to. Can be any ToolType value (RemoteToolId, LocalToolType, SidecarToolType, or MCPToolType string)"),permission:xd("type",[an({type:Ti(["allow","deny","ask-user"])}),an({type:ki("webhook-policy"),"webhook-url":sn().describe("URL to send the request to")}),an({type:ki("script-policy"),script:sn().describe("Path to the script file to execute")})]),"event-type":Ti(["tool-call","tool-response"]).default("tool-call").optional(),"shell-input-regex":sn().optional()});var rs={exports:{}},ss={exports:{}},ce={},D={__esModule:!0};D.extend=wi,D.indexOf=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},D.escapeExpression=function(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return Fd.test(e)?e.replace(Dd,Ud):e},D.isEmpty=function(e){return!e&&e!==0||!(!Qo(e)||e.length!==0)},D.createFrame=function(e){var t=wi({},e);return t._parent=e,t},D.blockParams=function(e,t){return e.path=t,e},D.appendContextPath=function(e,t){return(e?e+".":"")+t};var Ld={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},Dd=/[&<>"'`=]/g,Fd=/[&<>"'`=]/;function Ud(e){return Ld[e]}function wi(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}var Ws=Object.prototype.toString;D.toString=Ws;var Sr=function(e){return typeof e=="function"};Sr(/x/)&&(D.isFunction=Sr=function(e){return typeof e=="function"&&Ws.call(e)==="[object Function]"}),D.isFunction=Sr;var Qo=Array.isArray||function(e){return!(!e||typeof e!="object")&&Ws.call(e)==="[object Array]"};D.isArray=Qo;var as={exports:{}};(function(e,t){t.__esModule=!0;var n=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function r(s,a){var i=a&&a.loc,o=void 0,l=void 0,u=void 0,p=void 0;i&&(o=i.start.line,l=i.end.line,u=i.start.column,p=i.end.column,s+=" - "+o+":"+u);for(var c=Error.prototype.constructor.call(this,s),d=0;d<n.length;d++)this[n[d]]=c[n[d]];Error.captureStackTrace&&Error.captureStackTrace(this,r);try{i&&(this.lineNumber=o,this.endLineNumber=l,Object.defineProperty?(Object.defineProperty(this,"column",{value:u,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:p,enumerable:!0})):(this.column=u,this.endColumn=p))}catch{}}r.prototype=new Error,t.default=r,e.exports=t.default})(as,as.exports);var fe=as.exports,Bt={},is={exports:{}};(function(e,t){t.__esModule=!0;var n=D;t.default=function(r){r.registerHelper("blockHelperMissing",function(s,a){var i=a.inverse,o=a.fn;if(s===!0)return o(this);if(s===!1||s==null)return i(this);if(n.isArray(s))return s.length>0?(a.ids&&(a.ids=[a.name]),r.helpers.each(s,a)):i(this);if(a.data&&a.ids){var l=n.createFrame(a.data);l.contextPath=n.appendContextPath(a.data.contextPath,a.name),a={data:l}}return o(s,a)})},e.exports=t.default})(is,is.exports);var $d=is.exports,os={exports:{}};(function(e,t){t.__esModule=!0;var n=D,r=function(s){return s&&s.__esModule?s:{default:s}}(fe);t.default=function(s){s.registerHelper("each",function(a,i){if(!i)throw new r.default("Must pass iterator to #each");var o,l=i.fn,u=i.inverse,p=0,c="",d=void 0,h=void 0;function m(b,N,A){d&&(d.key=b,d.index=N,d.first=N===0,d.last=!!A,h&&(d.contextPath=h+b)),c+=l(a[b],{data:d,blockParams:n.blockParams([a[b],b],[h+b,null])})}if(i.data&&i.ids&&(h=n.appendContextPath(i.data.contextPath,i.ids[0])+"."),n.isFunction(a)&&(a=a.call(this)),i.data&&(d=n.createFrame(i.data)),a&&typeof a=="object")if(n.isArray(a))for(var g=a.length;p<g;p++)p in a&&m(p,p,p===a.length-1);else if(typeof Symbol=="function"&&a[Symbol.iterator]){for(var f=[],_=a[Symbol.iterator](),S=_.next();!S.done;S=_.next())f.push(S.value);for(g=(a=f).length;p<g;p++)m(p,p,p===a.length-1)}else o=void 0,Object.keys(a).forEach(function(b){o!==void 0&&m(o,p-1),o=b,p++}),o!==void 0&&m(o,p-1,!0);return p===0&&(c=u(this)),c})},e.exports=t.default})(os,os.exports);var Bd=os.exports,us={exports:{}};(function(e,t){t.__esModule=!0;var n=function(r){return r&&r.__esModule?r:{default:r}}(fe);t.default=function(r){r.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new n.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t.default})(us,us.exports);var jd=us.exports,ls={exports:{}};(function(e,t){t.__esModule=!0;var n=D,r=function(s){return s&&s.__esModule?s:{default:s}}(fe);t.default=function(s){s.registerHelper("if",function(a,i){if(arguments.length!=2)throw new r.default("#if requires exactly one argument");return n.isFunction(a)&&(a=a.call(this)),!i.hash.includeZero&&!a||n.isEmpty(a)?i.inverse(this):i.fn(this)}),s.registerHelper("unless",function(a,i){if(arguments.length!=2)throw new r.default("#unless requires exactly one argument");return s.helpers.if.call(this,a,{fn:i.inverse,inverse:i.fn,hash:i.hash})})},e.exports=t.default})(ls,ls.exports);var Ii,kr,Gd=ls.exports,cs={exports:{}};Ii=cs,(kr=cs.exports).__esModule=!0,kr.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],n=arguments[arguments.length-1],r=0;r<arguments.length-1;r++)t.push(arguments[r]);var s=1;n.hash.level!=null?s=n.hash.level:n.data&&n.data.level!=null&&(s=n.data.level),t[0]=s,e.log.apply(e,t)})},Ii.exports=kr.default;var qd=cs.exports,ds={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){n.registerHelper("lookup",function(r,s,a){return r&&a.lookupProperty(r,s)})},e.exports=t.default})(ds,ds.exports);var Vd=ds.exports,ps={exports:{}};(function(e,t){t.__esModule=!0;var n=D,r=function(s){return s&&s.__esModule?s:{default:s}}(fe);t.default=function(s){s.registerHelper("with",function(a,i){if(arguments.length!=2)throw new r.default("#with requires exactly one argument");n.isFunction(a)&&(a=a.call(this));var o=i.fn;if(n.isEmpty(a))return i.inverse(this);var l=i.data;return i.data&&i.ids&&((l=n.createFrame(i.data)).contextPath=n.appendContextPath(i.data.contextPath,i.ids[0])),o(a,{data:l,blockParams:n.blockParams([a],[l&&l.contextPath])})})},e.exports=t.default})(ps,ps.exports);var Yd=ps.exports;function ut(e){return e&&e.__esModule?e:{default:e}}Bt.__esModule=!0,Bt.registerDefaultHelpers=function(e){Kd.default(e),Hd.default(e),Zd.default(e),Wd.default(e),zd.default(e),Xd.default(e),Jd.default(e)},Bt.moveHelperToHooks=function(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])};var Kd=ut($d),Hd=ut(Bd),Zd=ut(jd),Wd=ut(Gd),zd=ut(qd),Xd=ut(Vd),Jd=ut(Yd),hs={},ms={exports:{}};(function(e,t){t.__esModule=!0;var n=D;t.default=function(r){r.registerDecorator("inline",function(s,a,i,o){var l=s;return a.partials||(a.partials={},l=function(u,p){var c=i.partials;i.partials=n.extend({},c,a.partials);var d=s(u,p);return i.partials=c,d}),a.partials[o.args[0]]=o.fn,l})},e.exports=t.default})(ms,ms.exports);var Qd=ms.exports;hs.__esModule=!0,hs.registerDefaultDecorators=function(e){ep.default(e)};var ep=function(e){return e&&e.__esModule?e:{default:e}}(Qd),fs={exports:{}};(function(e,t){t.__esModule=!0;var n=D,r={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(s){if(typeof s=="string"){var a=n.indexOf(r.methodMap,s.toLowerCase());s=a>=0?a:parseInt(s,10)}return s},log:function(s){if(s=r.lookupLevel(s),typeof console<"u"&&r.lookupLevel(r.level)<=s){var a=r.methodMap[s];console[a]||(a="log");for(var i=arguments.length,o=Array(i>1?i-1:0),l=1;l<i;l++)o[l-1]=arguments[l];console[a].apply(console,o)}}};t.default=r,e.exports=t.default})(fs,fs.exports);var eu=fs.exports,yt={},tp={__esModule:!0,createNewLookupObject:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return np.extend.apply(void 0,[Object.create(null)].concat(t))}},np=D;yt.__esModule=!0,yt.createProtoAccessControl=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=Object.create(null);return n.__proto__=!1,{properties:{whitelist:Ni.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:Ni.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},yt.resultIsAllowed=function(e,t,n){return sp(typeof e=="function"?t.methods:t.properties,n)},yt.resetLoggedProperties=function(){Object.keys(zn).forEach(function(e){delete zn[e]})};var Ni=tp,rp=function(e){return e&&e.__esModule?e:{default:e}}(eu),zn=Object.create(null);function sp(e,t){return e.whitelist[t]!==void 0?e.whitelist[t]===!0:e.defaultValue!==void 0?e.defaultValue:(function(n){zn[n]!==!0&&(zn[n]=!0,rp.default.log("error",'Handlebars: Access has been denied to resolve the property "'+n+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}(t),!1)}function tu(e){return e&&e.__esModule?e:{default:e}}ce.__esModule=!0,ce.HandlebarsEnvironment=gs;var Je=D,Tr=tu(fe),ap=Bt,ip=hs,Xn=tu(eu),op=yt;ce.VERSION="4.7.8";ce.COMPILER_REVISION=8;ce.LAST_COMPATIBLE_COMPILER_REVISION=7;ce.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var wr="[object Object]";function gs(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},ap.registerDefaultHelpers(this),ip.registerDefaultDecorators(this)}gs.prototype={constructor:gs,logger:Xn.default,log:Xn.default.log,registerHelper:function(e,t){if(Je.toString.call(e)===wr){if(t)throw new Tr.default("Arg not supported with multiple helpers");Je.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(Je.toString.call(e)===wr)Je.extend(this.partials,e);else{if(t===void 0)throw new Tr.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(Je.toString.call(e)===wr){if(t)throw new Tr.default("Arg not supported with multiple decorators");Je.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){op.resetLoggedProperties()}};var up=Xn.default.log;ce.log=up,ce.createFrame=Je.createFrame,ce.logger=Xn.default;var ys={exports:{}};(function(e,t){function n(r){this.string=r}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t.default=n,e.exports=t.default})(ys,ys.exports);var lp=ys.exports,je={},vs={};vs.__esModule=!0,vs.wrapHelper=function(e,t){return typeof e!="function"?e:function(){return arguments[arguments.length-1]=t(arguments[arguments.length-1]),e.apply(this,arguments)}},je.__esModule=!0,je.checkRevision=function(e){var t=e&&e[0]||1,n=Re.COMPILER_REVISION;if(!(t>=Re.LAST_COMPATIBLE_COMPILER_REVISION&&t<=Re.COMPILER_REVISION)){if(t<Re.LAST_COMPATIBLE_COMPILER_REVISION){var r=Re.REVISION_CHANGES[n],s=Re.REVISION_CHANGES[t];throw new xe.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+r+") or downgrade your runtime to an older version ("+s+").")}throw new xe.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},je.template=function(e,t){if(!t)throw new xe.default("No environment passed to template");if(!e||!e.main)throw new xe.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var n=e.compiler&&e.compiler[0]===7,r={strict:function(a,i,o){if(!a||!(i in a))throw new xe.default('"'+i+'" not defined in '+a,{loc:o});return r.lookupProperty(a,i)},lookupProperty:function(a,i){var o=a[i];return o==null||Object.prototype.hasOwnProperty.call(a,i)||xi.resultIsAllowed(o,r.protoAccessControl,i)?o:void 0},lookup:function(a,i){for(var o=a.length,l=0;l<o;l++)if((a[l]&&r.lookupProperty(a[l],i))!=null)return a[l][i]},lambda:function(a,i){return typeof a=="function"?a.call(i):a},escapeExpression:Ae.escapeExpression,invokePartial:function(a,i,o){o.hash&&(i=Ae.extend({},i,o.hash),o.ids&&(o.ids[0]=!0)),a=t.VM.resolvePartial.call(this,a,i,o);var l=Ae.extend({},o,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),u=t.VM.invokePartial.call(this,a,i,l);if(u==null&&t.compile&&(o.partials[o.name]=t.compile(a,e.compilerOptions,t),u=o.partials[o.name](i,l)),u!=null){if(o.indent){for(var p=u.split(`
`),c=0,d=p.length;c<d&&(p[c]||c+1!==d);c++)p[c]=o.indent+p[c];u=p.join(`
`)}return u}throw new xe.default("The partial "+o.name+" could not be compiled when running in runtime-only mode")},fn:function(a){var i=e[a];return i.decorator=e[a+"_d"],i},programs:[],program:function(a,i,o,l,u){var p=this.programs[a],c=this.fn(a);return i||u||l||o?p=on(this,a,c,i,o,l,u):p||(p=this.programs[a]=on(this,a,c)),p},data:function(a,i){for(;a&&i--;)a=a._parent;return a},mergeIfNeeded:function(a,i){var o=a||i;return a&&i&&a!==i&&(o=Ae.extend({},i,a)),o},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function s(a){var i=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],o=i.data;s._setup(i),!i.partial&&e.useData&&(o=function(c,d){return d&&"root"in d||((d=d?Re.createFrame(d):{}).root=c),d}(a,o));var l=void 0,u=e.useBlockParams?[]:void 0;function p(c){return""+e.main(r,c,r.helpers,r.partials,o,u,l)}return e.useDepths&&(l=i.depths?a!=i.depths[0]?[a].concat(i.depths):i.depths:[a]),(p=nu(e.main,p,r,i.depths||[],o,u))(a,i)}return s.isTop=!0,s._setup=function(a){if(a.partial)r.protoAccessControl=a.protoAccessControl,r.helpers=a.helpers,r.partials=a.partials,r.decorators=a.decorators,r.hooks=a.hooks;else{var i=Ae.extend({},t.helpers,a.helpers);(function(l,u){Object.keys(l).forEach(function(p){var c=l[p];l[p]=function(d,h){var m=h.lookupProperty;return cp.wrapHelper(d,function(g){return Ae.extend({lookupProperty:m},g)})}(c,u)})})(i,r),r.helpers=i,e.usePartial&&(r.partials=r.mergeIfNeeded(a.partials,t.partials)),(e.usePartial||e.useDecorators)&&(r.decorators=Ae.extend({},t.decorators,a.decorators)),r.hooks={},r.protoAccessControl=xi.createProtoAccessControl(a);var o=a.allowCallsToHelperMissing||n;Ci.moveHelperToHooks(r,"helperMissing",o),Ci.moveHelperToHooks(r,"blockHelperMissing",o)}},s._child=function(a,i,o,l){if(e.useBlockParams&&!o)throw new xe.default("must pass block params");if(e.useDepths&&!l)throw new xe.default("must pass parent depths");return on(r,a,e[a],i,0,o,l)},s},je.wrapProgram=on,je.resolvePartial=function(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e=n.name==="@partial-block"?n.data["partial-block"]:n.partials[n.name],e},je.invokePartial=function(e,t,n){var r=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var s=void 0;if(n.fn&&n.fn!==Ri&&function(){n.data=Re.createFrame(n.data);var a=n.fn;s=n.data["partial-block"]=function(i){var o=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return o.data=Re.createFrame(o.data),o.data["partial-block"]=r,a(i,o)},a.partials&&(n.partials=Ae.extend({},n.partials,a.partials))}(),e===void 0&&s&&(e=s),e===void 0)throw new xe.default("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)},je.noop=Ri;var Ae=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(D),xe=function(e){return e&&e.__esModule?e:{default:e}}(fe),Re=ce,Ci=Bt,cp=vs,xi=yt;function on(e,t,n,r,s,a,i){function o(l){var u=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],p=i;return!i||l==i[0]||l===e.nullContext&&i[0]===null||(p=[l].concat(i)),n(e,l,e.helpers,e.partials,u.data||r,a&&[u.blockParams].concat(a),p)}return(o=nu(n,o,e,i,r,a)).program=t,o.depth=i?i.length:0,o.blockParams=s||0,o}function Ri(){return""}function nu(e,t,n,r,s,a){if(e.decorator){var i={};t=e.decorator(t,i,n,r&&r[0],s,a,r),Ae.extend(t,i)}return t}var _s={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var r=globalThis.Handlebars;n.noConflict=function(){return globalThis.Handlebars===n&&(globalThis.Handlebars=r),n}},e.exports=t.default})(_s,_s.exports);var ru=_s.exports;(function(e,t){function n(d){return d&&d.__esModule?d:{default:d}}function r(d){if(d&&d.__esModule)return d;var h={};if(d!=null)for(var m in d)Object.prototype.hasOwnProperty.call(d,m)&&(h[m]=d[m]);return h.default=d,h}t.__esModule=!0;var s=r(ce),a=n(lp),i=n(fe),o=r(D),l=r(je),u=n(ru);function p(){var d=new s.HandlebarsEnvironment;return o.extend(d,s),d.SafeString=a.default,d.Exception=i.default,d.Utils=o,d.escapeExpression=o.escapeExpression,d.VM=l,d.template=function(h){return l.template(h,d)},d}var c=p();c.create=p,u.default(c),c.default=c,t.default=c,e.exports=t.default})(ss,ss.exports);var dp=ss.exports,bs={exports:{}};(function(e,t){t.__esModule=!0;var n={helpers:{helperExpression:function(r){return r.type==="SubExpression"||(r.type==="MustacheStatement"||r.type==="BlockStatement")&&!!(r.params&&r.params.length||r.hash)},scopedId:function(r){return/^\.|this\b/.test(r.original)},simpleId:function(r){return r.parts.length===1&&!n.helpers.scopedId(r)&&!r.depth}}};t.default=n,e.exports=t.default})(bs,bs.exports);var su=bs.exports,jt={},Es={exports:{}};(function(e,t){t.__esModule=!0;var n=function(){var r={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(i,o,l,u,p,c,d){var h=c.length-1;switch(p){case 1:return c[h-1];case 2:this.$=u.prepareProgram(c[h]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=c[h];break;case 9:this.$={type:"CommentStatement",value:u.stripComment(c[h]),strip:u.stripFlags(c[h],c[h]),loc:u.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:c[h],value:c[h],loc:u.locInfo(this._$)};break;case 11:this.$=u.prepareRawBlock(c[h-2],c[h-1],c[h],this._$);break;case 12:this.$={path:c[h-3],params:c[h-2],hash:c[h-1]};break;case 13:this.$=u.prepareBlock(c[h-3],c[h-2],c[h-1],c[h],!1,this._$);break;case 14:this.$=u.prepareBlock(c[h-3],c[h-2],c[h-1],c[h],!0,this._$);break;case 15:this.$={open:c[h-5],path:c[h-4],params:c[h-3],hash:c[h-2],blockParams:c[h-1],strip:u.stripFlags(c[h-5],c[h])};break;case 16:case 17:this.$={path:c[h-4],params:c[h-3],hash:c[h-2],blockParams:c[h-1],strip:u.stripFlags(c[h-5],c[h])};break;case 18:this.$={strip:u.stripFlags(c[h-1],c[h-1]),program:c[h]};break;case 19:var m=u.prepareBlock(c[h-2],c[h-1],c[h],c[h],!1,this._$),g=u.prepareProgram([m],c[h-1].loc);g.chained=!0,this.$={strip:c[h-2].strip,program:g,chain:!0};break;case 21:this.$={path:c[h-1],strip:u.stripFlags(c[h-2],c[h])};break;case 22:case 23:this.$=u.prepareMustache(c[h-3],c[h-2],c[h-1],c[h-4],u.stripFlags(c[h-4],c[h]),this._$);break;case 24:this.$={type:"PartialStatement",name:c[h-3],params:c[h-2],hash:c[h-1],indent:"",strip:u.stripFlags(c[h-4],c[h]),loc:u.locInfo(this._$)};break;case 25:this.$=u.preparePartialBlock(c[h-2],c[h-1],c[h],this._$);break;case 26:this.$={path:c[h-3],params:c[h-2],hash:c[h-1],strip:u.stripFlags(c[h-4],c[h])};break;case 29:this.$={type:"SubExpression",path:c[h-3],params:c[h-2],hash:c[h-1],loc:u.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:c[h],loc:u.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:u.id(c[h-2]),value:c[h],loc:u.locInfo(this._$)};break;case 32:this.$=u.id(c[h-1]);break;case 35:this.$={type:"StringLiteral",value:c[h],original:c[h],loc:u.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(c[h]),original:Number(c[h]),loc:u.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:c[h]==="true",original:c[h]==="true",loc:u.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:u.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:u.locInfo(this._$)};break;case 42:this.$=u.preparePath(!0,c[h],this._$);break;case 43:this.$=u.preparePath(!1,c[h],this._$);break;case 44:c[h-2].push({part:u.id(c[h]),original:c[h],separator:c[h-1]}),this.$=c[h-2];break;case 45:this.$=[{part:u.id(c[h]),original:c[h]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:c[h-1].push(c[h]);break;case 98:case 100:this.$=[c[h]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(i,o){throw new Error(i)},parse:function(i){var o=this,l=[0],u=[null],p=[],c=this.table,d="",h=0,m=0;this.lexer.setInput(i),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var g=this.lexer.yylloc;p.push(g);var f=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);for(var _,S,b,N,A,L,M,ne,$,V={};;){if(S=l[l.length-1],this.defaultActions[S]?b=this.defaultActions[S]:(_==null&&($=void 0,typeof($=o.lexer.lex()||1)!="number"&&($=o.symbols_[$]||$),_=$),b=c[S]&&c[S][_]),b===void 0||!b.length||!b[0]){var ge="";for(A in ne=[],c[S])this.terminals_[A]&&A>2&&ne.push("'"+this.terminals_[A]+"'");ge=this.lexer.showPosition?"Parse error on line "+(h+1)+`:
`+this.lexer.showPosition()+`
Expecting `+ne.join(", ")+", got '"+(this.terminals_[_]||_)+"'":"Parse error on line "+(h+1)+": Unexpected "+(_==1?"end of input":"'"+(this.terminals_[_]||_)+"'"),this.parseError(ge,{text:this.lexer.match,token:this.terminals_[_]||_,line:this.lexer.yylineno,loc:g,expected:ne})}if(b[0]instanceof Array&&b.length>1)throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+_);switch(b[0]){case 1:l.push(_),u.push(this.lexer.yytext),p.push(this.lexer.yylloc),l.push(b[1]),_=null,m=this.lexer.yyleng,d=this.lexer.yytext,h=this.lexer.yylineno,g=this.lexer.yylloc;break;case 2:if(L=this.productions_[b[1]][1],V.$=u[u.length-L],V._$={first_line:p[p.length-(L||1)].first_line,last_line:p[p.length-1].last_line,first_column:p[p.length-(L||1)].first_column,last_column:p[p.length-1].last_column},f&&(V._$.range=[p[p.length-(L||1)].range[0],p[p.length-1].range[1]]),(N=this.performAction.call(V,d,m,h,this.yy,b[1],u,p))!==void 0)return N;L&&(l=l.slice(0,-1*L*2),u=u.slice(0,-1*L),p=p.slice(0,-1*L)),l.push(this.productions_[b[1]][0]),u.push(V.$),p.push(V._$),M=c[l[l.length-2]][l[l.length-1]],l.push(M);break;case 3:return!0}}return!0}},s=function(){var i={EOF:1,parseError:function(o,l){if(!this.yy.parser)throw new Error(o);this.yy.parser.parseError(o,l)},setInput:function(o){return this._input=o,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var o=this._input[0];return this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o,o.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},unput:function(o){var l=o.length,u=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l-1),this.offset-=l;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),u.length-1&&(this.yylineno-=u.length-1);var c=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:u?(u.length===p.length?this.yylloc.first_column:0)+p[p.length-u.length].length-u[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[c[0],c[0]+this.yyleng-l]),this},more:function(){return this._more=!0,this},less:function(o){this.unput(this.match.slice(o))},pastInput:function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var o=this.pastInput(),l=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+l+"^"},next:function(){if(this.done)return this.EOF;var o,l,u,p,c;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var d=this._currentRules(),h=0;h<d.length&&(!(u=this._input.match(this.rules[d[h]]))||l&&!(u[0].length>l[0].length)||(l=u,p=h,this.options.flex));h++);return l?((c=l[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],o=this.performAction.call(this,this.yy,this,d[p],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var o=this.next();return o!==void 0?o:this.lex()},begin:function(o){this.conditionStack.push(o)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(o){this.begin(o)},options:{},performAction:function(o,l,u,p){function c(d,h){return l.yytext=l.yytext.substring(d,l.yyleng-h+d)}switch(u){case 0:if(l.yytext.slice(-2)==="\\\\"?(c(0,1),this.begin("mu")):l.yytext.slice(-1)==="\\"?(c(0,1),this.begin("emu")):this.begin("mu"),l.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(c(5,9),"END_RAW_BLOCK");case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(l.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return l.yytext=c(1,2).replace(/\\"/g,'"'),80;case 32:return l.yytext=c(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return l.yytext=l.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};return i}();function a(){this.yy={}}return r.lexer=s,a.prototype=r,r.Parser=a,new a}();t.default=n,e.exports=t.default})(Es,Es.exports);var pp=Es.exports,Ss={exports:{}},ks={exports:{}};(function(e,t){t.__esModule=!0;var n=function(o){return o&&o.__esModule?o:{default:o}}(fe);function r(){this.parents=[]}function s(o){this.acceptRequired(o,"path"),this.acceptArray(o.params),this.acceptKey(o,"hash")}function a(o){s.call(this,o),this.acceptKey(o,"program"),this.acceptKey(o,"inverse")}function i(o){this.acceptRequired(o,"name"),this.acceptArray(o.params),this.acceptKey(o,"hash")}r.prototype={constructor:r,mutating:!1,acceptKey:function(o,l){var u=this.accept(o[l]);if(this.mutating){if(u&&!r.prototype[u.type])throw new n.default('Unexpected node type "'+u.type+'" found when accepting '+l+" on "+o.type);o[l]=u}},acceptRequired:function(o,l){if(this.acceptKey(o,l),!o[l])throw new n.default(o.type+" requires "+l)},acceptArray:function(o){for(var l=0,u=o.length;l<u;l++)this.acceptKey(o,l),o[l]||(o.splice(l,1),l--,u--)},accept:function(o){if(o){if(!this[o.type])throw new n.default("Unknown type: "+o.type,o);this.current&&this.parents.unshift(this.current),this.current=o;var l=this[o.type](o);return this.current=this.parents.shift(),!this.mutating||l?l:l!==!1?o:void 0}},Program:function(o){this.acceptArray(o.body)},MustacheStatement:s,Decorator:s,BlockStatement:a,DecoratorBlock:a,PartialStatement:i,PartialBlockStatement:function(o){i.call(this,o),this.acceptKey(o,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:s,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(o){this.acceptArray(o.pairs)},HashPair:function(o){this.acceptRequired(o,"value")}},t.default=r,e.exports=t.default})(ks,ks.exports);var au=ks.exports;(function(e,t){t.__esModule=!0;var n=function(l){return l&&l.__esModule?l:{default:l}}(au);function r(){var l=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=l}function s(l,u,p){u===void 0&&(u=l.length);var c=l[u-1],d=l[u-2];return c?c.type==="ContentStatement"?(d||!p?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(c.original):void 0:p}function a(l,u,p){u===void 0&&(u=-1);var c=l[u+1],d=l[u+2];return c?c.type==="ContentStatement"?(d||!p?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(c.original):void 0:p}function i(l,u,p){var c=l[u==null?0:u+1];if(c&&c.type==="ContentStatement"&&(p||!c.rightStripped)){var d=c.value;c.value=c.value.replace(p?/^\s+/:/^[ \t]*\r?\n?/,""),c.rightStripped=c.value!==d}}function o(l,u,p){var c=l[u==null?l.length-1:u-1];if(c&&c.type==="ContentStatement"&&(p||!c.leftStripped)){var d=c.value;return c.value=c.value.replace(p?/\s+$/:/[ \t]+$/,""),c.leftStripped=c.value!==d,c.leftStripped}}r.prototype=new n.default,r.prototype.Program=function(l){var u=!this.options.ignoreStandalone,p=!this.isRootSeen;this.isRootSeen=!0;for(var c=l.body,d=0,h=c.length;d<h;d++){var m=c[d],g=this.accept(m);if(g){var f=s(c,d,p),_=a(c,d,p),S=g.openStandalone&&f,b=g.closeStandalone&&_,N=g.inlineStandalone&&f&&_;g.close&&i(c,d,!0),g.open&&o(c,d,!0),u&&N&&(i(c,d),o(c,d)&&m.type==="PartialStatement"&&(m.indent=/([ \t]+$)/.exec(c[d-1].original)[1])),u&&S&&(i((m.program||m.inverse).body),o(c,d)),u&&b&&(i(c,d),o((m.inverse||m.program).body))}}return l},r.prototype.BlockStatement=r.prototype.DecoratorBlock=r.prototype.PartialBlockStatement=function(l){this.accept(l.program),this.accept(l.inverse);var u=l.program||l.inverse,p=l.program&&l.inverse,c=p,d=p;if(p&&p.chained)for(c=p.body[0].program;d.chained;)d=d.body[d.body.length-1].program;var h={open:l.openStrip.open,close:l.closeStrip.close,openStandalone:a(u.body),closeStandalone:s((c||u).body)};if(l.openStrip.close&&i(u.body,null,!0),p){var m=l.inverseStrip;m.open&&o(u.body,null,!0),m.close&&i(c.body,null,!0),l.closeStrip.open&&o(d.body,null,!0),!this.options.ignoreStandalone&&s(u.body)&&a(c.body)&&(o(u.body),i(c.body))}else l.closeStrip.open&&o(u.body,null,!0);return h},r.prototype.Decorator=r.prototype.MustacheStatement=function(l){return l.strip},r.prototype.PartialStatement=r.prototype.CommentStatement=function(l){var u=l.strip||{};return{inlineStandalone:!0,open:u.open,close:u.close}},t.default=r,e.exports=t.default})(Ss,Ss.exports);var hp=Ss.exports,oe={};oe.__esModule=!0,oe.SourceLocation=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},oe.id=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},oe.stripFlags=function(e,t){return{open:e.charAt(2)==="~",close:t.charAt(t.length-3)==="~"}},oe.stripComment=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},oe.preparePath=function(e,t,n){n=this.locInfo(n);for(var r=e?"@":"",s=[],a=0,i=0,o=t.length;i<o;i++){var l=t[i].part,u=t[i].original!==l;if(r+=(t[i].separator||"")+l,u||l!==".."&&l!=="."&&l!=="this")s.push(l);else{if(s.length>0)throw new Ts.default("Invalid path: "+r,{loc:n});l===".."&&a++}}return{type:"PathExpression",data:e,depth:a,parts:s,original:r,loc:n}},oe.prepareMustache=function(e,t,n,r,s,a){var i=r.charAt(3)||r.charAt(2),o=i!=="{"&&i!=="&";return{type:/\*/.test(r)?"Decorator":"MustacheStatement",path:e,params:t,hash:n,escaped:o,strip:s,loc:this.locInfo(a)}},oe.prepareRawBlock=function(e,t,n,r){Ir(e,n),r=this.locInfo(r);var s={type:"Program",body:t,strip:{},loc:r};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:s,openStrip:{},inverseStrip:{},closeStrip:{},loc:r}},oe.prepareBlock=function(e,t,n,r,s,a){r&&r.path&&Ir(e,r);var i=/\*/.test(e.open);t.blockParams=e.blockParams;var o=void 0,l=void 0;if(n){if(i)throw new Ts.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=r.strip),l=n.strip,o=n.program}return s&&(s=o,o=t,t=s),{type:i?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:o,openStrip:e.strip,inverseStrip:l,closeStrip:r&&r.strip,loc:this.locInfo(a)}},oe.prepareProgram=function(e,t){if(!t&&e.length){var n=e[0].loc,r=e[e.length-1].loc;n&&r&&(t={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:r.end.line,column:r.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},oe.preparePartialBlock=function(e,t,n,r){return Ir(e,n),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:n&&n.strip,loc:this.locInfo(r)}};var Ts=function(e){return e&&e.__esModule?e:{default:e}}(fe);function Ir(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var n={loc:e.path.loc};throw new Ts.default(e.path.original+" doesn't match "+t,n)}}function iu(e){return e&&e.__esModule?e:{default:e}}jt.__esModule=!0,jt.parseWithoutProcessing=Ai,jt.parse=function(e,t){var n=Ai(e,t);return new mp.default(t).accept(n)};var ws=iu(pp),mp=iu(hp),fp=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(oe),gp=D;jt.parser=ws.default;var vn={};function Ai(e,t){return e.type==="Program"?e:(ws.default.yy=vn,vn.locInfo=function(n){return new vn.SourceLocation(t&&t.srcName,n)},ws.default.parse(e))}gp.extend(vn,fp);var Pt={};function ou(e){return e&&e.__esModule?e:{default:e}}Pt.__esModule=!0,Pt.Compiler=Is,Pt.precompile=function(e,t,n){if(e==null||typeof e!="string"&&e.type!=="Program")throw new Gt.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var r=n.parse(e,t),s=new n.Compiler().compile(r,t);return new n.JavaScriptCompiler().compile(s,t)},Pt.compile=function(e,t,n){if(t===void 0&&(t={}),e==null||typeof e!="string"&&e.type!=="Program")throw new Gt.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=zt.extend({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var r=void 0;function s(){var i=n.parse(e,t),o=new n.Compiler().compile(i,t),l=new n.JavaScriptCompiler().compile(o,t,void 0,!0);return n.template(l)}function a(i,o){return r||(r=s()),r.call(this,i,o)}return a._setup=function(i){return r||(r=s()),r._setup(i)},a._child=function(i,o,l,u){return r||(r=s()),r._child(i,o,l,u)},a};var Gt=ou(fe),zt=D,Ct=ou(su),yp=[].slice;function Is(){}function uu(e,t){if(e===t)return!0;if(zt.isArray(e)&&zt.isArray(t)&&e.length===t.length){for(var n=0;n<e.length;n++)if(!uu(e[n],t[n]))return!1;return!0}}function Oi(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}Is.prototype={compiler:Is,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var r=this.opcodes[n],s=e.opcodes[n];if(r.opcode!==s.opcode||!uu(r.args,s.args))return!1}for(t=this.children.length,n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=zt.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),n=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[n]=t,this.useDepths=this.useDepths||t.useDepths,n},accept:function(e){if(!this[e.type])throw new Gt.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,r=0;r<n;r++)this.accept(t[r]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){Oi(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var r=this.classifySexpr(e);r==="helper"?this.helperSexpr(e,t,n):r==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),r=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,r.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new Gt.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var r=e.name.original,s=e.name.type==="SubExpression";s&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var a=e.indent||"";this.options.preventIndent&&a&&(this.opcode("appendContent",a),a=""),this.opcode("invokePartial",s,r,a),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){Oi(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var r=e.path,s=r.parts[0],a=t!=null||n!=null;this.opcode("getContext",r.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),r.strict=!0,this.accept(r),this.opcode("invokeAmbiguous",s,a)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var r=this.setupFullMustacheParams(e,t,n),s=e.path,a=s.parts[0];if(this.options.knownHelpers[a])this.opcode("invokeKnownHelper",r.length,a);else{if(this.options.knownHelpersOnly)throw new Gt.default("You specified knownHelpersOnly, but used the unknown helper "+a,e);s.strict=!0,s.falsy=!0,this.accept(s),this.opcode("invokeHelper",r.length,s.original,Ct.default.helpers.simpleId(s))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=Ct.default.helpers.scopedId(e),r=!e.depth&&!n&&this.blockParamIndex(t);r?this.opcode("lookupBlockParam",r,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,r=t.length;for(this.opcode("pushHash");n<r;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:yp.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=Ct.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),r=!n&&Ct.default.helpers.helperExpression(e),s=!n&&(r||t);if(s&&!r){var a=e.path.parts[0],i=this.options;i.knownHelpers[a]?r=!0:i.knownHelpersOnly&&(s=!1)}return r?"helper":s?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(!e.parts||Ct.default.helpers.scopedId(e)||e.depth||(n=this.blockParamIndex(e.parts[0])),n){var r=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,r)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,r){var s=e.params;return this.pushParams(s),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",r),s},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var r=this.options.blockParams[t],s=r&&zt.indexOf(r,e);if(r&&s>=0)return[t,s]}}};var Pi,Mi,Ns={exports:{}},Cs={exports:{}},un={},Nr={},ln={},cn={};function vp(){if(Pi)return cn;Pi=1;var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");return cn.encode=function(t){if(0<=t&&t<e.length)return e[t];throw new TypeError("Must be between 0 and 63: "+t)},cn.decode=function(t){return 65<=t&&t<=90?t-65:97<=t&&t<=122?t-97+26:48<=t&&t<=57?t-48+52:t==43?62:t==47?63:-1},cn}function lu(){if(Mi)return ln;Mi=1;var e=vp();return ln.encode=function(t){var n,r="",s=function(a){return a<0?1+(-a<<1):0+(a<<1)}(t);do n=31&s,(s>>>=5)>0&&(n|=32),r+=e.encode(n);while(s>0);return r},ln.decode=function(t,n,r){var s,a,i,o,l=t.length,u=0,p=0;do{if(n>=l)throw new Error("Expected more digits in base 64 VLQ value.");if((a=e.decode(t.charCodeAt(n++)))===-1)throw new Error("Invalid base64 digit: "+t.charAt(n-1));s=!!(32&a),u+=(a&=31)<<p,p+=5}while(s);r.value=(o=(i=u)>>1,1&~i?o:-o),r.rest=n},ln}var Li,Di={};function Xt(){return Li||(Li=1,function(e){e.getArg=function(c,d,h){if(d in c)return c[d];if(arguments.length===3)return h;throw new Error('"'+d+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function r(c){var d=c.match(t);return d?{scheme:d[1],auth:d[2],host:d[3],port:d[4],path:d[5]}:null}function s(c){var d="";return c.scheme&&(d+=c.scheme+":"),d+="//",c.auth&&(d+=c.auth+"@"),c.host&&(d+=c.host),c.port&&(d+=":"+c.port),c.path&&(d+=c.path),d}function a(c){var d=c,h=r(c);if(h){if(!h.path)return c;d=h.path}for(var m,g=e.isAbsolute(d),f=d.split(/\/+/),_=0,S=f.length-1;S>=0;S--)(m=f[S])==="."?f.splice(S,1):m===".."?_++:_>0&&(m===""?(f.splice(S+1,_),_=0):(f.splice(S,2),_--));return(d=f.join("/"))===""&&(d=g?"/":"."),h?(h.path=d,s(h)):d}function i(c,d){c===""&&(c="."),d===""&&(d=".");var h=r(d),m=r(c);if(m&&(c=m.path||"/"),h&&!h.scheme)return m&&(h.scheme=m.scheme),s(h);if(h||d.match(n))return d;if(m&&!m.host&&!m.path)return m.host=d,s(m);var g=d.charAt(0)==="/"?d:a(c.replace(/\/+$/,"")+"/"+d);return m?(m.path=g,s(m)):g}e.urlParse=r,e.urlGenerate=s,e.normalize=a,e.join=i,e.isAbsolute=function(c){return c.charAt(0)==="/"||t.test(c)},e.relative=function(c,d){c===""&&(c="."),c=c.replace(/\/$/,"");for(var h=0;d.indexOf(c+"/")!==0;){var m=c.lastIndexOf("/");if(m<0||(c=c.slice(0,m)).match(/^([^\/]+:\/)?\/*$/))return d;++h}return Array(h+1).join("../")+d.substr(c.length+1)};var o=!("__proto__"in Object.create(null));function l(c){return c}function u(c){if(!c)return!1;var d=c.length;if(d<9||c.charCodeAt(d-1)!==95||c.charCodeAt(d-2)!==95||c.charCodeAt(d-3)!==111||c.charCodeAt(d-4)!==116||c.charCodeAt(d-5)!==111||c.charCodeAt(d-6)!==114||c.charCodeAt(d-7)!==112||c.charCodeAt(d-8)!==95||c.charCodeAt(d-9)!==95)return!1;for(var h=d-10;h>=0;h--)if(c.charCodeAt(h)!==36)return!1;return!0}function p(c,d){return c===d?0:c===null?1:d===null?-1:c>d?1:-1}e.toSetString=o?l:function(c){return u(c)?"$"+c:c},e.fromSetString=o?l:function(c){return u(c)?c.slice(1):c},e.compareByOriginalPositions=function(c,d,h){var m=p(c.source,d.source);return m!==0||(m=c.originalLine-d.originalLine)!==0||(m=c.originalColumn-d.originalColumn)!==0||h||(m=c.generatedColumn-d.generatedColumn)!==0||(m=c.generatedLine-d.generatedLine)!==0?m:p(c.name,d.name)},e.compareByGeneratedPositionsDeflated=function(c,d,h){var m=c.generatedLine-d.generatedLine;return m!==0||(m=c.generatedColumn-d.generatedColumn)!==0||h||(m=p(c.source,d.source))!==0||(m=c.originalLine-d.originalLine)!==0||(m=c.originalColumn-d.originalColumn)!==0?m:p(c.name,d.name)},e.compareByGeneratedPositionsInflated=function(c,d){var h=c.generatedLine-d.generatedLine;return h!==0||(h=c.generatedColumn-d.generatedColumn)!==0||(h=p(c.source,d.source))!==0||(h=c.originalLine-d.originalLine)!==0||(h=c.originalColumn-d.originalColumn)!==0?h:p(c.name,d.name)},e.parseSourceMapInput=function(c){return JSON.parse(c.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(c,d,h){if(d=d||"",c&&(c[c.length-1]!=="/"&&d[0]!=="/"&&(c+="/"),d=c+d),h){var m=r(h);if(!m)throw new Error("sourceMapURL could not be parsed");if(m.path){var g=m.path.lastIndexOf("/");g>=0&&(m.path=m.path.substring(0,g+1))}d=i(s(m),d)}return a(d)}}(Di)),Di}var Fi,Cr={};function cu(){if(Fi)return Cr;Fi=1;var e=Xt(),t=Object.prototype.hasOwnProperty,n=typeof Map<"u";function r(){this._array=[],this._set=n?new Map:Object.create(null)}return r.fromArray=function(s,a){for(var i=new r,o=0,l=s.length;o<l;o++)i.add(s[o],a);return i},r.prototype.size=function(){return n?this._set.size:Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(s,a){var i=n?s:e.toSetString(s),o=n?this.has(s):t.call(this._set,i),l=this._array.length;o&&!a||this._array.push(s),o||(n?this._set.set(s,l):this._set[i]=l)},r.prototype.has=function(s){if(n)return this._set.has(s);var a=e.toSetString(s);return t.call(this._set,a)},r.prototype.indexOf=function(s){if(n){var a=this._set.get(s);if(a>=0)return a}else{var i=e.toSetString(s);if(t.call(this._set,i))return this._set[i]}throw new Error('"'+s+'" is not in the set.')},r.prototype.at=function(s){if(s>=0&&s<this._array.length)return this._array[s];throw new Error("No element indexed by "+s)},r.prototype.toArray=function(){return this._array.slice()},Cr.ArraySet=r,Cr}var Ui,$i,xr={};function _p(){if(Ui)return xr;Ui=1;var e=Xt();function t(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}return t.prototype.unsortedForEach=function(n,r){this._array.forEach(n,r)},t.prototype.add=function(n){var r,s,a,i,o,l;r=this._last,s=n,a=r.generatedLine,i=s.generatedLine,o=r.generatedColumn,l=s.generatedColumn,i>a||i==a&&l>=o||e.compareByGeneratedPositionsInflated(r,s)<=0?(this._last=n,this._array.push(n)):(this._sorted=!1,this._array.push(n))},t.prototype.toArray=function(){return this._sorted||(this._array.sort(e.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},xr.MappingList=t,xr}function Bi(){if($i)return Nr;$i=1;var e=lu(),t=Xt(),n=cu().ArraySet,r=_p().MappingList;function s(a){a||(a={}),this._file=t.getArg(a,"file",null),this._sourceRoot=t.getArg(a,"sourceRoot",null),this._skipValidation=t.getArg(a,"skipValidation",!1),this._sources=new n,this._names=new n,this._mappings=new r,this._sourcesContents=null}return s.prototype._version=3,s.fromSourceMap=function(a){var i=a.sourceRoot,o=new s({file:a.file,sourceRoot:i});return a.eachMapping(function(l){var u={generated:{line:l.generatedLine,column:l.generatedColumn}};l.source!=null&&(u.source=l.source,i!=null&&(u.source=t.relative(i,u.source)),u.original={line:l.originalLine,column:l.originalColumn},l.name!=null&&(u.name=l.name)),o.addMapping(u)}),a.sources.forEach(function(l){var u=l;i!==null&&(u=t.relative(i,l)),o._sources.has(u)||o._sources.add(u);var p=a.sourceContentFor(l);p!=null&&o.setSourceContent(l,p)}),o},s.prototype.addMapping=function(a){var i=t.getArg(a,"generated"),o=t.getArg(a,"original",null),l=t.getArg(a,"source",null),u=t.getArg(a,"name",null);this._skipValidation||this._validateMapping(i,o,l,u),l!=null&&(l=String(l),this._sources.has(l)||this._sources.add(l)),u!=null&&(u=String(u),this._names.has(u)||this._names.add(u)),this._mappings.add({generatedLine:i.line,generatedColumn:i.column,originalLine:o!=null&&o.line,originalColumn:o!=null&&o.column,source:l,name:u})},s.prototype.setSourceContent=function(a,i){var o=a;this._sourceRoot!=null&&(o=t.relative(this._sourceRoot,o)),i!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[t.toSetString(o)]=i):this._sourcesContents&&(delete this._sourcesContents[t.toSetString(o)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))},s.prototype.applySourceMap=function(a,i,o){var l=i;if(i==null){if(a.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);l=a.file}var u=this._sourceRoot;u!=null&&(l=t.relative(u,l));var p=new n,c=new n;this._mappings.unsortedForEach(function(d){if(d.source===l&&d.originalLine!=null){var h=a.originalPositionFor({line:d.originalLine,column:d.originalColumn});h.source!=null&&(d.source=h.source,o!=null&&(d.source=t.join(o,d.source)),u!=null&&(d.source=t.relative(u,d.source)),d.originalLine=h.line,d.originalColumn=h.column,h.name!=null&&(d.name=h.name))}var m=d.source;m==null||p.has(m)||p.add(m);var g=d.name;g==null||c.has(g)||c.add(g)},this),this._sources=p,this._names=c,a.sources.forEach(function(d){var h=a.sourceContentFor(d);h!=null&&(o!=null&&(d=t.join(o,d)),u!=null&&(d=t.relative(u,d)),this.setSourceContent(d,h))},this)},s.prototype._validateMapping=function(a,i,o,l){if(i&&typeof i.line!="number"&&typeof i.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(a&&"line"in a&&"column"in a&&a.line>0&&a.column>=0)||i||o||l)&&!(a&&"line"in a&&"column"in a&&i&&"line"in i&&"column"in i&&a.line>0&&a.column>=0&&i.line>0&&i.column>=0&&o))throw new Error("Invalid mapping: "+JSON.stringify({generated:a,source:o,original:i,name:l}))},s.prototype._serializeMappings=function(){for(var a,i,o,l,u=0,p=1,c=0,d=0,h=0,m=0,g="",f=this._mappings.toArray(),_=0,S=f.length;_<S;_++){if(a="",(i=f[_]).generatedLine!==p)for(u=0;i.generatedLine!==p;)a+=";",p++;else if(_>0){if(!t.compareByGeneratedPositionsInflated(i,f[_-1]))continue;a+=","}a+=e.encode(i.generatedColumn-u),u=i.generatedColumn,i.source!=null&&(l=this._sources.indexOf(i.source),a+=e.encode(l-m),m=l,a+=e.encode(i.originalLine-1-d),d=i.originalLine-1,a+=e.encode(i.originalColumn-c),c=i.originalColumn,i.name!=null&&(o=this._names.indexOf(i.name),a+=e.encode(o-h),h=o)),g+=a}return g},s.prototype._generateSourcesContent=function(a,i){return a.map(function(o){if(!this._sourcesContents)return null;i!=null&&(o=t.relative(i,o));var l=t.toSetString(o);return Object.prototype.hasOwnProperty.call(this._sourcesContents,l)?this._sourcesContents[l]:null},this)},s.prototype.toJSON=function(){var a={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(a.file=this._file),this._sourceRoot!=null&&(a.sourceRoot=this._sourceRoot),this._sourcesContents&&(a.sourcesContent=this._generateSourcesContent(a.sources,a.sourceRoot)),a},s.prototype.toString=function(){return JSON.stringify(this.toJSON())},Nr.SourceMapGenerator=s,Nr}var ji,xt={},Gi={};function bp(){return ji||(ji=1,function(e){function t(n,r,s,a,i,o){var l=Math.floor((r-n)/2)+n,u=i(s,a[l],!0);return u===0?l:u>0?r-l>1?t(l,r,s,a,i,o):o==e.LEAST_UPPER_BOUND?r<a.length?r:-1:l:l-n>1?t(n,l,s,a,i,o):o==e.LEAST_UPPER_BOUND?l:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,r,s,a){if(r.length===0)return-1;var i=t(-1,r.length,n,r,s,a||e.GREATEST_LOWER_BOUND);if(i<0)return-1;for(;i-1>=0&&s(r[i],r[i-1],!0)===0;)--i;return i}}(Gi)),Gi}var qi,Vi,Rr={};function Ep(){if(qi)return Rr;function e(n,r,s){var a=n[r];n[r]=n[s],n[s]=a}function t(n,r,s,a){if(s<a){var i=s-1;e(n,(p=s,c=a,Math.round(p+Math.random()*(c-p))),a);for(var o=n[a],l=s;l<a;l++)r(n[l],o)<=0&&e(n,i+=1,l);e(n,i+1,l);var u=i+1;t(n,r,s,u-1),t(n,r,u+1,a)}var p,c}return qi=1,Rr.quickSort=function(n,r){t(n,r,0,n.length-1)},Rr}var Yi,Ki,Ar={};function Sp(){return Ki||(Ki=1,un.SourceMapGenerator=Bi().SourceMapGenerator,un.SourceMapConsumer=function(){if(Vi)return xt;Vi=1;var e=Xt(),t=bp(),n=cu().ArraySet,r=lu(),s=Ep().quickSort;function a(u,p){var c=u;return typeof u=="string"&&(c=e.parseSourceMapInput(u)),c.sections!=null?new l(c,p):new i(c,p)}function i(u,p){var c=u;typeof u=="string"&&(c=e.parseSourceMapInput(u));var d=e.getArg(c,"version"),h=e.getArg(c,"sources"),m=e.getArg(c,"names",[]),g=e.getArg(c,"sourceRoot",null),f=e.getArg(c,"sourcesContent",null),_=e.getArg(c,"mappings"),S=e.getArg(c,"file",null);if(d!=this._version)throw new Error("Unsupported version: "+d);g&&(g=e.normalize(g)),h=h.map(String).map(e.normalize).map(function(b){return g&&e.isAbsolute(g)&&e.isAbsolute(b)?e.relative(g,b):b}),this._names=n.fromArray(m.map(String),!0),this._sources=n.fromArray(h,!0),this._absoluteSources=this._sources.toArray().map(function(b){return e.computeSourceURL(g,b,p)}),this.sourceRoot=g,this.sourcesContent=f,this._mappings=_,this._sourceMapURL=p,this.file=S}function o(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function l(u,p){var c=u;typeof u=="string"&&(c=e.parseSourceMapInput(u));var d=e.getArg(c,"version"),h=e.getArg(c,"sections");if(d!=this._version)throw new Error("Unsupported version: "+d);this._sources=new n,this._names=new n;var m={line:-1,column:0};this._sections=h.map(function(g){if(g.url)throw new Error("Support for url field in sections not implemented.");var f=e.getArg(g,"offset"),_=e.getArg(f,"line"),S=e.getArg(f,"column");if(_<m.line||_===m.line&&S<m.column)throw new Error("Section offsets must be ordered and non-overlapping.");return m=f,{generatedOffset:{generatedLine:_+1,generatedColumn:S+1},consumer:new a(e.getArg(g,"map"),p)}})}return a.fromSourceMap=function(u,p){return i.fromSourceMap(u,p)},a.prototype._version=3,a.prototype.__generatedMappings=null,Object.defineProperty(a.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),a.prototype.__originalMappings=null,Object.defineProperty(a.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),a.prototype._charIsMappingSeparator=function(u,p){var c=u.charAt(p);return c===";"||c===","},a.prototype._parseMappings=function(u,p){throw new Error("Subclasses must implement _parseMappings")},a.GENERATED_ORDER=1,a.ORIGINAL_ORDER=2,a.GREATEST_LOWER_BOUND=1,a.LEAST_UPPER_BOUND=2,a.prototype.eachMapping=function(u,p,c){var d,h=p||null;switch(c||a.GENERATED_ORDER){case a.GENERATED_ORDER:d=this._generatedMappings;break;case a.ORIGINAL_ORDER:d=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var m=this.sourceRoot;d.map(function(g){var f=g.source===null?null:this._sources.at(g.source);return{source:f=e.computeSourceURL(m,f,this._sourceMapURL),generatedLine:g.generatedLine,generatedColumn:g.generatedColumn,originalLine:g.originalLine,originalColumn:g.originalColumn,name:g.name===null?null:this._names.at(g.name)}},this).forEach(u,h)},a.prototype.allGeneratedPositionsFor=function(u){var p=e.getArg(u,"line"),c={source:e.getArg(u,"source"),originalLine:p,originalColumn:e.getArg(u,"column",0)};if(c.source=this._findSourceIndex(c.source),c.source<0)return[];var d=[],h=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,t.LEAST_UPPER_BOUND);if(h>=0){var m=this._originalMappings[h];if(u.column===void 0)for(var g=m.originalLine;m&&m.originalLine===g;)d.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h];else for(var f=m.originalColumn;m&&m.originalLine===p&&m.originalColumn==f;)d.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h]}return d},xt.SourceMapConsumer=a,i.prototype=Object.create(a.prototype),i.prototype.consumer=a,i.prototype._findSourceIndex=function(u){var p,c=u;if(this.sourceRoot!=null&&(c=e.relative(this.sourceRoot,c)),this._sources.has(c))return this._sources.indexOf(c);for(p=0;p<this._absoluteSources.length;++p)if(this._absoluteSources[p]==u)return p;return-1},i.fromSourceMap=function(u,p){var c=Object.create(i.prototype),d=c._names=n.fromArray(u._names.toArray(),!0),h=c._sources=n.fromArray(u._sources.toArray(),!0);c.sourceRoot=u._sourceRoot,c.sourcesContent=u._generateSourcesContent(c._sources.toArray(),c.sourceRoot),c.file=u._file,c._sourceMapURL=p,c._absoluteSources=c._sources.toArray().map(function(A){return e.computeSourceURL(c.sourceRoot,A,p)});for(var m=u._mappings.toArray().slice(),g=c.__generatedMappings=[],f=c.__originalMappings=[],_=0,S=m.length;_<S;_++){var b=m[_],N=new o;N.generatedLine=b.generatedLine,N.generatedColumn=b.generatedColumn,b.source&&(N.source=h.indexOf(b.source),N.originalLine=b.originalLine,N.originalColumn=b.originalColumn,b.name&&(N.name=d.indexOf(b.name)),f.push(N)),g.push(N)}return s(c.__originalMappings,e.compareByOriginalPositions),c},i.prototype._version=3,Object.defineProperty(i.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),i.prototype._parseMappings=function(u,p){for(var c,d,h,m,g,f=1,_=0,S=0,b=0,N=0,A=0,L=u.length,M=0,ne={},$={},V=[],ge=[];M<L;)if(u.charAt(M)===";")f++,M++,_=0;else if(u.charAt(M)===",")M++;else{for((c=new o).generatedLine=f,m=M;m<L&&!this._charIsMappingSeparator(u,m);m++);if(h=ne[d=u.slice(M,m)])M+=d.length;else{for(h=[];M<m;)r.decode(u,M,$),g=$.value,M=$.rest,h.push(g);if(h.length===2)throw new Error("Found a source, but no line and column");if(h.length===3)throw new Error("Found a source and line, but no column");ne[d]=h}c.generatedColumn=_+h[0],_=c.generatedColumn,h.length>1&&(c.source=N+h[1],N+=h[1],c.originalLine=S+h[2],S=c.originalLine,c.originalLine+=1,c.originalColumn=b+h[3],b=c.originalColumn,h.length>4&&(c.name=A+h[4],A+=h[4])),ge.push(c),typeof c.originalLine=="number"&&V.push(c)}s(ge,e.compareByGeneratedPositionsDeflated),this.__generatedMappings=ge,s(V,e.compareByOriginalPositions),this.__originalMappings=V},i.prototype._findMapping=function(u,p,c,d,h,m){if(u[c]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+u[c]);if(u[d]<0)throw new TypeError("Column must be greater than or equal to 0, got "+u[d]);return t.search(u,p,h,m)},i.prototype.computeColumnSpans=function(){for(var u=0;u<this._generatedMappings.length;++u){var p=this._generatedMappings[u];if(u+1<this._generatedMappings.length){var c=this._generatedMappings[u+1];if(p.generatedLine===c.generatedLine){p.lastGeneratedColumn=c.generatedColumn-1;continue}}p.lastGeneratedColumn=1/0}},i.prototype.originalPositionFor=function(u){var p={generatedLine:e.getArg(u,"line"),generatedColumn:e.getArg(u,"column")},c=this._findMapping(p,this._generatedMappings,"generatedLine","generatedColumn",e.compareByGeneratedPositionsDeflated,e.getArg(u,"bias",a.GREATEST_LOWER_BOUND));if(c>=0){var d=this._generatedMappings[c];if(d.generatedLine===p.generatedLine){var h=e.getArg(d,"source",null);h!==null&&(h=this._sources.at(h),h=e.computeSourceURL(this.sourceRoot,h,this._sourceMapURL));var m=e.getArg(d,"name",null);return m!==null&&(m=this._names.at(m)),{source:h,line:e.getArg(d,"originalLine",null),column:e.getArg(d,"originalColumn",null),name:m}}}return{source:null,line:null,column:null,name:null}},i.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(u){return u==null})},i.prototype.sourceContentFor=function(u,p){if(!this.sourcesContent)return null;var c=this._findSourceIndex(u);if(c>=0)return this.sourcesContent[c];var d,h=u;if(this.sourceRoot!=null&&(h=e.relative(this.sourceRoot,h)),this.sourceRoot!=null&&(d=e.urlParse(this.sourceRoot))){var m=h.replace(/^file:\/\//,"");if(d.scheme=="file"&&this._sources.has(m))return this.sourcesContent[this._sources.indexOf(m)];if((!d.path||d.path=="/")&&this._sources.has("/"+h))return this.sourcesContent[this._sources.indexOf("/"+h)]}if(p)return null;throw new Error('"'+h+'" is not in the SourceMap.')},i.prototype.generatedPositionFor=function(u){var p=e.getArg(u,"source");if((p=this._findSourceIndex(p))<0)return{line:null,column:null,lastColumn:null};var c={source:p,originalLine:e.getArg(u,"line"),originalColumn:e.getArg(u,"column")},d=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,e.getArg(u,"bias",a.GREATEST_LOWER_BOUND));if(d>=0){var h=this._originalMappings[d];if(h.source===c.source)return{line:e.getArg(h,"generatedLine",null),column:e.getArg(h,"generatedColumn",null),lastColumn:e.getArg(h,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},xt.BasicSourceMapConsumer=i,l.prototype=Object.create(a.prototype),l.prototype.constructor=a,l.prototype._version=3,Object.defineProperty(l.prototype,"sources",{get:function(){for(var u=[],p=0;p<this._sections.length;p++)for(var c=0;c<this._sections[p].consumer.sources.length;c++)u.push(this._sections[p].consumer.sources[c]);return u}}),l.prototype.originalPositionFor=function(u){var p={generatedLine:e.getArg(u,"line"),generatedColumn:e.getArg(u,"column")},c=t.search(p,this._sections,function(h,m){return h.generatedLine-m.generatedOffset.generatedLine||h.generatedColumn-m.generatedOffset.generatedColumn}),d=this._sections[c];return d?d.consumer.originalPositionFor({line:p.generatedLine-(d.generatedOffset.generatedLine-1),column:p.generatedColumn-(d.generatedOffset.generatedLine===p.generatedLine?d.generatedOffset.generatedColumn-1:0),bias:u.bias}):{source:null,line:null,column:null,name:null}},l.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(u){return u.consumer.hasContentsOfAllSources()})},l.prototype.sourceContentFor=function(u,p){for(var c=0;c<this._sections.length;c++){var d=this._sections[c].consumer.sourceContentFor(u,!0);if(d)return d}if(p)return null;throw new Error('"'+u+'" is not in the SourceMap.')},l.prototype.generatedPositionFor=function(u){for(var p=0;p<this._sections.length;p++){var c=this._sections[p];if(c.consumer._findSourceIndex(e.getArg(u,"source"))!==-1){var d=c.consumer.generatedPositionFor(u);if(d)return{line:d.line+(c.generatedOffset.generatedLine-1),column:d.column+(c.generatedOffset.generatedLine===d.line?c.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},l.prototype._parseMappings=function(u,p){this.__generatedMappings=[],this.__originalMappings=[];for(var c=0;c<this._sections.length;c++)for(var d=this._sections[c],h=d.consumer._generatedMappings,m=0;m<h.length;m++){var g=h[m],f=d.consumer._sources.at(g.source);f=e.computeSourceURL(d.consumer.sourceRoot,f,this._sourceMapURL),this._sources.add(f),f=this._sources.indexOf(f);var _=null;g.name&&(_=d.consumer._names.at(g.name),this._names.add(_),_=this._names.indexOf(_));var S={source:f,generatedLine:g.generatedLine+(d.generatedOffset.generatedLine-1),generatedColumn:g.generatedColumn+(d.generatedOffset.generatedLine===g.generatedLine?d.generatedOffset.generatedColumn-1:0),originalLine:g.originalLine,originalColumn:g.originalColumn,name:_};this.__generatedMappings.push(S),typeof S.originalLine=="number"&&this.__originalMappings.push(S)}s(this.__generatedMappings,e.compareByGeneratedPositionsDeflated),s(this.__originalMappings,e.compareByOriginalPositions)},xt.IndexedSourceMapConsumer=l,xt}().SourceMapConsumer,un.SourceNode=function(){if(Yi)return Ar;Yi=1;var e=Bi().SourceMapGenerator,t=Xt(),n=/(\r?\n)/,r="$$$isSourceNode$$$";function s(a,i,o,l,u){this.children=[],this.sourceContents={},this.line=a??null,this.column=i??null,this.source=o??null,this.name=u??null,this[r]=!0,l!=null&&this.add(l)}return s.fromStringWithSourceMap=function(a,i,o){var l=new s,u=a.split(n),p=0,c=function(){return f()+(f()||"");function f(){return p<u.length?u[p++]:void 0}},d=1,h=0,m=null;return i.eachMapping(function(f){if(m!==null){if(!(d<f.generatedLine)){var _=(S=u[p]||"").substr(0,f.generatedColumn-h);return u[p]=S.substr(f.generatedColumn-h),h=f.generatedColumn,g(m,_),void(m=f)}g(m,c()),d++,h=0}for(;d<f.generatedLine;)l.add(c()),d++;if(h<f.generatedColumn){var S=u[p]||"";l.add(S.substr(0,f.generatedColumn)),u[p]=S.substr(f.generatedColumn),h=f.generatedColumn}m=f},this),p<u.length&&(m&&g(m,c()),l.add(u.splice(p).join(""))),i.sources.forEach(function(f){var _=i.sourceContentFor(f);_!=null&&(o!=null&&(f=t.join(o,f)),l.setSourceContent(f,_))}),l;function g(f,_){if(f===null||f.source===void 0)l.add(_);else{var S=o?t.join(o,f.source):f.source;l.add(new s(f.originalLine,f.originalColumn,S,_,f.name))}}},s.prototype.add=function(a){if(Array.isArray(a))a.forEach(function(i){this.add(i)},this);else{if(!a[r]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);a&&this.children.push(a)}return this},s.prototype.prepend=function(a){if(Array.isArray(a))for(var i=a.length-1;i>=0;i--)this.prepend(a[i]);else{if(!a[r]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);this.children.unshift(a)}return this},s.prototype.walk=function(a){for(var i,o=0,l=this.children.length;o<l;o++)(i=this.children[o])[r]?i.walk(a):i!==""&&a(i,{source:this.source,line:this.line,column:this.column,name:this.name})},s.prototype.join=function(a){var i,o,l=this.children.length;if(l>0){for(i=[],o=0;o<l-1;o++)i.push(this.children[o]),i.push(a);i.push(this.children[o]),this.children=i}return this},s.prototype.replaceRight=function(a,i){var o=this.children[this.children.length-1];return o[r]?o.replaceRight(a,i):typeof o=="string"?this.children[this.children.length-1]=o.replace(a,i):this.children.push("".replace(a,i)),this},s.prototype.setSourceContent=function(a,i){this.sourceContents[t.toSetString(a)]=i},s.prototype.walkSourceContents=function(a){for(var i=0,o=this.children.length;i<o;i++)this.children[i][r]&&this.children[i].walkSourceContents(a);var l=Object.keys(this.sourceContents);for(i=0,o=l.length;i<o;i++)a(t.fromSetString(l[i]),this.sourceContents[l[i]])},s.prototype.toString=function(){var a="";return this.walk(function(i){a+=i}),a},s.prototype.toStringWithSourceMap=function(a){var i={code:"",line:1,column:0},o=new e(a),l=!1,u=null,p=null,c=null,d=null;return this.walk(function(h,m){i.code+=h,m.source!==null&&m.line!==null&&m.column!==null?(u===m.source&&p===m.line&&c===m.column&&d===m.name||o.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:i.line,column:i.column},name:m.name}),u=m.source,p=m.line,c=m.column,d=m.name,l=!0):l&&(o.addMapping({generated:{line:i.line,column:i.column}}),u=null,l=!1);for(var g=0,f=h.length;g<f;g++)h.charCodeAt(g)===10?(i.line++,i.column=0,g+1===f?(u=null,l=!1):l&&o.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:i.line,column:i.column},name:m.name})):i.column++}),this.walkSourceContents(function(h,m){o.setSourceContent(h,m)}),{code:i.code,map:o}},Ar.SourceNode=s,Ar}().SourceNode),un}(function(e,t){t.__esModule=!0;var n=D,r=void 0;try{var s=Sp();r=s.SourceNode}catch{}function a(o,l,u){if(n.isArray(o)){for(var p=[],c=0,d=o.length;c<d;c++)p.push(l.wrap(o[c],u));return p}return typeof o=="boolean"||typeof o=="number"?o+"":o}function i(o){this.srcFile=o,this.source=[]}r||((r=function(o,l,u,p){this.src="",p&&this.add(p)}).prototype={add:function(o){n.isArray(o)&&(o=o.join("")),this.src+=o},prepend:function(o){n.isArray(o)&&(o=o.join("")),this.src=o+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),i.prototype={isEmpty:function(){return!this.source.length},prepend:function(o,l){this.source.unshift(this.wrap(o,l))},push:function(o,l){this.source.push(this.wrap(o,l))},merge:function(){var o=this.empty();return this.each(function(l){o.add(["  ",l,`
`])}),o},each:function(o){for(var l=0,u=this.source.length;l<u;l++)o(this.source[l])},empty:function(){var o=this.currentLocation||{start:{}};return new r(o.start.line,o.start.column,this.srcFile)},wrap:function(o){var l=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return o instanceof r?o:(o=a(o,this,l),new r(l.start.line,l.start.column,this.srcFile,o))},functionCall:function(o,l,u){return u=this.generateList(u),this.wrap([o,l?"."+l+"(":"(",u,")"])},quotedString:function(o){return'"'+(o+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(o){var l=this,u=[];Object.keys(o).forEach(function(c){var d=a(o[c],l);d!=="undefined"&&u.push([l.quotedString(c),":",d])});var p=this.generateList(u);return p.prepend("{"),p.add("}"),p},generateList:function(o){for(var l=this.empty(),u=0,p=o.length;u<p;u++)u&&l.add(","),l.add(a(o[u],this));return l},generateArray:function(o){var l=this.generateList(o);return l.prepend("["),l.add("]"),l}},t.default=i,e.exports=t.default})(Cs,Cs.exports);var kp=Cs.exports;(function(e,t){function n(u){return u&&u.__esModule?u:{default:u}}t.__esModule=!0;var r=ce,s=n(fe),a=D,i=n(kp);function o(u){this.value=u}function l(){}l.prototype={nameLookup:function(u,p){return this.internalNameLookup(u,p)},depthedLookup:function(u){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(u),")"]},compilerInfo:function(){var u=r.COMPILER_REVISION;return[u,r.REVISION_CHANGES[u]]},appendToBuffer:function(u,p,c){return a.isArray(u)||(u=[u]),u=this.source.wrap(u,p),this.environment.isSimple?["return ",u,";"]:c?["buffer += ",u,";"]:(u.appendToBuffer=!0,u)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(u,p){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",u,",",JSON.stringify(p),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(u,p,c,d){this.environment=u,this.options=p,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!d,this.name=this.environment.name,this.isChild=!!c,this.context=c||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(u,p),this.useDepths=this.useDepths||u.useDepths||u.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||u.useBlockParams;var h=u.opcodes,m=void 0,g=void 0,f=void 0,_=void 0;for(f=0,_=h.length;f<_;f++)m=h[f],this.source.currentLocation=m.loc,g=g||m.loc,this[m.opcode].apply(this,m.args);if(this.source.currentLocation=g,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new s.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),d?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var S=this.createFunctionContext(d);if(this.isChild)return S;var b={compiler:this.compilerInfo(),main:S};this.decorators&&(b.main_d=this.decorators,b.useDecorators=!0);var N=this.context,A=N.programs,L=N.decorators;for(f=0,_=A.length;f<_;f++)A[f]&&(b[f]=A[f],L[f]&&(b[f+"_d"]=L[f],b.useDecorators=!0));return this.environment.usePartial&&(b.usePartial=!0),this.options.data&&(b.useData=!0),this.useDepths&&(b.useDepths=!0),this.useBlockParams&&(b.useBlockParams=!0),this.options.compat&&(b.compat=!0),d?b.compilerOptions=this.options:(b.compiler=JSON.stringify(b.compiler),this.source.currentLocation={start:{line:1,column:0}},b=this.objectLiteral(b),p.srcName?(b=b.toStringWithSourceMap({file:p.destName})).map=b.map&&b.map.toString():b=b.toString()),b},preamble:function(){this.lastContext=0,this.source=new i.default(this.options.srcName),this.decorators=new i.default(this.options.srcName)},createFunctionContext:function(u){var p=this,c="",d=this.stackVars.concat(this.registers.list);d.length>0&&(c+=", "+d.join(", "));var h=0;Object.keys(this.aliases).forEach(function(f){var _=p.aliases[f];_.children&&_.referenceCount>1&&(c+=", alias"+ ++h+"="+f,_.children[0]="alias"+h)}),this.lookupPropertyFunctionIsUsed&&(c+=", "+this.lookupPropertyFunctionVarDeclaration());var m=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&m.push("blockParams"),this.useDepths&&m.push("depths");var g=this.mergeSource(c);return u?(m.push(g),Function.apply(this,m)):this.source.wrap(["function(",m.join(","),`) {
  `,g,"}"])},mergeSource:function(u){var p=this.environment.isSimple,c=!this.forceBuffer,d=void 0,h=void 0,m=void 0,g=void 0;return this.source.each(function(f){f.appendToBuffer?(m?f.prepend("  + "):m=f,g=f):(m&&(h?m.prepend("buffer += "):d=!0,g.add(";"),m=g=void 0),h=!0,p||(c=!1))}),c?m?(m.prepend("return "),g.add(";")):h||this.source.push('return "";'):(u+=", buffer = "+(d?"":this.initializeBuffer()),m?(m.prepend("return buffer + "),g.add(";")):this.source.push("return buffer;")),u&&this.source.prepend("var "+u.substring(2)+(d?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(u){var p=this.aliasable("container.hooks.blockHelperMissing"),c=[this.contextName(0)];this.setupHelperArgs(u,0,c);var d=this.popStack();c.splice(1,0,d),this.push(this.source.functionCall(p,"call",c))},ambiguousBlockValue:function(){var u=this.aliasable("container.hooks.blockHelperMissing"),p=[this.contextName(0)];this.setupHelperArgs("",0,p,!0),this.flushInline();var c=this.topStack();p.splice(1,0,c),this.pushSource(["if (!",this.lastHelper,") { ",c," = ",this.source.functionCall(u,"call",p),"}"])},appendContent:function(u){this.pendingContent?u=this.pendingContent+u:this.pendingLocation=this.source.currentLocation,this.pendingContent=u},append:function(){if(this.isInline())this.replaceStack(function(p){return[" != null ? ",p,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var u=this.popStack();this.pushSource(["if (",u," != null) { ",this.appendToBuffer(u,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(u){this.lastContext=u},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(u,p,c,d){var h=0;d||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(u[h++])),this.resolvePath("context",u,h,p,c)},lookupBlockParam:function(u,p){this.useBlockParams=!0,this.push(["blockParams[",u[0],"][",u[1],"]"]),this.resolvePath("context",p,1)},lookupData:function(u,p,c){u?this.pushStackLiteral("container.data(data, "+u+")"):this.pushStackLiteral("data"),this.resolvePath("data",p,0,!0,c)},resolvePath:function(u,p,c,d,h){var m=this;if(this.options.strict||this.options.assumeObjects)this.push(function(f,_,S,b,N){var A=_.popStack(),L=S.length;for(f&&L--;b<L;b++)A=_.nameLookup(A,S[b],N);return f?[_.aliasable("container.strict"),"(",A,", ",_.quotedString(S[b]),", ",JSON.stringify(_.source.currentLocation)," )"]:A}(this.options.strict&&h,this,p,c,u));else for(var g=p.length;c<g;c++)this.replaceStack(function(f){var _=m.nameLookup(f,p[c],u);return d?[" && ",_]:[" != null ? ",_," : ",f]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(u,p){this.pushContext(),this.pushString(p),p!=="SubExpression"&&(typeof u=="string"?this.pushString(u):this.pushStackLiteral(u))},emptyHash:function(u){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(u?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var u=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(u.ids)),this.stringParams&&(this.push(this.objectLiteral(u.contexts)),this.push(this.objectLiteral(u.types))),this.push(this.objectLiteral(u.values))},pushString:function(u){this.pushStackLiteral(this.quotedString(u))},pushLiteral:function(u){this.pushStackLiteral(u)},pushProgram:function(u){u!=null?this.pushStackLiteral(this.programExpression(u)):this.pushStackLiteral(null)},registerDecorator:function(u,p){var c=this.nameLookup("decorators",p,"decorator"),d=this.setupHelperArgs(p,u);this.decorators.push(["fn = ",this.decorators.functionCall(c,"",["fn","props","container",d])," || fn;"])},invokeHelper:function(u,p,c){var d=this.popStack(),h=this.setupHelper(u,p),m=[];c&&m.push(h.name),m.push(d),this.options.strict||m.push(this.aliasable("container.hooks.helperMissing"));var g=["(",this.itemsSeparatedBy(m,"||"),")"],f=this.source.functionCall(g,"call",h.callParams);this.push(f)},itemsSeparatedBy:function(u,p){var c=[];c.push(u[0]);for(var d=1;d<u.length;d++)c.push(p,u[d]);return c},invokeKnownHelper:function(u,p){var c=this.setupHelper(u,p);this.push(this.source.functionCall(c.name,"call",c.callParams))},invokeAmbiguous:function(u,p){this.useRegister("helper");var c=this.popStack();this.emptyHash();var d=this.setupHelper(0,u,p),h=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",u,"helper")," || ",c,")"];this.options.strict||(h[0]="(helper = ",h.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",h,d.paramsInit?["),(",d.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",d.callParams)," : helper))"])},invokePartial:function(u,p,c){var d=[],h=this.setupParams(p,1,d);u&&(p=this.popStack(),delete h.name),c&&(h.indent=JSON.stringify(c)),h.helpers="helpers",h.partials="partials",h.decorators="container.decorators",u?d.unshift(p):d.unshift(this.nameLookup("partials",p,"partial")),this.options.compat&&(h.depths="depths"),h=this.objectLiteral(h),d.push(h),this.push(this.source.functionCall("container.invokePartial","",d))},assignToHash:function(u){var p=this.popStack(),c=void 0,d=void 0,h=void 0;this.trackIds&&(h=this.popStack()),this.stringParams&&(d=this.popStack(),c=this.popStack());var m=this.hash;c&&(m.contexts[u]=c),d&&(m.types[u]=d),h&&(m.ids[u]=h),m.values[u]=p},pushId:function(u,p,c){u==="BlockParam"?this.pushStackLiteral("blockParams["+p[0]+"].path["+p[1]+"]"+(c?" + "+JSON.stringify("."+c):"")):u==="PathExpression"?this.pushString(p):u==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:l,compileChildren:function(u,p){for(var c=u.children,d=void 0,h=void 0,m=0,g=c.length;m<g;m++){d=c[m],h=new this.compiler;var f=this.matchExistingProgram(d);if(f==null){this.context.programs.push("");var _=this.context.programs.length;d.index=_,d.name="program"+_,this.context.programs[_]=h.compile(d,p,this.context,!this.precompile),this.context.decorators[_]=h.decorators,this.context.environments[_]=d,this.useDepths=this.useDepths||h.useDepths,this.useBlockParams=this.useBlockParams||h.useBlockParams,d.useDepths=this.useDepths,d.useBlockParams=this.useBlockParams}else d.index=f.index,d.name="program"+f.index,this.useDepths=this.useDepths||f.useDepths,this.useBlockParams=this.useBlockParams||f.useBlockParams}},matchExistingProgram:function(u){for(var p=0,c=this.context.environments.length;p<c;p++){var d=this.context.environments[p];if(d&&d.equals(u))return d}},programExpression:function(u){var p=this.environment.children[u],c=[p.index,"data",p.blockParams];return(this.useBlockParams||this.useDepths)&&c.push("blockParams"),this.useDepths&&c.push("depths"),"container.program("+c.join(", ")+")"},useRegister:function(u){this.registers[u]||(this.registers[u]=!0,this.registers.list.push(u))},push:function(u){return u instanceof o||(u=this.source.wrap(u)),this.inlineStack.push(u),u},pushStackLiteral:function(u){this.push(new o(u))},pushSource:function(u){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),u&&this.source.push(u)},replaceStack:function(u){var p=["("],c=void 0,d=void 0,h=void 0;if(!this.isInline())throw new s.default("replaceStack on non-inline");var m=this.popStack(!0);if(m instanceof o)p=["(",c=[m.value]],h=!0;else{d=!0;var g=this.incrStack();p=["((",this.push(g)," = ",m,")"],c=this.topStack()}var f=u.call(this,c);h||this.popStack(),d&&this.stackSlot--,this.push(p.concat(f,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var u=this.inlineStack;this.inlineStack=[];for(var p=0,c=u.length;p<c;p++){var d=u[p];if(d instanceof o)this.compileStack.push(d);else{var h=this.incrStack();this.pushSource([h," = ",d,";"]),this.compileStack.push(h)}}},isInline:function(){return this.inlineStack.length},popStack:function(u){var p=this.isInline(),c=(p?this.inlineStack:this.compileStack).pop();if(!u&&c instanceof o)return c.value;if(!p){if(!this.stackSlot)throw new s.default("Invalid stack pop");this.stackSlot--}return c},topStack:function(){var u=this.isInline()?this.inlineStack:this.compileStack,p=u[u.length-1];return p instanceof o?p.value:p},contextName:function(u){return this.useDepths&&u?"depths["+u+"]":"depth"+u},quotedString:function(u){return this.source.quotedString(u)},objectLiteral:function(u){return this.source.objectLiteral(u)},aliasable:function(u){var p=this.aliases[u];return p?(p.referenceCount++,p):((p=this.aliases[u]=this.source.wrap(u)).aliasable=!0,p.referenceCount=1,p)},setupHelper:function(u,p,c){var d=[];return{params:d,paramsInit:this.setupHelperArgs(p,u,d,c),name:this.nameLookup("helpers",p,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(d)}},setupParams:function(u,p,c){var d={},h=[],m=[],g=[],f=!c,_=void 0;f&&(c=[]),d.name=this.quotedString(u),d.hash=this.popStack(),this.trackIds&&(d.hashIds=this.popStack()),this.stringParams&&(d.hashTypes=this.popStack(),d.hashContexts=this.popStack());var S=this.popStack(),b=this.popStack();(b||S)&&(d.fn=b||"container.noop",d.inverse=S||"container.noop");for(var N=p;N--;)_=this.popStack(),c[N]=_,this.trackIds&&(g[N]=this.popStack()),this.stringParams&&(m[N]=this.popStack(),h[N]=this.popStack());return f&&(d.args=this.source.generateArray(c)),this.trackIds&&(d.ids=this.source.generateArray(g)),this.stringParams&&(d.types=this.source.generateArray(m),d.contexts=this.source.generateArray(h)),this.options.data&&(d.data="data"),this.useBlockParams&&(d.blockParams="blockParams"),d},setupHelperArgs:function(u,p,c,d){var h=this.setupParams(u,p,c);return h.loc=JSON.stringify(this.source.currentLocation),h=this.objectLiteral(h),d?(this.useRegister("options"),c.push("options"),["options=",h]):c?(c.push(h),""):h}},function(){for(var u="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),p=l.RESERVED_WORDS={},c=0,d=u.length;c<d;c++)p[u[c]]=!0}(),l.isValidJavaScriptVariableName=function(u){return!l.RESERVED_WORDS[u]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(u)},t.default=l,e.exports=t.default})(Ns,Ns.exports);var Tp=Ns.exports;(function(e,t){function n(h){return h&&h.__esModule?h:{default:h}}t.__esModule=!0;var r=n(dp),s=n(su),a=jt,i=Pt,o=n(Tp),l=n(au),u=n(ru),p=r.default.create;function c(){var h=p();return h.compile=function(m,g){return i.compile(m,g,h)},h.precompile=function(m,g){return i.precompile(m,g,h)},h.AST=s.default,h.Compiler=i.Compiler,h.JavaScriptCompiler=o.default,h.Parser=a.parser,h.parse=a.parse,h.parseWithoutProcessing=a.parseWithoutProcessing,h}var d=c();d.create=c,u.default(d),d.Visitor=l.default,d.default=d,t.default=d,e.exports=t.default})(rs,rs.exports);const wp=Jn(rs.exports),Ip=`
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
	<files_modified>
{{#each filesModified}}
		{{{this}}}
{{/each}}
	</files_modified>
{{/if}}
{{#if filesCreated}}
	<files_created>
{{#each filesCreated}}
		{{{this}}}
{{/each}}
	</files_created>
{{/if}}
{{#if filesDeleted}}
	<files_deleted>
{{#each filesDeleted}}
		{{{this}}}
{{/each}}
	</files_deleted>
{{/if}}
{{#if filesViewed}}
	<files_viewed>
{{#each filesViewed}}
		{{{this}}}
{{/each}}
	</files_viewed>
{{/if}}
{{#if terminalCommands}}
	<terminal_commands>
{{#each terminalCommands}}
		{{{this}}}
{{/each}}
	</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim(),Wh=wp.compile(Ip);function U(e,t){t=t||{},this._capacity=t.capacity,this._head=0,this._tail=0,Array.isArray(e)?this._fromArray(e):(this._capacityMask=3,this._list=new Array(4))}U.prototype.peekAt=function(e){var t=e;if(t===(0|t)){var n=this.size();if(!(t>=n||t<-n))return t<0&&(t+=n),t=this._head+t&this._capacityMask,this._list[t]}},U.prototype.get=function(e){return this.peekAt(e)},U.prototype.peek=function(){if(this._head!==this._tail)return this._list[this._head]},U.prototype.peekFront=function(){return this.peek()},U.prototype.peekBack=function(){return this.peekAt(-1)},Object.defineProperty(U.prototype,"length",{get:function(){return this.size()}}),U.prototype.size=function(){return this._head===this._tail?0:this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},U.prototype.unshift=function(e){if(arguments.length===0)return this.size();var t=this._list.length;return this._head=this._head-1+t&this._capacityMask,this._list[this._head]=e,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.pop(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},U.prototype.shift=function(){var e=this._head;if(e!==this._tail){var t=this._list[e];return this._list[e]=void 0,this._head=e+1&this._capacityMask,e<2&&this._tail>1e4&&this._tail<=this._list.length>>>2&&this._shrinkArray(),t}},U.prototype.push=function(e){if(arguments.length===0)return this.size();var t=this._tail;return this._list[t]=e,this._tail=t+1&this._capacityMask,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.shift(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},U.prototype.pop=function(){var e=this._tail;if(e!==this._head){var t=this._list.length;this._tail=e-1+t&this._capacityMask;var n=this._list[this._tail];return this._list[this._tail]=void 0,this._head<2&&e>1e4&&e<=t>>>2&&this._shrinkArray(),n}},U.prototype.removeOne=function(e){var t=e;if(t===(0|t)&&this._head!==this._tail){var n=this.size(),r=this._list.length;if(!(t>=n||t<-n)){t<0&&(t+=n),t=this._head+t&this._capacityMask;var s,a=this._list[t];if(e<n/2){for(s=e;s>0;s--)this._list[t]=this._list[t=t-1+r&this._capacityMask];this._list[t]=void 0,this._head=this._head+1+r&this._capacityMask}else{for(s=n-1-e;s>0;s--)this._list[t]=this._list[t=t+1+r&this._capacityMask];this._list[t]=void 0,this._tail=this._tail-1+r&this._capacityMask}return a}}},U.prototype.remove=function(e,t){var n,r=e,s=t;if(r===(0|r)&&this._head!==this._tail){var a=this.size(),i=this._list.length;if(!(r>=a||r<-a||t<1)){if(r<0&&(r+=a),t===1||!t)return(n=new Array(1))[0]=this.removeOne(r),n;if(r===0&&r+t>=a)return n=this.toArray(),this.clear(),n;var o;for(r+t>a&&(t=a-r),n=new Array(t),o=0;o<t;o++)n[o]=this._list[this._head+r+o&this._capacityMask];if(r=this._head+r&this._capacityMask,e+t===a){for(this._tail=this._tail-t+i&this._capacityMask,o=t;o>0;o--)this._list[r=r+1+i&this._capacityMask]=void 0;return n}if(e===0){for(this._head=this._head+t+i&this._capacityMask,o=t-1;o>0;o--)this._list[r=r+1+i&this._capacityMask]=void 0;return n}if(r<a/2){for(this._head=this._head+e+t+i&this._capacityMask,o=e;o>0;o--)this.unshift(this._list[r=r-1+i&this._capacityMask]);for(r=this._head-1+i&this._capacityMask;s>0;)this._list[r=r-1+i&this._capacityMask]=void 0,s--;e<0&&(this._tail=r)}else{for(this._tail=r,r=r+t+i&this._capacityMask,o=a-(t+e);o>0;o--)this.push(this._list[r++]);for(r=this._tail;s>0;)this._list[r=r+1+i&this._capacityMask]=void 0,s--}return this._head<2&&this._tail>1e4&&this._tail<=i>>>2&&this._shrinkArray(),n}}},U.prototype.splice=function(e,t){var n=e;if(n===(0|n)){var r=this.size();if(n<0&&(n+=r),!(n>r)){if(arguments.length>2){var s,a,i,o=arguments.length,l=this._list.length,u=2;if(!r||n<r/2){for(a=new Array(n),s=0;s<n;s++)a[s]=this._list[this._head+s&this._capacityMask];for(t===0?(i=[],n>0&&(this._head=this._head+n+l&this._capacityMask)):(i=this.remove(n,t),this._head=this._head+n+l&this._capacityMask);o>u;)this.unshift(arguments[--o]);for(s=n;s>0;s--)this.unshift(a[s-1])}else{var p=(a=new Array(r-(n+t))).length;for(s=0;s<p;s++)a[s]=this._list[this._head+n+t+s&this._capacityMask];for(t===0?(i=[],n!=r&&(this._tail=this._head+n+l&this._capacityMask)):(i=this.remove(n,t),this._tail=this._tail-p+l&this._capacityMask);u<o;)this.push(arguments[u++]);for(s=0;s<p;s++)this.push(a[s])}return i}return this.remove(n,t)}}},U.prototype.clear=function(){this._list=new Array(this._list.length),this._head=0,this._tail=0},U.prototype.isEmpty=function(){return this._head===this._tail},U.prototype.toArray=function(){return this._copyArray(!1)},U.prototype._fromArray=function(e){var t=e.length,n=this._nextPowerOf2(t);this._list=new Array(n),this._capacityMask=n-1,this._tail=t;for(var r=0;r<t;r++)this._list[r]=e[r]},U.prototype._copyArray=function(e,t){var n=this._list,r=n.length,s=this.length;if((t|=s)==s&&this._head<this._tail)return this._list.slice(this._head,this._tail);var a,i=new Array(t),o=0;if(e||this._head>this._tail){for(a=this._head;a<r;a++)i[o++]=n[a];for(a=0;a<this._tail;a++)i[o++]=n[a]}else for(a=this._head;a<this._tail;a++)i[o++]=n[a];return i},U.prototype._growArray=function(){if(this._head!=0){var e=this._copyArray(!0,this._list.length<<1);this._tail=this._list.length,this._head=0,this._list=e}else this._tail=this._list.length,this._list.length<<=1;this._capacityMask=this._capacityMask<<1|1},U.prototype._shrinkArray=function(){this._list.length>>>=1,this._capacityMask>>>=1},U.prototype._nextPowerOf2=function(e){var t=1<<Math.log(e)/Math.log(2)+1;return Math.max(t,4)};const zh=Jn(U);function Xh(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case le.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case le.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case le.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case le.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Np[t]}const Np={[le.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[le.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[le.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[le.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var _e=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(_e||{}),_n=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(_n||{}),du=(e=>(e.memoryCreated="memory-created",e.memoryCreatedResponse="memory-created-response",e.memoryProcessed="memory-processed",e.getMemoriesByState="get-memories-by-state",e.getMemoriesByStateResponse="get-memories-by-state-response",e.updateMemoryState="update-memory-state",e.updateMemoryStateResponse="update-memory-state-response",e.flushPendingMemories="flush-pending-memories",e.flushPendingMemoriesResponse="flush-pending-memories-response",e))(du||{});class Jh{constructor(t,n,r){y(this,"_taskClient");y(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:w.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test..."),async function(n){return(await Xa(ti,new Nn({sendMessage:s=>{n.postMessage(s)},onReceiveMessage:s=>{const a=i=>{s(i.data)};return window.addEventListener("message",a),()=>{window.removeEventListener("message",a)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host).then(n=>{console.log("Hello world result:",n)}),async function(n,r){const s=Xa(ti,new Nn({sendMessage:a=>{n.postMessage(a)},onReceiveMessage:a=>{const i=o=>{a(o.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).streamHelloWorld({foo:"streaming"},{timeoutMs:1e4});for await(const a of s)r(a.result)}(this._host,n=>{console.log("Hello world streaming chunk:",n)})}catch(n){console.error("Hello world error:",n)}return t.data});y(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:w.reportWebviewClientMetric,data:{webviewName:Oo.chat,client_metric:t,value:1}})});y(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.sendToSidecar({type:Yr.trackAnalyticsEvent,data:{eventName:t,properties:n}},5e3)});y(this,"trackExperimentViewed",(t,n,r)=>{this._asyncMsgSender.sendToSidecar({type:Yr.trackExperimentViewedEvent,data:{experimentName:t,treatment:n,properties:r}},5e3)});y(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:Y.reportAgentSessionEvent,data:t})});y(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:Y.reportAgentRequestEvent,data:t})});y(this,"getSuggestions",async(t,n=!1)=>{const r={rootPath:"",relPath:t},s=this.findFiles(r,6),a=this.findRecentlyOpenedFiles(r,6),i=this.findFolders(r,3),o=this.findExternalSources(t,n),l=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[u,p,c,d,h]=await Promise.all([Rt(s,[]),Rt(a,[]),Rt(i,[]),Rt(o,[]),Rt(l,[])]),m=(f,_)=>({...id(f),[_]:f}),g=[...u.map(f=>m(f,"file")),...c.map(f=>m(f,"folder")),...p.map(f=>m(f,"recentFile")),...d.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...h.map(f=>({...od(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(t);f.length>0&&g.push(...f)}return g});y(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return hi;const n=t.toLowerCase();return hi.filter(r=>{const s=r.personality.description.toLowerCase(),a=r.label.toLowerCase();return s.includes(n)||a.includes(n)})});y(this,"sendAction",t=>{this._host.postMessage({type:w.mainPanelPerformAction,data:t})});y(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:w.showAugmentPanel})});y(this,"showNotification",t=>{this._host.postMessage({type:w.showNotification,data:t})});y(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:w.openConfirmationModal,data:t},1e9)).data.ok);y(this,"clearMetadataFor",t=>{this._host.postMessage({type:w.chatClearMetadata,data:t})});y(this,"resolvePath",async(t,n=void 0)=>{const r=await this._asyncMsgSender.send({type:w.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(r.data)return r.data});y(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:w.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);y(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:w.getDiagnosticsRequest},1e3)).data);y(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:w.findFileRequest,data:{...t,maxResults:n}},5e3)).data);y(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:w.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);y(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:w.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);y(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:w.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);y(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:_e.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);y(this,"openFile",t=>{this._host.postMessage({type:w.openFile,data:t})});y(this,"saveFile",t=>this._host.postMessage({type:w.saveFile,data:t}));y(this,"loadFile",t=>this._host.postMessage({type:w.loadFile,data:t}));y(this,"openMemoriesFile",()=>{this._host.postMessage({type:w.openMemoriesFile})});y(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:w.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(r){return console.error("Failed to check if terminal can be shown:",r),!1}});y(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:w.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(r){return console.error("Failed to show terminal:",r),!1}});y(this,"createFile",(t,n)=>{this._host.postMessage({type:w.chatCreateFile,data:{code:t,relPath:n}})});y(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:w.openScratchFileRequest,data:{content:t,language:n}},1e4)});y(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:w.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});y(this,"smartPaste",t=>{this._host.postMessage({type:w.chatSmartPaste,data:t})});y(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));y(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));y(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});y(this,"createTask",async(t,n,r,s)=>this._taskClient.createTask(t,n,r,s));y(this,"updateTask",async(t,n,r)=>this._taskClient.updateTask(t,n,r));y(this,"saveChat",async(t,n,r)=>this._asyncMsgSender.send({type:w.saveChat,data:{conversationId:t,chatHistory:n,title:r}},5e3));y(this,"updateUserGuidelines",t=>{this._host.postMessage({type:w.updateUserGuidelines,data:t})});y(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:w.updateWorkspaceGuidelines,data:t})});y(this,"openSettingsPage",t=>{this._host.postMessage({type:w.openSettingsPage,data:t})});y(this,"_activeRetryStreams",new Map);y(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:w.chatUserCancel,data:{requestId:t}},1e4)});y(this,"sendUserRating",async(t,n,r,s="")=>{const a={requestId:t,rating:r,note:s,mode:n},i={type:w.chatRating,data:a};return(await this._asyncMsgSender.send(i,3e4)).data});y(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:w.usedChat})});y(this,"createProject",t=>{this._host.postMessage({type:w.mainPanelCreateProject,data:{name:t}})});y(this,"openProjectFolder",()=>{this._host.postMessage({type:w.mainPanelPerformAction,data:"open-folder"})});y(this,"closeProjectFolder",()=>{this._host.postMessage({type:w.mainPanelPerformAction,data:"close-folder"})});y(this,"cloneRepository",()=>{this._host.postMessage({type:w.mainPanelPerformAction,data:"clone-repository"})});y(this,"grantSyncPermission",()=>{this._host.postMessage({type:w.mainPanelPerformAction,data:"grant-sync-permission"})});y(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:w.startRemoteMCPAuth,data:{name:t}})});y(this,"callTool",async(t,n,r,s,a,i)=>{const o={type:w.callTool,data:{chatRequestId:t,toolUseId:n,name:r,input:s,chatHistory:a,conversationId:i}};return(await this._asyncMsgSender.send(o,0)).data});y(this,"cancelToolRun",async(t,n)=>{const r={type:w.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(r,0)});y(this,"checkSafe",async t=>{const n={type:fn.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});y(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:fn.closeAllToolProcesses},0)});y(this,"getToolIdentifier",async t=>{const n={type:fn.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});y(this,"getChatMode",async()=>{const t={type:Y.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});y(this,"setChatMode",t=>{this._asyncMsgSender.send({type:w.chatModeChanged,data:{mode:t}})});y(this,"getAgentEditList",async(t,n)=>{const r={type:Y.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});y(this,"hasChangesSince",async t=>{const n={type:Y.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(r=>{var s,a;return((s=r.changesSummary)==null?void 0:s.totalAddedLines)||((a=r.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});y(this,"getToolCallCheckpoint",async t=>{const n={type:w.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});y(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:Y.setCurrentConversation,data:{conversationId:t}},5e3)});y(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:Y.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});y(this,"showAgentReview",(t,n,r,s=!0,a)=>{this._asyncMsgSender.sendToSidecar({type:Y.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:r,retainFocus:s,useNativeDiffIfAvailable:a}})});y(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:Y.chatAgentEditAcceptAll}),!0));y(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:Y.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));y(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:w.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);y(this,"getAgentEditChangesByRequestId",async t=>{const n={type:Y.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});y(this,"getAgentEditContentsByRequestId",async t=>{const n={type:Y.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});y(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:w.triggerInitialOrientation})});y(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:w.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});y(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:w.toggleCollapseUnchangedRegions})});y(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:w.checkAgentAutoModeApproval},5e3)).data);y(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:w.setAgentAutoModeApproved,data:t},5e3)});y(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:Y.checkHasEverUsedAgent},5e3)).data);y(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:Y.setHasEverUsedAgent,data:t},5e3)});y(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:Y.checkHasEverUsedRemoteAgent},5e3)).data);y(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:Y.setHasEverUsedRemoteAgent,data:t},5e3)});y(this,"getChatRequestIdeState",async()=>{const t={type:w.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});y(this,"reportError",t=>{this._host.postMessage({type:w.reportError,data:t})});y(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});y(this,"sendFlushPendingMemories",async(t=3e4)=>{try{const n={type:du.flushPendingMemories,data:{}};await this._asyncMsgSender.sendToSidecar(n,t)}catch{return}});y(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=r,this._taskClient=new Vl(n)}async*generateCommitMessage(){const t={type:w.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*Or(n,()=>{},this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*sendInstructionMessage(t,n){const r={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},s={type:w.chatInstructionMessage,data:r},a=this._asyncMsgSender.stream(s,3e4,6e4);yield*async function*(i){let o;try{for await(const l of i)o=l.data.requestId,yield{request_id:o,response_text:l.data.text,seen_state:Te.unseen,status:se.sent};yield{request_id:o,seen_state:Te.unseen,status:se.success}}catch(l){console.error("Error in chat instruction model reply stream:",l),yield{request_id:o,seen_state:Te.unseen,status:se.failed}}}(a)}async openGuidelines(t){this._host.postMessage({type:w.openGuidelines,data:t})}async*getExistingChatStream(t,n,r){const s=r==null?void 0:r.flags.enablePreferenceCollection,a=s?1e9:6e4,i=s?1e9:3e5,o={type:w.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},l=this._asyncMsgSender.stream(o,a,i);yield*Or(l,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*startChatStream(t,n){const r=n==null?void 0:n.flags.enablePreferenceCollection,s=r?1e9:1e5,a=r?1e9:3e5,i={type:w.chatUserMessage,data:t},o=this._asyncMsgSender.stream(i,s,a);yield*Or(o,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:w.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const r=ei(await pr(t)),s=n??`${await Qa(await hr(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:w.chatSaveImageRequest,data:{filename:s,data:r}},1e4)).data}async saveAttachment(t,n){const r=ei(await pr(t)),s=n??`${await Qa(await hr(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:w.chatSaveAttachmentRequest,data:{filename:s,data:r}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:w.chatLoadImageRequest,data:t},1e4),r=n.data?await hr(n.data):void 0;if(!r)return;let s="application/octet-stream";const a=t.split(".").at(-1);a==="png"?s="image/png":a!=="jpg"&&a!=="jpeg"||(s="image/jpeg");const i=new File([r],t,{type:s});return await pr(i)}async deleteImage(t){await this._asyncMsgSender.send({type:w.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,r){const s=new ql(t,n,(a,i)=>this.startChatStream(a,i),(r==null?void 0:r.maxRetries)??5,4e3,r==null?void 0:r.flags);this._activeRetryStreams.set(t,s);try{yield*s.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:w.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const r={type:gn.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const r={type:gn.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationExchanges(t){const n={type:gn.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationHistory(t){var s;const n={type:yn.loadConversationHistoryRequest,data:{conversationId:t}},r=await this._asyncMsgSender.sendToSidecar(n,3e4);return((s=r==null?void 0:r.data)==null?void 0:s.chatHistory)||[]}async saveConversationHistory(t,n){const r={type:yn.saveConversationHistoryRequest,data:{conversationId:t,chatHistory:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationHistory(t){const n={type:yn.deleteConversationHistoryRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:_n.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const r={type:_n.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationToolUseStates(t){const n={type:_n.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Or(e,t=()=>{},n,r){let s;try{for await(const a of e){if(s=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),r==null||r(Bl.MESSAGE_SEND_ERROR_DISPLAYED,{errorMessagePreview:a.data.error.displayErrorMessage.substring(0,100),requestId:s}),yield{request_id:s,seen_state:Te.unseen,status:se.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const i={request_id:s,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:Cp(a.data.nodes),seen_state:Te.unseen,status:se.sent,lastChunkId:a.data.chunkId};a.data.stop_reason!=null&&(i.stop_reason=a.data.stop_reason),yield i}yield{request_id:s,seen_state:Te.unseen,status:se.success}}catch(a){let i,o;if(t({originalRequestId:s||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof vu&&n)switch(a.name){case"MessageTimeout":i=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":i=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:s,seen_state:Te.unseen,status:se.failed,isRetriable:i,shouldBackoff:o}}}async function Rt(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function Cp(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==Ou.TOOL_USE||!t&&(t=!0,!0))}var xp=Mu,Rp=/\s/,Ap=function(e){for(var t=e.length;t--&&Rp.test(e.charAt(t)););return t},Op=/^\s+/,Pp=Du,Mp=Lu,Lp=function(e){return e&&e.slice(0,Ap(e)+1).replace(Op,"")},Hi=As,Dp=function(e){return typeof e=="symbol"||Mp(e)&&Pp(e)=="[object Symbol]"},Fp=/^[-+]0x[0-9a-f]+$/i,Up=/^0b[01]+$/i,$p=/^0o[0-7]+$/i,Bp=parseInt,jp=As,Pr=function(){return xp.Date.now()},Zi=function(e){if(typeof e=="number")return e;if(Dp(e))return NaN;if(Hi(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Hi(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Lp(e);var n=Up.test(e);return n||$p.test(e)?Bp(e.slice(2),n?2:8):Fp.test(e)?NaN:+e},Gp=Math.max,qp=Math.min,Vp=function(e,t,n){var r,s,a,i,o,l,u=0,p=!1,c=!1,d=!0;if(typeof e!="function")throw new TypeError("Expected a function");function h(S){var b=r,N=s;return r=s=void 0,u=S,i=e.apply(N,b)}function m(S){var b=S-l;return l===void 0||b>=t||b<0||c&&S-u>=a}function g(){var S=Pr();if(m(S))return f(S);o=setTimeout(g,function(b){var N=t-(b-l);return c?qp(N,a-(b-u)):N}(S))}function f(S){return o=void 0,d&&r?h(S):(r=s=void 0,i)}function _(){var S=Pr(),b=m(S);if(r=arguments,s=this,l=S,b){if(o===void 0)return function(N){return u=N,o=setTimeout(g,t),p?h(N):i}(l);if(c)return clearTimeout(o),o=setTimeout(g,t),h(l)}return o===void 0&&(o=setTimeout(g,t)),i}return t=Zi(t)||0,jp(n)&&(p=!!n.leading,a=(c="maxWait"in n)?Gp(Zi(n.maxWait)||0,t):a,d="trailing"in n?!!n.trailing:d),_.cancel=function(){o!==void 0&&clearTimeout(o),u=0,r=l=s=o=void 0},_.flush=function(){return o===void 0?i:f(Pr())},_},Yp=As;const Kp=Jn(function(e,t,n){var r=!0,s=!0;if(typeof e!="function")throw new TypeError("Expected a function");return Yp(n)&&(r="leading"in n?!!n.leading:r,s="trailing"in n?!!n.trailing:s),Vp(e,t,{leading:r,maxWait:t,trailing:s})});class Hp{constructor(t){y(this,"SIDECAR_TIMEOUT_MS",5e3);y(this,"getRulesList",async(t=!0)=>{const n={type:_e.getRulesListRequest,data:{includeGuidelines:t}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});y(this,"createRule",async t=>{const n={type:_e.createRule,data:{ruleName:t.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});y(this,"getWorkspaceRoot",async()=>{const t={type:_e.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});y(this,"updateRuleFile",async(t,n)=>{const r={type:_e.updateRuleFile,data:{path:t,content:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});y(this,"deleteRule",async(t,n=!0)=>{const r={type:_e.deleteRule,data:{path:t,confirmed:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});y(this,"processSelectedPaths",async(t,n=!0)=>{const r={type:_e.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:n}},s=await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:s.data.importedRulesCount,directoryOrFile:s.data.directoryOrFile,errors:s.data.errors}});y(this,"getAutoImportOptions",async()=>{const t={type:_e.autoImportRules};return await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)});y(this,"processAutoImportSelection",async t=>{const n={type:_e.autoImportRulesSelectionRequest,data:{selectedLabel:t}},r=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:r.data.importedRulesCount,duplicatesCount:r.data.duplicatesCount,totalAttempted:r.data.totalAttempted,source:r.data.source}});this._asyncMsgSender=t}}class Qh{constructor(t,n=!0){y(this,"_rulesFiles",bn([]));y(this,"_loading",bn(!0));y(this,"_extensionClientRules");y(this,"_requestRulesThrottled",Kp(async()=>{this._loading.set(!0);try{const t=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(t)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=t,this.includeGuidelines=n,this._extensionClientRules=new Hp(this._msgBroker),this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==w.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(t){try{const n=await this._extensionClientRules.createRule(t);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const n=Zr.formatRuleFileForMarkdown(t);try{await this._extensionClientRules.updateRuleFile(t.path,n)}catch(r){console.error("Failed to update rule file:",r)}await this.requestRules()}async deleteRule(t){try{await this._extensionClientRules.deleteRule(t,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(t){try{const n=await this._extensionClientRules.processSelectedPaths(t,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(t){try{const n=await this._extensionClientRules.processAutoImportSelection(t.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var Zp=Ji('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function Wp(e){var t=Zp();j(e,t)}const qt=class qt{constructor(t=void 0){y(this,"_lastFocusAnchorElement");y(this,"_focusedIndexStore",bn(void 0));y(this,"focusedIndex",this._focusedIndexStore);y(this,"_rootElement");y(this,"_triggerElement");y(this,"_getItems",()=>{var r;const t=(r=this._rootElement)==null?void 0:r.querySelectorAll(`.${qt.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});y(this,"_recomputeFocusAnchor",t=>{var a;const n=(a=this._parentContext)==null?void 0:a._getItems(),r=n==null?void 0:n.indexOf(t);if(r===void 0||n===void 0)return;const s=Math.max(r-1,0);this._lastFocusAnchorElement=n[s]});y(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},r=s=>{t.contains(s.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",r),this._getItems(),{destroy:()=>{this._removeFromTrapStack(),this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",r),this._focusedIndexStore.set(void 0)}}});y(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));y(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const r=this.getCurrentFocusedIdx();if(r===void 0||this.parentContext)break;(!t.shiftKey&&r===this._getItems().length-1||t.shiftKey&&r===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});y(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Bu)});y(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(r=>r===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});y(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=dn(t,n.length);this._focusedIndexStore.set(r)});y(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=dn(t,n.length),s=n[r];s==null||s.focus(),this._focusedIndexStore.set(r)});y(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});y(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=dn(t.findIndex(r=>r===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});y(this,"focusPrev",()=>{var r;const t=this._getItems();if(t.length===0)return;const n=dn(t.findIndex(s=>s===document.activeElement)-1,t.length);(r=t[n])==null||r.focus(),this._focusedIndexStore.set(n)});y(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await Mr())});y(this,"_addToTrapStack",()=>{this._rootElement&&aa.add(this._rootElement)});y(this,"_removeFromTrapStack",()=>{this._rootElement&&aa.remove(this._rootElement)});y(this,"handleOpenChange",t=>{t?this._addToTrapStack():this._removeFromTrapStack()});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};y(qt,"CONTEXT_KEY","augment-dropdown-menu-focus"),y(qt,"ITEM_CLASS","js-dropdown-menu__focusable-item");let ie=qt;function dn(e,t){return(e%t+t)%t}const vt="augment-dropdown-menu-content";var zp=Ne("<div><!></div>"),Xp=Ne('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Wi(e,t){pe(t,!1);const[n,r]=Ge(),s=()=>Ee(h,"$sizeState",n),a=rt();let i=F(t,"size",8,2),o=F(t,"onEscapeKeyDown",8,()=>{}),l=F(t,"onClickOutside",8,()=>{}),u=F(t,"onRequestClose",8,()=>{}),p=F(t,"side",8,"top"),c=F(t,"align",8,"center");const d={size:bn(i())},h=d.size;Rs(vt,d);const m=J(ie.CONTEXT_KEY),g=J(pn.CONTEXT_KEY);Ye(()=>_t(i()),()=>{h.set(i())}),Ye(()=>{},()=>{Eu(nt(a,g.state),"$openState",n)}),Mt(),be(),_u("keydown",bu,function(f){if(Ee(H(a),"$openState",n).open&&f.key==="Tab"&&!f.shiftKey){if(m.getCurrentFocusedIdx()!==void 0)return;f.preventDefault(),m==null||m.focusIdx(0)}}),ju(e,{onEscapeKeyDown:o(),onClickOutside:function(f){var _;return g.forceControlSetOpen(!1),(_=l())==null?void 0:_(f)},onRequestClose:u(),get side(){return p()},get align(){return c()},$$events:{keydown(f){Fu.call(this,t,f)}},children:(f,_)=>{var S=Xp(),b=Le(S);Gu(b,{get size(){return s()},insetContent:!0,includeBackground:!1,children:(N,A)=>{var L=zp(),M=Le(L);ue(M,t,"default",{},null),Dr(L,ne=>{var $;return($=m.registerRoot)==null?void 0:$.call(m,ne)}),Lt(()=>Dt(L,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${s()}`,"svelte-o54ind")),j(N,L)},$$slots:{default:!0}}),j(f,S)},$$slots:{default:!0}}),he(),r()}var Jp=Ne('<div class="c-dropdown-menu-augment__item-icon svelte-17zgln9"><!></div>'),Qp=Ne('<div class="c-dropdown-menu-augment__item-icon svelte-17zgln9"><!></div>'),eh=Ne("<!> <!> <!>",1);function xs(e,t){const n=Su(t),r=Q(t,["children","$$slots","$$events","$$legacy"]),s=Q(r,["highlight","disabled","color","onSelect"]);pe(t,!1);const[a,i]=Ge(),o=()=>Ee(_,"$sizeState",a),l=rt(),u=rt(),p=rt();let c=F(t,"highlight",24,()=>{}),d=F(t,"disabled",24,()=>{}),h=F(t,"color",24,()=>{}),m=F(t,"onSelect",8,()=>{});const g=J(vt),f=J(ie.CONTEXT_KEY),_=g.size;function S(M){var V;if(d())return;const ne=(V=f.rootElement)==null?void 0:V.querySelectorAll(`.${ie.ITEM_CLASS}`);if(!ne)return;const $=Array.from(ne).findIndex(ge=>ge===M);$!==-1&&f.setFocusedIdx($)}Ye(()=>(H(l),H(u),_t(s)),()=>{nt(l,s.class),nt(u,ku(s,["class"]))}),Ye(()=>(_t(d()),_t(c()),H(l)),()=>{nt(p,[d()?"":ie.ITEM_CLASS,"c-dropdown-menu-augment__item",c()?"c-dropdown-menu-augment__item--highlighted":"",H(l)].join(" "))}),Mt(),be();const b=Lr(()=>h()??"neutral"),N=Lr(()=>!h());var A=ta(()=>na("dropdown-menu-item","highlighted",c())),L=ta(()=>na("dropdown-menu-item","disabled",d()));Uu(e,et({get class(){return H(p)},get size(){return o()},variant:"ghost",get color(){return H(b)},get highContrast(){return H(N)},alignment:"left",get disabled(){return d()}},()=>H(A),()=>H(L),()=>H(u),{$$events:{click:M=>{M.currentTarget instanceof HTMLElement&&S(M.currentTarget),m()(M)},mouseover:M=>{M.currentTarget instanceof HTMLElement&&S(M.currentTarget)},mousedown:M=>{M.preventDefault(),M.stopPropagation()}},children:(M,ne)=>{var $=eh(),V=ke($),ge=Ce=>{var lt=Jp(),ct=Le(lt);ue(ct,t,"iconLeft",{},null),j(Ce,lt)};Qs(V,Ce=>{En(()=>n.iconLeft)&&Ce(ge)});var Js=ea(V,2);Qi(Js,{get size(){return o()},children:(Ce,lt)=>{var ct=Me(),fu=ke(ct);ue(fu,t,"default",{},null),j(Ce,ct)},$$slots:{default:!0}});var hu=ea(Js,2),mu=Ce=>{var lt=Qp(),ct=Le(lt);ue(ct,t,"iconRight",{},null),j(Ce,lt)};Qs(hu,Ce=>{En(()=>n.iconRight)&&Ce(mu)}),j(M,$)},$$slots:{default:!0}})),he(),i()}var th=Ji("<svg><!></svg>");function nh(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]);var r=th();Tu(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 320 512",...n}));var s=Le(r);$u(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',!0),j(e,r)}function zi(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,[]);xs(e,et({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>r,{children:(s,a)=>{var i=Me(),o=ke(i);ue(o,t,"default",{},null),j(s,i)},$$slots:{default:!0,iconRight:(s,a)=>{wu(s,{slot:"iconRight",name:"chevron-right",children:(i,o)=>{nh(i,{})},$$slots:{default:!0}})}}}))}var rh=Ne("<div><!></div>");const pu=Symbol("command-scope-node");function sh(){return J(pu)}function em(e){const t=[];let n=e;for(;n;)t.push(n.value),n=n.parent??void 0;return t.reverse()}function ah(e,t){pe(t,!0);const n=sh()??null,r=function(i,o){const l={value:i,parent:o,children:new Set};return o&&o.children.add(l),l}(t.scope,n);(function(i){Rs(pu,i)})(r),Iu(()=>{r.value=t.scope}),Nu(()=>{(function(i){i.parent&&i.parent.children.delete(i),i.parent=null})(r)});var s=Me(),a=ke(s);Cu(a,()=>t.children??xu),j(e,s),he()}function Xi(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);pe(t,!1);let s=F(t,"defaultOpen",24,()=>{}),a=F(t,"open",24,()=>{}),i=F(t,"onOpenChange",24,()=>{}),o=F(t,"delayDurationMs",24,()=>{}),l=F(t,"nested",24,()=>{}),u=F(t,"onHoverStart",8,()=>{}),p=F(t,"onHoverEnd",8,()=>{}),c=F(t,"triggerOn",24,()=>[hn.Click]),d=rt();const h=()=>{var A;return(A=H(d))==null?void 0:A.requestOpen()},m=()=>{var A;return(A=H(d))==null?void 0:A.requestClose()},g=A=>b.focusIdx(A),f=A=>b.setFocusedIdx(A),_=()=>b.getCurrentFocusedIdx(),S=J(ie.CONTEXT_KEY),b=new ie(S);Rs(ie.CONTEXT_KEY,b);const N=b.focusedIndex;return be(),Ru(qu(e,et({get defaultOpen(){return s()},get open(){return a()},onOpenChange:function(A){var L;b.handleOpenChange(A),(L=i())==null||L(A)},get delayDurationMs(){return o()},onHoverStart:u(),onHoverEnd:p(),get triggerOn(){return c()},get nested(){return l()}},()=>r,{children:(A,L)=>{ah(A,{scope:"dropdown-menu",children:(M,ne)=>{var $=Me(),V=ke($);ue(V,t,"default",{},null),j(M,$)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0})),A=>nt(d,A),()=>H(d)),dt(t,"requestOpen",h),dt(t,"requestClose",m),dt(t,"focusIdx",g),dt(t,"setFocusedIdx",f),dt(t,"getCurrentFocusedIdx",_),dt(t,"focusedIndex",N),he({requestOpen:h,requestClose:m,focusIdx:g,setFocusedIdx:f,getCurrentFocusedIdx:_,focusedIndex:N})}var ih=Ne("<div></div>");function oh(e,t){let n=F(t,"size",3,1),r=F(t,"orientation",3,"horizontal"),s=F(t,"useCurrentColor",3,!1),a=F(t,"class",3,"");var i=ih();let o;Lt(l=>o=Dt(i,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${r()} ${a()}`,"svelte-o0csoy",o,l),[()=>({"c-separator--current-color":s()})]),j(e,i)}var uh=Ne("<div><!></div>"),lh=Ne("<div><!></div>"),ch=Ne("<div><!></div>");const tm={BreadcrumbBackItem:function(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,[]);xs(e,et({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>r,{children:(s,a)=>{var i=Me(),o=ke(i);ue(o,t,"default",{},null),j(s,i)},$$slots:{default:!0,iconLeft:(s,a)=>{Wp(s)}}}))},BreadcrumbItem:zi,Content:Wi,Item:xs,Label:function(e,t){pe(t,!1);const[n,r]=Ge(),s=()=>Ee(i,"$sizeState",n),a=rt(),i=J(vt).size;Ye(()=>s(),()=>{nt(a,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${s()}`].join(" "))}),Mt(),be();var o=rh(),l=Le(o);Qi(l,{get size(){return s()},weight:"regular",children:(u,p)=>{var c=Me(),d=ke(c);ue(d,t,"default",{},null),j(u,c)},$$slots:{default:!0}}),Lt(()=>Dt(o,1,ra(H(a)),"svelte-gehsvg")),j(e,o),he(),r()},Root:Xi,Separator:function(e,t){pe(t,!1);const[n,r]=Ge(),s=J(vt).size;be();var a=uh();oh(Le(a),{size:4,orientation:"horizontal"}),Lt(()=>Dt(a,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${Ee(s,"$sizeState",n)}`,"svelte-24h9u")),j(e,a),he(),r()},Sub:function(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,[]);pe(t,!1),be();const s=Lr(()=>(_t(hn),En(()=>[hn.Click,hn.Hover])));Xi(e,et({nested:!0,get triggerOn(){return H(s)}},()=>r,{children:(a,i)=>{var o=Me(),l=ke(o);ue(l,t,"default",{},null),j(a,o)},$$slots:{default:!0}})),he()},SubContent:function(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,[]);pe(t,!1);const[s,a]=Ge(),i=()=>Ee(p,"$didOpen",s),o=J(vt).size,l=J(ie.CONTEXT_KEY),u=J(pn.CONTEXT_KEY),p=Au(u.state,c=>c.open);Ye(()=>(i(),Mr),()=>{i()&&Mr().then(()=>l==null?void 0:l.focusIdx(0))}),Ye(()=>i(),()=>{!i()&&(l==null||l.popNestedFocus())}),Mt(),be(),Wi(e,et(()=>r,{side:"right",align:"start",get size(){return Ee(o,"$sizeState",s)},children:(c,d)=>{var h=Me(),m=ke(h);ue(m,t,"default",{},null),j(c,h)},$$slots:{default:!0}})),he(),a()},SubTrigger:function(e,t){pe(t,!1);const[n,r]=Ge(),s=J(pn.CONTEXT_KEY).state;be(),sa(e,{children:(a,i)=>{zi(a,{get highlight(){return Ee(s,"$stateStore",n).open},children:(o,l)=>{var u=Me(),p=ke(u);ue(p,t,"default",{},null),j(o,u)},$$slots:{default:!0}})},$$slots:{default:!0}}),he(),r()},TextFieldItem:function(e,t){const n=Q(t,["children","$$slots","$$events","$$legacy"]),r=Q(n,["value"]);pe(t,!1);const[s,a]=Ge(),i=()=>Ee(u,"$sizeState",s),o=rt();let l=F(t,"value",12,"");const u=J(vt).size;Ye(()=>i(),()=>{nt(o,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${i()}`].join(" "))}),Mt(),be();var p=lh(),c=Le(p);Vu(c,et({get class(){return _t(ie),En(()=>ie.ITEM_CLASS)},get size(){return i()}},()=>r,{get value(){return l()},set value(d){l(d)},$$legacy:!0})),Lt(()=>Dt(p,1,ra(H(o)),"svelte-1xu00bc")),j(e,p),he(),a()},Trigger:function(e,t){pe(t,!1);const[n,r]=Ge(),s=()=>Ee(l,"$openState",n);let a=F(t,"referenceClientRect",24,()=>{});const i=J(ie.CONTEXT_KEY),o=J(pn.CONTEXT_KEY),l=o.state;be(),sa(e,{get referenceClientRect(){return a()},$$events:{keydown:async u=>{switch(u.key){case"ArrowUp":u.preventDefault(),u.stopPropagation(),s().open||await i.clickFocusedItem(),i==null||i.focusIdx(-1);break;case"ArrowDown":u.preventDefault(),u.stopPropagation(),s().open||await i.clickFocusedItem(),i==null||i.focusIdx(0);break;case"Enter":u.preventDefault(),u.stopPropagation(),i==null||i.clickFocusedItem()}}},children:(u,p)=>{var c=ch(),d=Le(c);ue(d,t,"default",{},null),Dr(c,h=>{var m;return(m=i.registerTrigger)==null?void 0:m.call(i,h)}),Dr(c,h=>{var m;return(m=o.registerTrigger)==null?void 0:m.call(o,h)}),j(u,c)},$$slots:{default:!0}}),he(),r()}};export{Zh as $,Dc as A,rd as B,jl as C,tm as D,Jh as E,Rc as F,Oh as G,Uh as H,ad as I,id as J,xs as K,Rd as L,Ac as M,Bh as N,$h as O,Rh as P,Dh as Q,Qh as R,Bc as S,Pd as T,Lh as U,Ih as V,Fh as W,Ah as X,Md as Y,an as Z,sn as _,Zr as a,Kh as a0,Ti as a1,Hh as a2,jh as a3,Gh as a4,Vh as a5,qh as a6,Yh as a7,nh as a8,Oc as a9,Go as aA,Sh as aB,Eh as aC,Ys as aD,wc as aE,Th as aF,kh as aG,jc as aa,Gc as ab,Kc as ac,Yc as ad,qc as ae,Vc as af,sh as ag,em as ah,ah as ai,oh as aj,Kp as ak,Ho as al,Lc as am,Mc as an,Ch as ao,Ph as ap,Zc as aq,Hc as ar,ae as as,Wp as at,zc as au,Wc as av,du as aw,Zo as ax,Uc as ay,Tc as az,Ic as b,Nc as c,$c as d,Y as e,Ad as f,$l as g,xc as h,Cc as i,Nh as j,Od as k,Wh as l,Fc as m,zh as n,Bl as o,sd as p,Xh as q,Mh as r,wh as s,Xc as t,Jc as u,Qc as v,ed as w,xh as x,td as y,nd as z};
