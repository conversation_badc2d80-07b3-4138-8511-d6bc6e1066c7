function S(){}function G(e,n,t,r,a){for(var o,l=[];n;)l.push(n),o=n.previousComponent,delete n.previousComponent,n=o;l.reverse();for(var i=0,s=l.length,u=0,c=0;i<s;i++){var f=l[i];if(f.removed)f.value=e.join(r.slice(c,c+f.count)),c+=f.count;else{if(!f.added&&a){var h=t.slice(u,u+f.count);h=h.map(function(p,d){var g=r[c+d];return g.length>p.length?g:p}),f.value=e.join(h)}else f.value=e.join(t.slice(u,u+f.count));u+=f.count,f.added||(c+=f.count)}}return l}function K(e,n){var t;for(t=0;t<e.length&&t<n.length;t++)if(e[t]!=n[t])return e.slice(0,t);return e.slice(0,t)}function Q(e,n){var t;if(!e||!n||e[e.length-1]!=n[n.length-1])return"";for(t=0;t<e.length&&t<n.length;t++)if(e[e.length-(t+1)]!=n[n.length-(t+1)])return e.slice(-t);return e.slice(-t)}function q(e,n,t){if(e.slice(0,n.length)!=n)throw Error("string ".concat(JSON.stringify(e)," doesn't start with prefix ").concat(JSON.stringify(n),"; this is a bug"));return t+e.slice(n.length)}function M(e,n,t){if(!n)return e+t;if(e.slice(-n.length)!=n)throw Error("string ".concat(JSON.stringify(e)," doesn't end with suffix ").concat(JSON.stringify(n),"; this is a bug"));return e.slice(0,-n.length)+t}function L(e,n){return q(e,n,"")}function N(e,n){return M(e,n,"")}function V(e,n){return n.slice(0,function(t,r){var a=0;t.length>r.length&&(a=t.length-r.length);var o=r.length;t.length<r.length&&(o=t.length);var l=Array(o),i=0;l[0]=0;for(var s=1;s<o;s++){for(r[s]==r[i]?l[s]=l[i]:l[s]=i;i>0&&r[s]!=r[i];)i=l[i];r[s]==r[i]&&i++}i=0;for(var u=a;u<t.length;u++){for(;i>0&&t[u]!=r[i];)i=l[i];t[u]==r[i]&&i++}return i}(e,n))}S.prototype={diff:function(e,n){var t,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=r.callback;typeof r=="function"&&(a=r,r={});var o=this;function l(v){return v=o.postProcess(v,r),a?(setTimeout(function(){a(v)},0),!0):v}e=this.castInput(e,r),n=this.castInput(n,r),e=this.removeEmpty(this.tokenize(e,r));var i=(n=this.removeEmpty(this.tokenize(n,r))).length,s=e.length,u=1,c=i+s;r.maxEditLength!=null&&(c=Math.min(c,r.maxEditLength));var f=(t=r.timeout)!==null&&t!==void 0?t:1/0,h=Date.now()+f,p=[{oldPos:-1,lastComponent:void 0}],d=this.extractCommon(p[0],n,e,0,r);if(p[0].oldPos+1>=s&&d+1>=i)return l(G(o,p[0].lastComponent,n,e,o.useLongestToken));var g=-1/0,C=1/0;function k(){for(var v=Math.max(g,-u);v<=Math.min(C,u);v+=2){var b=void 0,w=p[v-1],P=p[v+1];w&&(p[v-1]=void 0);var y=!1;if(P){var m=P.oldPos-v;y=P&&0<=m&&m<i}var j=w&&w.oldPos+1<s;if(y||j){if(b=!j||y&&w.oldPos<P.oldPos?o.addToPath(P,!0,!1,0,r):o.addToPath(w,!1,!0,1,r),d=o.extractCommon(b,n,e,v,r),b.oldPos+1>=s&&d+1>=i)return l(G(o,b.lastComponent,n,e,o.useLongestToken));p[v]=b,b.oldPos+1>=s&&(C=Math.min(C,v-1)),d+1>=i&&(g=Math.max(g,v+1))}else p[v]=void 0}u++}if(a)(function v(){setTimeout(function(){if(u>c||Date.now()>h)return a();k()||v()},0)})();else for(;u<=c&&Date.now()<=h;){var O=k();if(O)return O}},addToPath:function(e,n,t,r,a){var o=e.lastComponent;return o&&!a.oneChangePerToken&&o.added===n&&o.removed===t?{oldPos:e.oldPos+r,lastComponent:{count:o.count+1,added:n,removed:t,previousComponent:o.previousComponent}}:{oldPos:e.oldPos+r,lastComponent:{count:1,added:n,removed:t,previousComponent:o}}},extractCommon:function(e,n,t,r,a){for(var o=n.length,l=t.length,i=e.oldPos,s=i-r,u=0;s+1<o&&i+1<l&&this.equals(t[i+1],n[s+1],a);)s++,i++,u++,a.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return u&&!a.oneChangePerToken&&(e.lastComponent={count:u,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=i,s},equals:function(e,n,t){return t.comparator?t.comparator(e,n):e===n||t.ignoreCase&&e.toLowerCase()===n.toLowerCase()},removeEmpty:function(e){for(var n=[],t=0;t<e.length;t++)e[t]&&n.push(e[t]);return n},castInput:function(e){return e},tokenize:function(e){return Array.from(e)},join:function(e){return e.join("")},postProcess:function(e){return e}};var F="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",ln=new RegExp("[".concat(F,"]+|\\s+|[^").concat(F,"]"),"ug"),A=new S;function X(e,n,t,r){if(n&&t){var a=n.value.match(/^\s*/)[0],o=n.value.match(/\s*$/)[0],l=t.value.match(/^\s*/)[0],i=t.value.match(/\s*$/)[0];if(e){var s=K(a,l);e.value=M(e.value,l,s),n.value=L(n.value,s),t.value=L(t.value,s)}if(r){var u=Q(o,i);r.value=q(r.value,i,u),n.value=N(n.value,u),t.value=N(t.value,u)}}else if(t)e&&(t.value=t.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(e&&r){var c=r.value.match(/^\s*/)[0],f=n.value.match(/^\s*/)[0],h=n.value.match(/\s*$/)[0],p=K(c,f);n.value=L(n.value,p);var d=Q(L(c,p),h);n.value=N(n.value,d),r.value=q(r.value,c,d),e.value=M(e.value,c,c.slice(0,c.length-d.length))}else if(r){var g=r.value.match(/^\s*/)[0],C=V(n.value.match(/\s*$/)[0],g);n.value=N(n.value,C)}else if(e){var k=V(e.value.match(/\s*$/)[0],n.value.match(/^\s*/)[0]);n.value=L(n.value,k)}}A.equals=function(e,n,t){return t.ignoreCase&&(e=e.toLowerCase(),n=n.toLowerCase()),e.trim()===n.trim()},A.tokenize=function(e){var n,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t.intlSegmenter){if(t.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');n=Array.from(t.intlSegmenter.segment(e),function(o){return o.segment})}else n=e.match(ln)||[];var r=[],a=null;return n.forEach(function(o){/\s/.test(o)?a==null?r.push(o):r.push(r.pop()+o):/\s/.test(a)?r[r.length-1]==a?r.push(r.pop()+o):r.push(a+o):r.push(o),a=o}),r},A.join=function(e){return e.map(function(n,t){return t==0?n:n.replace(/^\s+/,"")}).join("")},A.postProcess=function(e,n){if(!e||n.oneChangePerToken)return e;var t=null,r=null,a=null;return e.forEach(function(o){o.added?r=o:o.removed?a=o:((r||a)&&X(t,a,r,o),t=o,r=null,a=null)}),(r||a)&&X(t,a,r,null),e},new S().tokenize=function(e){var n=new RegExp("(\\r?\\n)|[".concat(F,"]+|[^\\S\\n\\r]+|[^").concat(F,"]"),"ug");return e.match(n)||[]};var I=new S;function Y(e,n,t){return I.diff(e,n,t)}function nn(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function z(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?nn(Object(t),!0).forEach(function(r){an(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):nn(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function un(e){var n=function(t,r){if(typeof t!="object"||!t)return t;var a=t[Symbol.toPrimitive];if(a!==void 0){var o=a.call(t,r);if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(t)}(e,"string");return typeof n=="symbol"?n:n+""}function W(e){return W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},W(e)}function an(e,n,t){return(n=un(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function D(e){return function(n){if(Array.isArray(n))return H(n)}(e)||function(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}(e)||function(n,t){if(n){if(typeof n=="string")return H(n,t);var r=Object.prototype.toString.call(n).slice(8,-1);if(r==="Object"&&n.constructor&&(r=n.constructor.name),r==="Map"||r==="Set")return Array.from(n);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return H(n,t)}}(e)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function H(e,n){(n==null||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}I.tokenize=function(e,n){n.stripTrailingCr&&(e=e.replace(/\r\n/g,`
`));var t=[],r=e.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var a=0;a<r.length;a++){var o=r[a];a%2&&!n.newlineIsToken?t[t.length-1]+=o:t.push(o)}return t},I.equals=function(e,n,t){return t.ignoreWhitespace?(t.newlineIsToken&&e.includes(`
`)||(e=e.trim()),t.newlineIsToken&&n.includes(`
`)||(n=n.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(e.endsWith(`
`)&&(e=e.slice(0,-1)),n.endsWith(`
`)&&(n=n.slice(0,-1))),S.prototype.equals.call(this,e,n,t)},new S().tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)},new S().tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};var E=new S;function R(e,n,t,r,a){var o,l;for(n=n||[],t=t||[],r&&(e=r(a,e)),o=0;o<n.length;o+=1)if(n[o]===e)return t[o];if(Object.prototype.toString.call(e)==="[object Array]"){for(n.push(e),l=new Array(e.length),t.push(l),o=0;o<e.length;o+=1)l[o]=R(e[o],n,t,r,a);return n.pop(),t.pop(),l}if(e&&e.toJSON&&(e=e.toJSON()),W(e)==="object"&&e!==null){n.push(e),l={},t.push(l);var i,s=[];for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&s.push(i);for(s.sort(),o=0;o<s.length;o+=1)l[i=s[o]]=R(e[i],n,t,r,i);n.pop(),t.pop()}else l=e;return l}E.useLongestToken=!0,E.tokenize=I.tokenize,E.castInput=function(e,n){var t=n.undefinedReplacement,r=n.stringifyReplacer,a=r===void 0?function(o,l){return l===void 0?t:l}:r;return typeof e=="string"?e:JSON.stringify(R(e,null,null,a),a,"  ")},E.equals=function(e,n,t){return S.prototype.equals.call(E,e.replace(/,([\r\n])/g,"$1"),n.replace(/,([\r\n])/g,"$1"),t)};var J=new S;function sn(e){var n=e.split(/\n/),t=[],r=0;function a(){var i={};for(t.push(i);r<n.length;){var s=n[r];if(/^(\-\-\-|\+\+\+|@@)\s/.test(s))break;var u=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(s);u&&(i.index=u[1]),r++}for(o(i),o(i),i.hunks=[];r<n.length;){var c=n[r];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(l());else{if(c)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(c));r++}}}function o(i){var s=/^(---|\+\+\+)\s+(.*)\r?$/.exec(n[r]);if(s){var u=s[1]==="---"?"old":"new",c=s[2].split("	",2),f=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(f)&&(f=f.substr(1,f.length-2)),i[u+"FileName"]=f,i[u+"Header"]=(c[1]||"").trim(),r++}}function l(){var i=r,s=n[r++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),u={oldStart:+s[1],oldLines:s[2]===void 0?1:+s[2],newStart:+s[3],newLines:s[4]===void 0?1:+s[4],lines:[]};u.oldLines===0&&(u.oldStart+=1),u.newLines===0&&(u.newStart+=1);for(var c=0,f=0;r<n.length&&(f<u.oldLines||c<u.newLines||(h=n[r])!==null&&h!==void 0&&h.startsWith("\\"));r++){var h,p=n[r].length==0&&r!=n.length-1?" ":n[r][0];if(p!=="+"&&p!=="-"&&p!==" "&&p!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(n[r]));u.lines.push(n[r]),p==="+"?c++:p==="-"?f++:p===" "&&(c++,f++)}if(c||u.newLines!==1||(u.newLines=0),f||u.oldLines!==1||(u.oldLines=0),c!==u.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(f!==u.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return u}for(;r<n.length;)a();return t}function en(e,n,t,r,a,o,l){if(l||(l={}),typeof l=="function"&&(l={callback:l}),l.context===void 0&&(l.context=4),l.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!l.callback)return s(Y(t,r,l));var i=l.callback;function s(u){if(u){u.push({value:"",lines:[]});for(var c=[],f=0,h=0,p=[],d=1,g=1,C=function(){var y=u[k],m=y.lines||function(x){var rn=x.endsWith(`
`),T=x.split(`
`).map(function(on){return on+`
`});return rn?T.pop():T.push(T.pop().slice(0,-1)),T}(y.value);if(y.lines=m,y.added||y.removed){var j;if(!f){var Z=u[k-1];f=d,h=g,Z&&(p=l.context>0?P(Z.lines.slice(-l.context)):[],f-=p.length,h-=p.length)}(j=p).push.apply(j,D(m.map(function(x){return(y.added?"+":"-")+x}))),y.added?g+=m.length:d+=m.length}else{if(f)if(m.length<=2*l.context&&k<u.length-2){var _;(_=p).push.apply(_,D(P(m)))}else{var B,$=Math.min(m.length,l.context);(B=p).push.apply(B,D(P(m.slice(0,$))));var tn={oldStart:f,oldLines:d-f+$,newStart:h,newLines:g-h+$,lines:p};c.push(tn),f=0,h=0,p=[]}d+=m.length,g+=m.length}},k=0;k<u.length;k++)C();for(var O=0,v=c;O<v.length;O++)for(var b=v[O],w=0;w<b.lines.length;w++)b.lines[w].endsWith(`
`)?b.lines[w]=b.lines[w].slice(0,-1):(b.lines.splice(w+1,0,"\\ No newline at end of file"),w++);return{oldFileName:e,newFileName:n,oldHeader:a,newHeader:o,hunks:c}}function P(y){return y.map(function(m){return" "+m})}}Y(t,r,z(z({},l),{},{callback:function(u){var c=s(u);i(c)}}))}function U(e){if(Array.isArray(e))return e.map(U).join(`
`);var n=[];e.oldFileName==e.newFileName&&n.push("Index: "+e.oldFileName),n.push("==================================================================="),n.push("--- "+e.oldFileName+(e.oldHeader===void 0?"":"	"+e.oldHeader)),n.push("+++ "+e.newFileName+(e.newHeader===void 0?"":"	"+e.newHeader));for(var t=0;t<e.hunks.length;t++){var r=e.hunks[t];r.oldLines===0&&(r.oldStart-=1),r.newLines===0&&(r.newStart-=1),n.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),n.push.apply(n,r.lines)}return n.join(`
`)+`
`}function cn(e,n,t,r,a,o,l){var i;if(typeof l=="function"&&(l={callback:l}),(i=l)===null||i===void 0||!i.callback){var s=en(e,n,t,r,a,o,l);return s?U(s):void 0}var u=l.callback;en(e,n,t,r,a,o,z(z({},l),{},{callback:function(c){c?u(U(c)):u()}}))}J.tokenize=function(e){return e.slice()},J.join=J.removeEmpty=function(e){return e};export{cn as c,sn as p};
