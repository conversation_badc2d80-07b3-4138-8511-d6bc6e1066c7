.c-textarea-label.svelte-hohluk{font-size:var(--text-area-label-font-size, var(--ds-font-size-1));display:block;text-align:left}.c-textarea-label-container.svelte-hohluk{display:flex;justify-content:space-between;margin-bottom:var(--ds-spacing-1)}.c-textarea.svelte-hohluk .c-textarea__input.c-base-text-input__input:where(.svelte-hohluk){font-size:var(--text-area-font-size, var(--ds-font-size-1));text-indent:unset;padding:var(--base-text-field-input-padding);min-height:var(--text-area-min-height-rows);max-height:var(--text-area-max-height, auto);overflow-y:auto}.c-textarea.svelte-hohluk .c-textarea--autoresize:where(.svelte-hohluk){field-sizing:content}.c-textarea.svelte-hohluk .c-textarea--resize-none:where(.svelte-hohluk){resize:none}.c-textarea.svelte-hohluk .c-textarea--resize-both:where(.svelte-hohluk){resize:both}.c-textarea.svelte-hohluk .c-textarea--resize-horizontal:where(.svelte-hohluk){resize:horizontal}.c-textarea.svelte-hohluk .c-textarea--resize-vertical:where(.svelte-hohluk){resize:vertical}.c-textarea.svelte-hohluk .c-base-text-input{--base-text-field-height: auto}
