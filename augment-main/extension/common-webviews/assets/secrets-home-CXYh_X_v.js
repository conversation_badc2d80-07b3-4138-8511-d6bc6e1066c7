import{N as xe,x as ce,y as h,a as Ze,r as Ke,u as d,S as Xe,V as ot,$ as Ae,C as B,a0 as Mt,b as r,X as Se,z as X,G as Ne,H as le,at as Vt,ag as T,ah as Lt,Z as Et,ac as t,P as e,R as k,B as o,T as C,a7 as w,a9 as Dt,a8 as $e,a1 as P,aj as U,ae as Ot,b4 as Ut,ax as Ht,W as ke,E as Zt,aP as Kt}from"./GuardedIcon-BFT2yJIo.js";import"./design-system-init-K1OaxmPU.js";import{T as Xt}from"./TextCombo-CTaxEruE.js";import{I as _t,c as Gt}from"./IconButtonAugment-CR0fVrwD.js";import{M as Yt}from"./message-broker-BygIEqPd.js";import{B as Ft}from"./index-BuQDLh4_.js";import{B as je}from"./ButtonAugment-CWDjQYWT.js";import{M as Pt}from"./ModalAugment-xq6pa268.js";import{T as He}from"./TextFieldAugment-DfCJMerV.js";import{T as pt,a as Jt}from"./CardAugment-DVbbQqkH.js";import{X as jt,M as Qt,C as er,F as tr}from"./clock-B1EQOiug.js";import{P as rr}from"./pen-to-square-Bn06n7i7.js";import{R as ar,T as sr}from"./trash-ByjpApta.js";import{C as lr,a as nr}from"./CheckboxAugment-BA-zj2xS.js";import{T as cr}from"./TextAreaAugment-DIwZ8JEM.js";import{P as or}from"./plus-BGQoI3Yl.js";import"./async-messaging-Bp70swAv.js";import"./focusTrapStack-CDv9v5kQ.js";import"./BaseTextInput-BaUpeUef.js";var ir=["second","minute","hour","day","week","month","year"],dr=["秒","分钟","小时","天","周","个月","年"],Tt={},At=function(b,s){Tt[b]=s},Ct=[60,60,24,7,365/7/12,12];function vr(b,s){var n;return(+new Date-+((n=b)instanceof Date?n:!isNaN(n)||/^\d+$/.test(n)?new Date(parseInt(n)):(n=(n||"").trim().replace(/\.\d+/,"").replace(/-/,"/").replace(/-/,"/").replace(/(\d)T(\d)/,"$1 $2").replace(/Z/," UTC").replace(/([+-]\d\d):?(\d\d)/," $1$2"),new Date(n))))/1e3}var ur=function(b,s,n){return function(v,u){for(var m=v<0?1:0,q=v=Math.abs(v),p=0;v>=Ct[p]&&p<Ct.length;p++)v/=Ct[p];return(v=Math.floor(v))>((p*=2)==0?9:1)&&(p+=1),u(v,p,q)[m].replace("%s",v.toString())}(vr(b),function(v){return Tt[v]||Tt.en_US}(s))};At("en_US",function(b,s){if(s===0)return["just now","right now"];var n=ir[Math.floor(s/2)];return b>1&&(n+="s"),[b+" "+n+" ago","in "+b+" "+n]}),At("zh_CN",function(b,s){if(s===0)return["刚刚","片刻后"];var n=dr[~~(s/2)];return[b+" "+n+"前",b+" "+n+"后"]});var fr=h('<div class="c-table-augment svelte-ykxg03"><table><!></table></div>'),mr=h("<tbody><!></tbody>"),$r=h("<td><!></td>"),gr=h("<th><!></th>"),hr=h("<thead><!></thead>"),pr=h("<tr><!></tr>");const _r=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=ce(s,"size",3,"2"),u=ce(s,"variant",3,"default"),m=Ke(s,["$$slots","$$events","$$legacy","ref","class","size","variant","children"]);var q=fr(),p=d(q);Ze(p,()=>({class:["c-table-augment__table",s.class],"data-table-augment-size":v(),...m}),void 0,"svelte-ykxg03");var R=d(p);Xe(R,()=>s.children??ot),Ae(p,N=>n(N),()=>n()),B(()=>{Mt(q,"data-table-augment-size",v()),Mt(q,"data-table-augment-variant",u())}),r(b,q),Se()},wr=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=Ke(s,["$$slots","$$events","$$legacy","ref","class","children"]);var u=mr();Ze(u,()=>({class:["c-table-augment__body",s.class],...v}),void 0,"svelte-1j9w6fc");var m=d(u);Xe(m,()=>s.children??ot),Ae(u,q=>n(q),()=>n()),r(b,u),Se()},nt=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=Ke(s,["$$slots","$$events","$$legacy","ref","class","children"]);var u=$r();Ze(u,()=>({class:["c-table-augment__cell",s.class],...v}),void 0,"svelte-iqktoq");var m=d(u);Xe(m,()=>s.children??ot),Ae(u,q=>n(q),()=>n()),r(b,u),Se()},ct=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=Ke(s,["$$slots","$$events","$$legacy","ref","class","children"]);var u=gr();Ze(u,()=>({class:["c-table-augment__head-cell",s.class],...v}),void 0,"svelte-1dnajj6");var m=d(u),q=p=>{var R=Ne(),N=le(R);Xe(N,()=>s.children),r(p,R)};X(m,p=>{s.children&&p(q)}),Ae(u,p=>n(p),()=>n()),r(b,u),Se()},yr=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=Ke(s,["$$slots","$$events","$$legacy","ref","class","children"]);var u=hr();Ze(u,()=>({class:["c-table-augment__header",s.class],...v}),void 0,"svelte-1y58d4y");var m=d(u);Xe(m,()=>s.children??ot),Ae(u,q=>n(q),()=>n()),r(b,u),Se()},It=function(b,s){xe(s,!0);let n=ce(s,"ref",15,null),v=Ke(s,["$$slots","$$events","$$legacy","ref","class","children"]);var u=pr();Ze(u,()=>({class:["c-table-augment__row",s.class],...v}),void 0,"svelte-ym8a5u");var m=d(u);Xe(m,()=>s.children??ot),Ae(u,q=>n(q),()=>n()),r(b,u),Se()};var br=h("<strong>Error:</strong> ",1),zr=h('<div class="c-secret-form-modal__error svelte-1qrz6vr"><!></div>'),kr=h('<span class="c-secret-form-modal__required svelte-1qrz6vr">*</span>'),xr=h("Secret Name <!>",1),Sr=h('<div class="c-secret-form-modal__file-info"><!> <!></div>'),qr=h("<div>Upload a file to use its content as the secret value</div>"),Er=h('<input type="file" hidden/> Select file',1),Cr=h('<div class="c-secret-form-modal__file-upload"><!></div>'),Mr=h('<div class="c-secret-form-modal__mount-path svelte-1qrz6vr"><!></div>'),Dr=h('<div class="c-secret-form-modal__body svelte-1qrz6vr"><!> <div class="c-secret-form-modal__form svelte-1qrz6vr"><div class="c-secret-form-modal__field svelte-1qrz6vr"><!> <!></div> <div class="c-secret-form-modal__field svelte-1qrz6vr"><div class="c-secret-form-modal__value-section"><!></div></div> <div class="c-secret-form-modal__field svelte-1qrz6vr"><div class="c-secret-form-modal__file-controls"><!></div></div> <!> <div class="c-secret-form-modal__field svelte-1qrz6vr"><!> <!></div> <div class="c-secret-form-modal__field svelte-1qrz6vr"><!> <div class="c-secret-form-modal__tags svelte-1qrz6vr"><!> <!></div> <div class="c-secret-form-modal__tag-item svelte-1qrz6vr"><!> <!> <!> <!></div></div> <div class="c-secret-form-modal__field svelte-1qrz6vr"><div class="c-secret-form-modal__mount svelte-1qrz6vr"><div class="c-secret-form-modal__mount-checkbox svelte-1qrz6vr"><!> <!></div> <!></div></div></div></div>'),Fr=h('<div class="c-secret-form-modal__footer svelte-1qrz6vr"><!> <!></div>');function Tr(b,s){xe(s,!0);let n=ce(s,"show",3,!1),v=ce(s,"mode",3,"create"),u=ce(s,"secret",3,null),m=ce(s,"submitting",3,!1),q=ce(s,"error",3,null),p=T(""),R=T(""),N=T(""),H=T(Lt(new Map)),J=T(!1),Q=T(""),G=T(""),me=T(""),ge=T(null);const it=`secret-file-${Date.now()}-${Math.floor(1e4*Math.random())}`;let Ie=T(null),ee=T(null),W=T(null),qe=k(()=>function(y){return y.length>1024?"Description must be 1024 characters or less":null}(e(N))),Ee=k(()=>{return y=e(R),new Blob([y]).size<=16384?null:"Secret value is too larger than 16KB";var y});const dt=/^[A-Z0-9_]+$/;async function vt(y){var ne;const V=y.target,Z=(ne=V.files)==null?void 0:ne[0];if(Z){try{const te=await function(Ce){return new Promise((Me,de)=>{const a=new FileReader;a.onload=$=>{var g;const _=(g=$.target)==null?void 0:g.result;typeof _=="string"?Me(_):de(new Error("Failed to read file as text"))},a.onerror=()=>{de(new Error("Failed to read file"))},a.readAsText(Ce)})}(Z);t(R,te,!0),t(ge,Z.name,!0)}catch(te){console.error("Error reading file:",te)}V.value=""}}function ut(){t(ge,null),t(R,"")}function ft(y){if(v()==="create")if(y.trim())if(dt.test(y)){if(s.validateSecretName){const V=s.validateSecretName(y);if(V)return void t(ee,V,!0)}t(ee,null)}else t(ee,"Secret name can only contain capital letters, numbers, and underscores");else t(ee,"Secret name is required");else t(ee,null)}function Ge(y){y.trim()?e(H).has(y.trim())?t(W,`Tag "${y}" already exists`):y.trim().startsWith("augment:")?t(W,"Tags starting with 'augment:' are reserved"):t(W,null):t(W,null)}function wt(){e(G).trim()&&!e(W)&&(e(H).set(e(G).trim(),e(me)),t(H,new Map(e(H)),!0),t(G,""),t(me,""),t(W,null))}function Ye(){s.onCancel()}function yt(){const y={};for(const[V,Z]of e(H))y[V]=Z;if(e(J)&&e(Q).trim()&&(y["augment:mount_point"]=e(Q).trim()),v()==="edit"&&u())for(const[V,Z]of Object.entries(u().tags))V.startsWith("augment:")&&V!=="augment:mount_point"&&(y[V]=Z);s.onSubmit({name:e(p).trim(),value:e(R),description:e(N).trim(),tags:y})}Et(()=>{if(n()){if(v()==="edit"&&u()){t(p,u().name,!0),t(R,""),t(N,u().description||"",!0),t(H,function(V){const Z=new Map;for(const[ne,te]of Object.entries(V))ne.startsWith("augment:")||Z.set(ne,te);return Z}(u().tags),!0);const y=u().tags["augment:mount_point"]||null;t(J,!!y),t(Q,y||"",!0)}else v()==="create"&&(t(p,""),t(R,""),t(N,""),t(H,new Map,!0),t(J,!1),t(Q,""),t(ge,null));t(G,""),t(me,""),t(ee,null),t(W,null)}return()=>{n()||(t(p,""),t(R,""),t(N,""),t(H,new Map,!0),t(J,!1),t(Q,""),t(G,""),t(me,""),t(ee,null),t(W,null),t(ge,null))}}),Et(()=>{v()==="create"&&e(p)&&ft(e(p))}),Et(()=>{e(G)?Ge(e(G)):t(W,null)});let mt=k(()=>()=>!!m()||!e(p).trim()||!!e(ee)||!!e(Ee)||!!e(qe)),bt=k(()=>{var y;return v()==="create"?"Create New Secret":`Edit Secret: ${((y=u())==null?void 0:y.name)||""}`}),zt=k(()=>()=>m()?v()==="create"?"Creating...":"Updating...":v()==="create"?"Create Secret":"Update Secret");Pt(b,{get show(){return n()},get title(){return e(bt)},maxWidth:"600px",oncancel:Ye,get preventEscapeClose(){return m()},get preventBackdropClose(){return m()},body:Z=>{var ne=Dr(),te=d(ne),Ce=l=>{var c=zr(),f=d(c);C(f,{size:2,color:"error",children:(i,L)=>{var E=br(),z=o(le(E));B(()=>P(z,` ${q()??""}`)),r(i,E)},$$slots:{default:!0}}),r(l,c)};X(te,l=>{q()&&l(Ce)});var Me=o(te,2),de=d(Me),a=d(de);const $=k(()=>m()||v()==="edit"),_=k(()=>e(ee)?"error":void 0);He(a,{placeholder:"Enter secret name",get disabled(){return e($)},size:2,variant:"surface",get color(){return e(_)},get value(){return e(p)},$$events:{input:c=>{var f,i;v()==="create"&&(t(p,(i=(f=c==null?void 0:c.target)==null?void 0:f.value)==null?void 0:i.trim().toUpperCase(),!0),ft(e(p)))}},label:c=>{C(c,{size:2,weight:"medium",children:(f,i)=>{var L=xr(),E=o(le(L)),z=K=>{var D=kr();r(K,D)};X(E,K=>{v()==="create"&&K(z)}),r(f,L)},$$slots:{default:!0}})},$$slots:{label:!0}});var g=o(a,2),x=l=>{C(l,{size:1,color:"error",class:"c-secret-form-modal__error-text",children:(c,f)=>{var i=w();B(()=>P(i,e(ee))),r(c,i)},$$slots:{default:!0}})};X(g,l=>{e(ee)&&l(x)});var O=o(de,2),A=d(O),oe=d(A);const S=k(()=>v()==="create"?"Secret Value":"New Secret Value (leave empty to keep current)"),ve=k(()=>v()==="create"?"Enter secret value":"Enter new value or leave empty"),ie=k(()=>m()||!!e(ge)),kt=k(()=>e(Ee)?"error":void 0);cr(oe,{get label(){return e(S)},get placeholder(){return e(ve)},get disabled(){return e(ie)},size:2,rows:4,maxHeight:"10lh",variant:"surface",resize:"vertical",get color(){return e(kt)},get value(){return e(R)},set value(l){t(R,l,!0)}});var he=o(O,2),De=d(he),Je=d(De),Qe=l=>{var c=Sr(),f=d(c);C(f,{size:1,color:"neutral",children:(L,E)=>{var z=w();B(()=>P(z,`File: ${e(ge)??""}`)),r(L,z)},$$slots:{default:!0}});var i=o(f,2);_t(i,{size:1,variant:"ghost",color:"neutral",get disabled(){return m()},title:"Clear file",$$events:{click:ut},children:(L,E)=>{$e(L,{name:"x",children:(z,K)=>{jt(z,{})},$$slots:{default:!0}})},$$slots:{default:!0}}),r(l,c)},xt=l=>{var c=Cr(),f=d(c);const i=k(()=>[Jt.Hover]);pt(f,{get triggerOn(){return e(i)},delayDurationMs:300,content:E=>{var z=qr();r(E,z)},children:(E,z)=>{je(E,{get disabled(){return m()},title:"Upload File",size:.5,onclick:()=>{var D;return(D=e(Ie))==null?void 0:D.click()},iconLeft:D=>{$e(D,{name:"cloud-upload",children:(F,Y)=>{nr(F,{})},$$slots:{default:!0}})},children:(D,F)=>{var Y=Er(),j=le(Y);j.__change=vt,Ae(j,_e=>t(Ie,_e),()=>e(Ie)),B(()=>{Mt(j,"id",it),j.disabled=m()}),r(D,Y)},$$slots:{iconLeft:!0,default:!0}})},$$slots:{content:!0,default:!0}}),r(l,c)};X(Je,l=>{e(ge)?l(Qe):l(xt,!1)});var $t=o(he,2),Rt=l=>{C(l,{size:1,color:"error",class:"c-secret-form-modal__error-text",children:(c,f)=>{var i=w();B(()=>P(i,e(Ee))),r(c,i)},$$slots:{default:!0}})};X($t,l=>{e(Ee)&&l(Rt)});var et=o($t,2),tt=d(et);const St=k(()=>e(qe)?"error":void 0);He(tt,{placeholder:"Optional description",get disabled(){return m()},size:2,variant:"surface",get color(){return e(St)},get value(){return e(N)},set value(c){t(N,c,!0)},label:c=>{C(c,{size:2,weight:"medium",children:(f,i)=>{var L=w("Description");r(f,L)},$$slots:{default:!0}})},$$slots:{label:!0}});var rt=o(tt,2),at=l=>{C(l,{size:1,color:"error",class:"c-secret-form-modal__error-text",children:(c,f)=>{var i=w();B(()=>P(i,e(qe))),r(c,i)},$$slots:{default:!0}})};X(rt,l=>{e(qe)&&l(at)});var Le=o(et,2),gt=d(Le);C(gt,{size:2,weight:"medium",class:"c-secret-form-modal__label",children:(l,c)=>{var f=w("Tags");r(l,f)},$$slots:{default:!0}});var ze=o(gt,2),Oe=d(ze);Dt(Oe,17,()=>e(H),Ot,(l,c)=>{var f=k(()=>Ut(e(c),2));let i=()=>e(f)[0],L=()=>e(f)[1];var E=Ne(),z=le(E);U(z,()=>Ft.Root,(K,D)=>{D(K,{children:(F,Y)=>{var j=w();B(()=>P(j,`${i()??""}${L()?`:${L()}`:""}`)),r(F,j)},$$slots:{default:!0,rightButtons:(F,Y)=>{var j=Ne(),_e=le(j);U(_e,()=>Ft.IconButton,(we,ye)=>{ye(we,{slot:"rightButtons",$$events:{click:()=>function(be){e(H).delete(be),t(H,new Map(e(H)),!0)}(i())},children:(be,ht)=>{$e(be,{name:"x",children:(qt,Bt)=>{jt(qt,{})},$$slots:{default:!0}})},$$slots:{default:!0}})}),r(F,j)}}})}),r(l,E)});var Fe=o(Oe,2),I=l=>{C(l,{size:1,color:"error",class:"c-secret-form-modal__tag-error",children:(c,f)=>{var i=w();B(()=>P(i,e(W))),r(c,i)},$$slots:{default:!0}})};X(Fe,l=>{e(W)&&l(I)});var Ue=o(ze,2),st=d(Ue);const Pe=k(()=>e(W)?"error":void 0);He(st,{placeholder:"Tag key",get disabled(){return m()},size:1,variant:"surface",get color(){return e(Pe)},class:"c-secret-form-modal__tag-key",get value(){return e(G)},set value(l){t(G,l,!0)},$$events:{input:()=>Ge(e(G))}});var Te=o(st,2);C(Te,{size:2,class:"c-secret-form-modal__tag-separator",children:(l,c)=>{var f=w("=");r(l,f)},$$slots:{default:!0}});var Re=o(Te,2);He(Re,{placeholder:"Tag value",get disabled(){return m()},size:1,variant:"surface",class:"c-secret-form-modal__tag-value",get value(){return e(me)},set value(l){t(me,l,!0)}});var lt=o(Re,2);const Be=k(()=>m()||!e(G).trim()||!!e(W)),We=k(()=>e(W)||"Add tag"),re=k(()=>m()||!e(G).trim()||e(W)?"neutral":"accent");_t(lt,{get disabled(){return e(Be)},get title(){return e(We)},size:1,get color(){return e(re)},$$events:{click:wt},children:(l,c)=>{$e(l,{name:"plus",children:(f,i)=>{or(f,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var ae=o(Le,2),se=d(ae),pe=d(se),M=d(pe);lr(M,{get disabled(){return m()},size:2,get checked(){return e(J)},set checked(l){t(J,l,!0)}});var ue=o(M,2);C(ue,{size:2,weight:"medium",children:(l,c)=>{var f=w("Mount to Filesystem");r(l,f)},$$slots:{default:!0}});var fe=o(pe,2),Ve=l=>{var c=Mr(),f=d(c);He(f,{placeholder:"Enter mount path (e.g., /tmp/secret)",get disabled(){return m()},size:2,variant:"surface",get value(){return e(Q)},set value(i){t(Q,i,!0)}}),r(l,c)};X(fe,l=>{e(J)&&l(Ve)}),r(Z,ne)},footer:Z=>{var ne=Fr(),te=d(ne);je(te,{get disabled(){return m()},variant:"ghost",size:2,$$events:{click:Ye},children:(de,a)=>{var $=w("Cancel");r(de,$)},$$slots:{default:!0}});var Ce=o(te,2);const Me=k(()=>e(mt)());je(Ce,{get disabled(){return e(Me)},variant:"solid",color:"accent",size:2,$$events:{click:yt},children:(de,a)=>{var $=w();B(_=>P($,_),[()=>e(zt)()]),r(de,$)},$$slots:{default:!0}}),r(Z,ne)},$$slots:{body:!0,footer:!0}}),Se()}Vt(["change"]);var Rr=h("<div><!></div>"),Br=h('<div class="c-loading-state svelte-wkglxe"><div>Loading secrets...</div></div>'),Nr=h('<div class="c-error-state svelte-wkglxe"><div class="c-error-state__title svelte-wkglxe">Error Loading Secrets</div> <div class="c-error-state__description svelte-wkglxe"> </div></div>'),jr=h(`<div class="c-empty-state svelte-wkglxe"><div class="c-empty-state__title svelte-wkglxe">No Secrets Found</div> <div class="c-empty-state__description svelte-wkglxe">You haven't created any secrets yet. Secrets allow you to securely store sensitive
          information like API keys, passwords, and tokens that can be used by remote agents and
          other Augment features.</div></div>`),Ar=h('<div class="c-empty-state svelte-wkglxe"><div class="c-empty-state__title svelte-wkglxe">No Matching Secrets</div> <div class="c-empty-state__description svelte-wkglxe"> </div></div>'),Ir=h("<!> <!> <!> <!> <!>",1),Lr=h("<div><!></div>"),Or=h('<div class="c-secret-tags svelte-wkglxe"></div>'),Ur=h('<div class="c-mount-path svelte-wkglxe"><!> <!></div>'),Pr=h('<div class="c-secret-details svelte-wkglxe"><div><!></div> <!> <!> <!></div>'),Wr=h('<span slot="leftIcon" class="c-clock-icon svelte-wkglxe"><!></span>'),Vr=h('<div class="c-table-actions svelte-wkglxe"><!> <!></div>'),Hr=h("<!> <!> <!> <!> <!>",1),Zr=h("<!> <!>",1),Kr=h('<div class="c-secrets-table svelte-wkglxe"><!></div>'),Xr=h('<div class="c-delete-modal__body svelte-wkglxe"><!> <!> <!> <!></div>'),Gr=h('<div class="c-delete-modal__footer svelte-wkglxe"><!> <!></div>'),Yr=h('<div class="l-secrets-panel svelte-wkglxe"><div class="c-secrets-header svelte-wkglxe"><!> <div class="c-secrets-header__title svelte-wkglxe"><!> <div class="c-secrets-header__actions svelte-wkglxe"><!> <!></div></div></div> <div class="c-secrets-search svelte-wkglxe"><div class="c-secrets-search__field svelte-wkglxe"><!></div></div> <div class="c-secrets-content svelte-wkglxe"><!></div></div> <!> <!>',1);Kt(function(b,s){xe(s,!0);let n=T(Lt([])),v=T(!0),u=T(null),m=T(null),q=T(""),p=T(!1),R=T("create"),N=T(null),H=T(!1),J=T(null);const Q=new Yt(Gt);function G(){t(v,!0),t(u,null),Q.send({type:ke.listSecretsRequest,data:{}},1e4).then(a=>{var $;$=a.data,t(v,!1),t(n,$.secrets,!0),e(n).length===0&&t(u,null)}).catch(a=>{t(v,!1),a.name==="MessageTimeout"?t(u,"Request timed out. Please try again."):t(u,`Failed to load secrets: ${a.message||a}`),console.error("Failed to load secrets:",a)})}function me(){t(p,!1),t(N,null),t(J,null)}function ge(a){return e(n).some($=>$.name===a)?`A secret with the name "${a}" already exists`:null}function it(){t(m,null)}function Ie(a){const $={};for(const[_,g]of Object.entries(a))_.startsWith("augment:")||($[_]=g);return $}function ee(a){return a["augment:mount_point"]||null}Ht(()=>(Q.send({type:ke.secretsHomePanelLoaded,data:{}}),G(),()=>{Q.dispose()}));let W=k(()=>e(n).toSorted((a,$)=>a.updated_at>$.updated_at?-1:1)),qe=k(()=>e(W).filter(a=>{var _;if(!e(q))return!0;const $=e(q).toLowerCase();return a.name.toLowerCase().includes($)||((_=a.description)==null?void 0:_.toLowerCase().includes($))||Object.entries(a.tags).some(([g,x])=>g.toLowerCase().includes($)||x.toLowerCase().includes($))}));var Ee=Yr(),dt=le(Ee),vt=d(dt),ut=d(vt);C(ut,{size:4,color:"primary",children:(a,$)=>{var _=w("Secrets Manager");r(a,_)},$$slots:{default:!0}});var ft=o(ut,2),Ge=d(ft);C(Ge,{size:2,color:"neutral",children:(a,$)=>{var _=w("Manage and organize your application secrets securely");r(a,_)},$$slots:{default:!0}});var wt=o(Ge,2),Ye=d(wt);je(Ye,{get disabled(){return e(v)},title:"Refresh secrets",size:2,variant:"ghost",$$events:{click:G},children:(a,$)=>{var _=Rr();let g;var x=d(_);$e(x,{name:"refresh-ccw",children:(O,A)=>{ar(O)},$$slots:{default:!0}}),B(O=>g=Zt(_,1,"c-refresh-icon svelte-wkglxe",null,g,O),[()=>({"c-refresh-icon--spinning":e(v)})]),r(a,_)},$$slots:{default:!0}});var yt=o(Ye,2);je(yt,{variant:"solid",color:"accent",size:2,$$events:{click:function(){t(R,"create"),t(N,null),t(p,!0),t(J,null)}},children:(a,$)=>{var _=w("+ Add Secret");r(a,_)},$$slots:{default:!0}});var mt=o(vt,2),bt=d(mt),zt=d(bt);He(zt,{placeholder:"Search secrets by name or description...",variant:"surface",size:2,get value(){return e(q)},set value($){t(q,$,!0)},iconRight:$=>{$e($,{name:"search",class:"c-secrets-search__icon",children:(_,g)=>{Qt(_,{})},$$slots:{default:!0}})},$$slots:{iconRight:!0}});var y=o(mt,2),V=d(y),Z=a=>{var $=Br();r(a,$)},ne=(a,$)=>{var _=x=>{var O=Nr(),A=o(d(O),2),oe=d(A);B(()=>P(oe,e(u))),r(x,O)},g=(x,O)=>{var A=S=>{var ve=jr();r(S,ve)},oe=(S,ve)=>{var ie=he=>{var De=Ar(),Je=o(d(De),2),Qe=d(Je);B(()=>P(Qe,`No secrets match your search query "${e(q)??""}". Try adjusting your search terms.`)),r(he,De)},kt=he=>{var De=Kr(),Je=d(De);U(Je,()=>_r,(Qe,xt)=>{xt(Qe,{children:($t,Rt)=>{var et=Zr(),tt=le(et);U(tt,()=>yr,(rt,at)=>{at(rt,{children:(Le,gt)=>{var ze=Ne(),Oe=le(ze);U(Oe,()=>It,(Fe,I)=>{I(Fe,{children:(Ue,st)=>{var Pe=Ir(),Te=le(Pe);U(Te,()=>ct,(re,ae)=>{ae(re,{children:(se,pe)=>{var M=w("Details");r(se,M)},$$slots:{default:!0}})});var Re=o(Te,2);U(Re,()=>ct,(re,ae)=>{ae(re,{class:"column-updated",children:(se,pe)=>{var M=w("Updated");r(se,M)},$$slots:{default:!0}})});var lt=o(Re,2);U(lt,()=>ct,(re,ae)=>{ae(re,{class:"column-updated",children:(se,pe)=>{var M=w("Version");r(se,M)},$$slots:{default:!0}})});var Be=o(lt,2);U(Be,()=>ct,(re,ae)=>{ae(re,{class:"column-size",children:(se,pe)=>{var M=w("Size");r(se,M)},$$slots:{default:!0}})});var We=o(Be,2);U(We,()=>ct,(re,ae)=>{ae(re,{class:"column-actions",children:(se,pe)=>{var M=w("Actions");r(se,M)},$$slots:{default:!0}})}),r(Ue,Pe)},$$slots:{default:!0}})}),r(Le,ze)},$$slots:{default:!0}})});var St=o(tt,2);U(St,()=>wr,(rt,at)=>{at(rt,{children:(Le,gt)=>{var ze=Ne(),Oe=le(ze);Dt(Oe,17,()=>e(qe),Fe=>Fe.name,(Fe,I)=>{var Ue=Ne(),st=le(Ue);U(st,()=>It,(Pe,Te)=>{Te(Pe,{children:(Re,lt)=>{var Be=Hr(),We=le(Be);U(We,()=>nt,(M,ue)=>{ue(M,{children:(fe,Ve)=>{var l=Pr(),c=d(l),f=d(c);C(f,{size:3,weight:"medium",type:"monospace",children:(F,Y)=>{var j=w();B(()=>P(j,e(I).name)),r(F,j)},$$slots:{default:!0}});var i=o(c,2),L=F=>{var Y=Lr(),j=d(Y);C(j,{size:1,weight:"light",children:(_e,we)=>{var ye=w();B(()=>P(ye,e(I).description)),r(_e,ye)},$$slots:{default:!0}}),r(F,Y)};X(i,F=>{e(I).description&&F(L)});var E=o(i,2),z=F=>{var Y=Or();Dt(Y,21,()=>Object.entries(Ie(e(I).tags)),Ot,(j,_e)=>{var we=k(()=>Ut(e(_e),2));let ye=()=>e(we)[1];var be=Ne(),ht=le(be);U(ht,()=>Ft.Root,(qt,Bt)=>{Bt(qt,{variant:"soft",color:"accent",size:1,children:(Wt,Jr)=>{var Nt=w();B(()=>P(Nt,`${e(we)[0]??""}${ye()?`:${ye()}`:""}`)),r(Wt,Nt)},$$slots:{default:!0}})}),r(j,be)}),r(F,Y)};X(E,F=>{Object.keys(Ie(e(I).tags)).length>0&&F(z)});var K=o(E,2),D=F=>{var Y=Ur(),j=d(Y);$e(j,{name:"folder",class:"c-mount-path__icon",children:(we,ye)=>{tr(we,{})},$$slots:{default:!0}});var _e=o(j,2);C(_e,{size:1,weight:"light",type:"monospace",children:(we,ye)=>{var be=w();B(ht=>P(be,ht),[()=>ee(e(I).tags)]),r(we,be)},$$slots:{default:!0}}),r(F,Y)};X(K,F=>{ee(e(I).tags)&&F(D)}),r(fe,l)},$$slots:{default:!0}})});var re=o(We,2);U(re,()=>nt,(M,ue)=>{ue(M,{class:"column-updated l__table-cell--no-min-width",children:(fe,Ve)=>{const l=k(()=>function(c){try{return new Date(c).toLocaleString()}catch{return c}}(e(I).updated_at));pt(fe,{get content(){return e(l)},children:(c,f)=>{Xt(c,{size:1,$$slots:{text:(i,L)=>{C(i,{size:1,weight:"light",slot:"text",children:(E,z)=>{var K=w();B(D=>P(K,D),[()=>function(D){try{return ur(D)}catch{return D}}(e(I).updated_at)]),r(E,K)},$$slots:{default:!0}})},leftIcon:(i,L)=>{var E=Wr(),z=d(E);$e(z,{name:"clock",children:(K,D)=>{er(K,{})},$$slots:{default:!0}}),r(i,E)}}})},$$slots:{default:!0}})},$$slots:{default:!0}})});var ae=o(re,2);U(ae,()=>nt,(M,ue)=>{ue(M,{class:"l__table-cell--no-min-width",children:(fe,Ve)=>{C(fe,{size:1,weight:"light",children:(l,c)=>{var f=w();B(()=>P(f,`v${e(I).version??""}`)),r(l,f)},$$slots:{default:!0}})},$$slots:{default:!0}})});var se=o(ae,2);U(se,()=>nt,(M,ue)=>{ue(M,{class:"column-size  l__table-cell--no-min-width",children:(fe,Ve)=>{C(fe,{size:1,weight:"light",children:(l,c)=>{var f=w();B(i=>P(f,i),[()=>{return(i=e(I).value_size_bytes)<1024?`${i} B`:i<1048576?`${(i/1024).toFixed(1)} KB`:`${(i/1048576).toFixed(1)} MB`;var i}]),r(l,f)},$$slots:{default:!0}})},$$slots:{default:!0}})});var pe=o(se,2);U(pe,()=>nt,(M,ue)=>{ue(M,{class:"column-actions  l__table-cell--no-min-width",children:(fe,Ve)=>{var l=Vr(),c=d(l);pt(c,{content:"Edit secret",children:(i,L)=>{const E=k(()=>`Edit secret ${e(I).name}`);_t(i,{size:1,variant:"ghost",color:"neutral",get"aria-label"(){return e(E)},$$events:{click:()=>function(z){t(R,"edit"),t(N,z,!0),t(p,!0),t(J,null)}(e(I))},children:(z,K)=>{$e(z,{name:"square-pen",children:(D,F)=>{rr(D,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}});var f=o(c,2);pt(f,{content:"Delete secret",children:(i,L)=>{const E=k(()=>`Delete secret ${e(I).name}`);_t(i,{size:1,variant:"ghost",color:"error",get"aria-label"(){return e(E)},$$events:{click:()=>{return z=e(I).name,void t(m,z,!0);var z}},children:(z,K)=>{$e(z,{name:"trash-2",children:(D,F)=>{sr(D,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}}),r(fe,l)},$$slots:{default:!0}})}),r(Re,Be)},$$slots:{default:!0}})}),r(Fe,Ue)}),r(Le,ze)},$$slots:{default:!0}})}),r($t,et)},$$slots:{default:!0}})}),r(he,De)};X(S,he=>{e(qe).length===0?he(ie):he(kt,!1)},ve)};X(x,S=>{e(n).length===0?S(A):S(oe,!1)},O)};X(a,x=>{e(u)?x(_):x(g,!1)},$)};X(V,a=>{e(v)?a(Z):a(ne,!1)});var te=o(dt,2);const Ce=k(()=>e(R)==="create"?ge:void 0);Tr(te,{get show(){return e(p)},get mode(){return e(R)},get secret(){return e(N)},get submitting(){return e(H)},get error(){return e(J)},get validateSecretName(){return e(Ce)},onCancel:me,onSubmit:async function(a){var $,_;t(H,!0),t(J,null);try{if(e(R)==="create"){const g=await Q.send({type:ke.createSecretRequest,data:{name:a.name,value:a.value,tags:a.tags,description:a.description}});if(g.type!==ke.createSecretResponse||!g.data.success)throw new Error((($=g.data)==null?void 0:$.error)||"Failed to create secret");{const x={name:a.name,tags:a.tags,description:a.description,created_at:new Date().toISOString(),updated_at:g.data.updatedAt||new Date().toISOString(),version:g.data.version||"1",value_size_bytes:new TextEncoder().encode(a.value).length};t(n,[x,...e(n)],!0),me()}}else if(e(R)==="edit"&&e(N)){const g=await Q.send({type:ke.updateSecretRequest,data:{name:e(N).name,value:a.value||"",tags:a.tags,description:a.description,expectedVersion:e(N).version}});if(g.type!==ke.updateSecretResponse||!g.data.success)throw new Error(((_=g.data)==null?void 0:_.error)||"Failed to update secret");{const x=e(n).findIndex(O=>{var A;return O.name===((A=e(N))==null?void 0:A.name)});x!==-1&&(e(n)[x]={...e(n)[x],...a.value?{value:a.value}:{},tags:a.tags,description:a.description,updated_at:g.data.updatedAt||new Date().toISOString(),version:g.data.version||e(n)[x].version,value_size_bytes:a.value?new TextEncoder().encode(a.value).length:e(n)[x].value_size_bytes},t(n,[...e(n)],!0)),me()}}}catch(g){console.error("Failed to save secret:",g),t(J,g instanceof Error?g.message:"Failed to save secret",!0)}finally{t(H,!1)}}});var Me=o(te,2);const de=k(()=>!!e(m));Pt(Me,{get show(){return e(de)},title:"Delete Secret",maxWidth:"400px",oncancel:it,body:_=>{var g=Xr(),x=d(g);C(x,{size:2,children:(S,ve)=>{var ie=w("Are you sure you want to delete the secret");r(S,ie)},$$slots:{default:!0}});var O=o(x,2);C(O,{size:2,weight:"bold",children:(S,ve)=>{var ie=w();B(()=>P(ie,e(m))),r(S,ie)},$$slots:{default:!0}});var A=o(O,2);C(A,{size:2,children:(S,ve)=>{var ie=w("?");r(S,ie)},$$slots:{default:!0}});var oe=o(A,2);C(oe,{size:2,children:(S,ve)=>{var ie=w("This action cannot be undone.");r(S,ie)},$$slots:{default:!0}}),r(_,g)},footer:_=>{var g=Gr(),x=d(g);je(x,{variant:"ghost",size:2,$$events:{click:it},children:(A,oe)=>{var S=w("Cancel");r(A,S)},$$slots:{default:!0}});var O=o(x,2);je(O,{color:"error",variant:"solid",size:2,$$events:{click:()=>e(m)&&async function(A){var oe;try{const S=await Q.send({type:ke.deleteSecretRequest,data:{name:A}});if(S.type!==ke.deleteSecretResponse||!S.data.success)throw new Error(((oe=S.data)==null?void 0:oe.error)||"Failed to delete secret");t(n,e(n).filter(ve=>ve.name!==A),!0)}catch(S){console.error("Failed to delete secret:",S)}finally{t(m,null)}}(e(m))},children:(A,oe)=>{var S=w("Delete");r(A,S)},$$slots:{default:!0}}),r(_,g)},$$slots:{body:!0,footer:!0}}),r(b,Ee),Se()},{target:document.getElementById("app")});
