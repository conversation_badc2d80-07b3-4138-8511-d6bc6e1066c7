import{N as ee,x as o,m,ax as ae,P as e,ac as i,a3 as B,a4 as ie,a5 as se,y as G,F as b,ay as P,as as S,u as w,I as T,$ as j,B as E,z as te,C as de,D as re,E as q,b as A,X as ne,a8 as oe,ad as p}from"./GuardedIcon-BFT2yJIo.js";import{a as le,I as ce}from"./IconButtonAugment-CR0fVrwD.js";import{t as ve,f as ue}from"./index-BzB60MCy.js";import{E as me}from"./ellipsis-DBzJOEA0.js";const I=(_,{onResize:s,options:g})=>{const v=new ResizeObserver(s);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var fe=G('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),he=G('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ze(_,s){ee(s,!1);let g,v,C=o(s,"initialWidth",8,300),$=o(s,"expandedMinWidth",8,50),D=o(s,"minimizedWidth",8,0),t=o(s,"minimized",12,!1),H=o(s,"class",8,""),J=o(s,"showButton",8,!0),K=o(s,"deadzone",8,0),Q=o(s,"columnLayoutThreshold",8,600),r=o(s,"layoutMode",28,()=>{}),x=m(),f=m(),l=m(!1),c=m(C()),k=m(C()),d=m(!1);function F(){t(!t())}function M(){if(e(f)){if(r()!==void 0)return i(d,r()==="column"),void(e(d)&&i(l,!1));i(d,e(f).clientWidth<Q()),e(d)&&i(l,!1)}}ae(M),B(()=>(p(t()),p(r())),()=>{t()?(r("row"),i(d,!1)):r()!=="row"||t()||(r(void 0),M())}),B(()=>(p(r()),e(d)),()=>{r()!==void 0&&(i(d,r()==="column"),e(d)&&i(l,!1))}),B(()=>(p(t()),p(D()),e(c)),()=>{i(k,t()?D():e(c))}),ie(),se();var h=he();let L;b("mousemove",P,function(a){if(!e(l)||!e(x)||e(d))return;const n=a.clientX-g,W=e(f).clientWidth-200,u=v+n;u<$()?u<$()-K()?t(!0):(i(c,$()),t(!1)):u>W?(i(c,W),t(!1)):(i(c,u),t(!1))}),b("mouseup",P,function(){i(l,!1),i(c,Math.max(e(c),$()))});var z=w(h),R=w(z);S(R,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var U=w(R);T(U,s,"left",{},null),j(z,a=>i(x,a),()=>e(x));var y=E(z,2);let N;var O=E(y,2),V=w(O);T(V,s,"right",{},null);var Y=E(O,2),Z=a=>{var n=fe(),W=w(n);ce(W,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:F},children:(u,we)=>{oe(u,{name:"ellipsis",children:(X,pe)=>{me(X,{})},$$slots:{default:!0}})},$$slots:{default:!0}}),ve(3,n,()=>ue,()=>({y:0,x:0,duration:200})),A(a,n)};te(Y,a=>{t()&&J()&&a(Z)}),j(h,a=>i(f,a),()=>e(f)),le(h,(a,n)=>I==null?void 0:I(a,n),()=>({onResize:()=>r()===void 0&&M()})),de((a,n)=>{L=q(h,1,`c-drawer ${H()??""}`,"svelte-18f0m3o",L,a),S(z,`--augment-drawer-width:${e(k)??""}px;`),R.inert=e(l),N=q(y,1,"c-drawer__handle svelte-18f0m3o",null,N,n)},[()=>({"is-dragging":e(l),"is-hidden":!e(k),"is-column":e(d)}),()=>({"is-locked":e(d)})],re),b("mousedown",y,function(a){e(d)||(i(l,!0),g=a.clientX,v=e(x).offsetWidth,a.preventDefault())}),b("dblclick",y,F),A(_,h),ne()}export{ze as D,I as r};
