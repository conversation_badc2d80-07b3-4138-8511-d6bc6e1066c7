import{N as ee,x as Y,a3 as M,a4 as ae,a5 as se,G as te,H as y,z as k,P as e,m as v,A as h,b as l,X as le,a6 as oe,a7 as A,y as w,a8 as re,C as _,a1 as q,u as ne,B,a9 as ie,aa as de,ab as ce,ac as f,ad as P,D as ue,ae as ve}from"./GuardedIcon-BFT2yJIo.js";import{B as U}from"./ButtonAugment-CWDjQYWT.js";import{V as he,D as d}from"./index-DON3DCoY.js";import{T as fe}from"./CardAugment-DVbbQqkH.js";import{C as pe}from"./chevron-down-CkTAocB8.js";import{R}from"./chat-types-BDRYChZT.js";var me=w('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),$e=w("<!> <!>",1),ge=w("<!> <!>",1),be=w("<!> <!>",1);function Se(V,E){ee(E,!1);const[H,X]=oe(),p=()=>de(e(C),"$focusedIndex",H),T=v(),S=v(),o=v();let j=Y(E,"onSave",8),n=Y(E,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=v(void 0),D=v(()=>{});M(()=>P(n()),()=>{f(T,n().path)}),M(()=>P(n()),()=>{f(S,n().type)}),M(()=>e(S),()=>{f(o,x.find(r=>r.value===e(S)))}),ae(),se();var Q=te(),F=y(Q),J=r=>{fe(r,{content:"Workspace guidelines are always applied",children:(a,O)=>{U(a,{color:"accent",size:1,disabled:!0,children:(m,I)=>{var N=A("Always");l(m,N)},$$slots:{default:!0}})},$$slots:{default:!0}})},K=r=>{d.Root(r,{get requestClose(){return e(D)},set requestClose(a){f(D,a)},get focusedIndex(){return e(C)},set focusedIndex(a){ce(f(C,a),"$focusedIndex",H)},children:(a,O)=>{var m=be(),I=y(m);d.Trigger(I,{children:(z,Z)=>{var c=me(),$=ne(c);U($,{color:"neutral",size:1,variant:"soft",children:(u,G)=>{var s=A();_(()=>q(s,(e(o),h(()=>e(o).label)))),l(u,s)},$$slots:{default:!0,iconRight:(u,G)=>{re(u,{slot:"iconRight",name:"chevron-down",children:(s,t)=>{pe(s,{})},$$slots:{default:!0}})}}}),l(z,c)},$$slots:{default:!0}});var N=B(I,2);d.Content(N,{side:"bottom",align:"start",children:(z,Z)=>{var c=ge(),$=y(c);ie($,1,()=>x,ve,(s,t)=>{const g=ue(()=>(e(o),e(t),h(()=>e(o).label===e(t).label)));d.Item(s,{onSelect:()=>async function(i){e(D)();try{await j()(i.value,i.value!==R.AGENT_REQUESTED||n().description?n().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(t)),get highlight(){return e(g)},children:(i,b)=>{var L=A();_(()=>q(L,(e(t),h(()=>e(t).label)))),l(i,L)},$$slots:{default:!0}})});var u=B($,2),G=s=>{var t=$e(),g=y(t);d.Separator(g,{});var i=B(g,2);d.Label(i,{children:(b,L)=>{var W=A();_(()=>q(W,(p(),e(o),h(()=>p()!==void 0?x[p()].description:e(o).description)))),l(b,W)},$$slots:{default:!0}}),l(s,t)};k(u,s=>{(p()!==void 0||e(o))&&s(G)}),l(z,c)},$$slots:{default:!0}}),l(a,m)},$$slots:{default:!0},$$legacy:!0})};k(F,r=>{e(T),h(()=>{return a=e(T),he.includes(a);var a})?r(J):r(K,!1)}),l(V,Q),le(),X()}export{Se as R};
