var gs=Object.defineProperty;var ps=(g,t,r)=>t in g?gs(g,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):g[t]=r;var Ee=(g,t,r)=>ps(g,typeof t!="symbol"?t+"":t,r);import{f as lt,b as s,w as Xe,W as qe,J as Lr,bo as ms,aU as fr,bp as mt,at as Wr,N as Be,x as S,y as v,a9 as et,u as o,B as l,P as e,R as it,a8 as Ve,X as je,z as H,C as ve,E as vt,a1 as xe,T as se,a7 as R,ah as fs,a0 as St,as as $s,a6 as pt,aa as We,v as Cs,t as zt,a3 as we,a4 as nt,a as Ut,Q as ws,m as J,I as ut,A as M,F as ft,H as pe,G as tt,ac as u,Y as ys,ad as k,aj as $r,a5 as rt,ay as xr,D as Ne,ap as Zr,ae as bt,b4 as _r,aO as nr,al as Yt,ak as Tt,bq as Er,bd as cr,ab as Bt,ai as Cr,S as dr,V as ur,r as Ss,ag as Nr,ax as Kr,bc as Tr,aq as Qr,br as bs,aQ as Yr,aP as Ms}from"./GuardedIcon-BFT2yJIo.js";import"./initialize-DgduSj_U.js";import"./design-system-init-K1OaxmPU.js";import{I as Rt,b as ks,c as Ye,a as xs,h as Zt,i as Pr,g as As,H as vr}from"./IconButtonAugment-CR0fVrwD.js";import{M as zs}from"./message-broker-BygIEqPd.js";import{G as Rs,S as Is,a as Ls,N as _s,L as Es,b as st,M as Lt,D as Ns,F as Ts,f as Xr,c as Fr,d as Ps,C as Fs,P as hr,e as Us,g as Os,h as Vs,A as Ds,i as Hs}from"./partner-mcp-utils-DYgwDbFd.js";import{f as gr,Y as $t,L as He,Z as Kt,_ as _t,$ as or,a0 as ir,a1 as qs,a2 as wr,a3 as Ur,a4 as Or,a5 as Gs,a6 as Js,a7 as Ar,e as pr,t as yr,D as Ze,a8 as Bs,E as es,a9 as Xt,A as mr,V as js,b as Ws,c as Zs,R as Ks}from"./index-DON3DCoY.js";import{G as Qs}from"./github-ZfT2gl61.js";import{D as er,C as Ys,g as ts,T as rs,a as rr,b as ss,S as Sr,c as Xs,s as ea}from"./extension-client-context-CsPEBoR9.js";import{o as sr}from"./keypress-DD1aQVr0.js";import{V as as}from"./VSCodeCodicon-4Sfbv3fq.js";import{P as Pt,s as Vr}from"./plus-BGQoI3Yl.js";import{A as ta}from"./async-messaging-Bp70swAv.js";import{R as ra,T as sa}from"./trash-ByjpApta.js";import{k as aa,C as ns,a as na,T as ar}from"./CollapseButtonAugment-Cnr_pz_w.js";import{D as oa}from"./Drawer-DMvrLFVH.js";import{b as os,T as Ct,a as Nt,p as ia}from"./CardAugment-DVbbQqkH.js";import{B as Je}from"./ButtonAugment-CWDjQYWT.js";import{B as br}from"./index-BuQDLh4_.js";import{T as At}from"./TextFieldAugment-DfCJMerV.js";import{C as jt}from"./CalloutAugment-C9yL-4XM.js";import{E as la}from"./ellipsis-DBzJOEA0.js";import{P as ca}from"./pen-to-square-Bn06n7i7.js";import{T as zr}from"./TextAreaAugment-DIwZ8JEM.js";import{C as da}from"./copy-oYDqgVJ5.js";import{C as Rr}from"./chevron-down-CkTAocB8.js";import{S as ua}from"./SuccessfulButton-Btt1yYx9.js";import{M as va}from"./index-CWns8XM2.js";import"./BaseTextInput-BaUpeUef.js";import{R as ha}from"./RulesModeSelector-CNCQbfap.js";import{M as is}from"./ModalAugment-xq6pa268.js";import{R as Dr}from"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./focusTrapStack-CDv9v5kQ.js";import"./index-BzB60MCy.js";const qt={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class ga{constructor(t,r=qt){Ee(this,"timerId",null);Ee(this,"currentMS");Ee(this,"step",0);Ee(this,"params");this.callback=t;const a={...r};a.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),a.maxMS=qt.maxMS),a.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),a.initialMS=qt.initialMS),a.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),a.mult=qt.mult),a.maxSteps!==void 0&&a.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),a.maxSteps=qt.maxSteps),this.params=a,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const t=this.callback();t instanceof Promise&&t.catch(r=>console.error("Error in polling callback:",r))}catch(t){console.error("Error in polling callback:",t)}}}var pa=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function ma(g){var t=pa();s(g,t)}var fa=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function $a(g){var t=fa();s(g,t)}var Ca=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function yt(g){var t=Ca();s(g,t)}var wa=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function ya(g){var t=wa();s(g,t)}class Ft{constructor(t){Ee(this,"configs",Xe([]));Ee(this,"pollingManager");Ee(this,"_enableDebugFeatures",Xe(!1));Ee(this,"_settingsComponentSupported",Xe({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Ee(this,"_enableAgentMode",Xe(!1));Ee(this,"_enableAgentSwarmMode",Xe(!1));Ee(this,"_enableNativeRemoteMcp",Xe(!0));Ee(this,"_hasEverUsedRemoteAgent",Xe(!1));Ee(this,"_enableInitialOrientation",Xe(!1));Ee(this,"_userTier",Xe("unknown"));Ee(this,"_userEmail",Xe(void 0));Ee(this,"_guidelines",Xe({}));this._host=t,this.pollingManager=new ga(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(t){const r=!t.isConfigured,a=t.oauthUrl;if(t.identifier.hostName===gr.remoteToolHost){let n=t.identifier.toolId;switch(typeof n=="string"&&/^\d+$/.test(n)&&(n=Number(n)),n){case $t.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Qs,requiresAuthentication:r,authUrl:a};case $t.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Es,requiresAuthentication:r,authUrl:a};case $t.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:$a,requiresAuthentication:r,authUrl:a};case $t.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:_s,requiresAuthentication:r,authUrl:a};case $t.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:ma,requiresAuthentication:r,authUrl:a};case $t.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Ls,requiresAuthentication:r,authUrl:a};case $t.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Is,requiresAuthentication:r,authUrl:a};case $t.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Rs,requiresAuthentication:r,authUrl:a};case $t.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:r,authUrl:a};default:throw new Error(`Unhandled RemoteToolId: ${n}`)}}else if(t.identifier.hostName===gr.localToolHost){const n=t.identifier.toolId;switch(n){case He.readFile:case He.editFile:case He.saveFile:case He.launchProcess:case He.killProcess:case He.readProcess:case He.writeProcess:case He.listProcesses:case He.waitProcess:case He.openBrowser:case He.clarify:case He.onboardingSubAgent:case He.strReplaceEditor:case He.remember:case He.diagnostics:case He.webFetch:case He.setupScript:case He.readTerminal:case He.gitCommitRetrieval:case He.memoryRetrieval:case He.startWorkerAgent:case He.readWorkerState:case He.waitForWorkerAgent:case He.sendInstructionToWorkerAgent:case He.stopWorkerAgent:case He.deleteWorkerAgent:case He.readWorkerAgentEdits:case He.applyWorkerAgentEdits:case He.LocalSubAgent:return{displayName:t.definition.name.toString(),description:"Local tool",icon:yt,requiresAuthentication:r,authUrl:a};default:throw new Error(`Unhandled LocalToolType: ${n}`)}}else if(t.identifier.hostName===gr.sidecarToolHost){const n=t.identifier.toolId;switch(n){case st.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Lt,requiresAuthentication:r,authUrl:a};case st.shell:return{displayName:"Shell",description:"Shell",icon:Lt,requiresAuthentication:r,authUrl:a};case st.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Lt,requiresAuthentication:r,authUrl:a};case st.view:return{displayName:"File View",description:"File Viewer",icon:Lt,requiresAuthentication:r,authUrl:a};case st.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Lt,requiresAuthentication:r,authUrl:a};case st.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:ya,requiresAuthentication:r,authUrl:a};case st.remember:return{displayName:t.definition.name.toString(),description:"Remember",icon:yt,requiresAuthentication:r,authUrl:a};case st.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Ts,requiresAuthentication:r,authUrl:a};case st.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:yt,requiresAuthentication:r,authUrl:a};case st.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:yt,requiresAuthentication:r,authUrl:a};case st.viewRangeUntruncated:return{displayName:t.definition.name.toString(),description:"View Range",icon:yt,requiresAuthentication:r,authUrl:a};case st.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:yt,requiresAuthentication:r,authUrl:a};case st.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:yt,requiresAuthentication:r,authUrl:a};case st.searchUntruncated:return{displayName:t.definition.name.toString(),description:"Search Untruncated",icon:yt,requiresAuthentication:r,authUrl:a};case st.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Ns,requiresAuthentication:r,authUrl:a};case st.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Lt,requiresAuthentication:r,authUrl:a};default:throw new Error(`Unhandled SidecarToolType: ${n}`)}}return{displayName:t.definition.name.toString(),description:t.definition.description||"",requiresAuthentication:r,authUrl:a}}handleMessageFromExtension(t){const r=t.data;switch(r.type){case qe.toolConfigInitialize:return this.createConfigsFromHostTools(r.data.hostTools,r.data.toolConfigs),r.data&&r.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(r.data.enableDebugFeatures),r.data&&r.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(r.data.settingsComponentSupported),r.data.enableAgentMode!==void 0&&this._enableAgentMode.set(r.data.enableAgentMode),r.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(r.data.enableAgentSwarmMode),r.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(r.data.hasEverUsedRemoteAgent),r.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(r.data.enableInitialOrientation),r.data.userTier!==void 0&&this._userTier.set(r.data.userTier),r.data.userEmail!==void 0&&this._userEmail.set(r.data.userEmail),r.data.guidelines!==void 0&&this._guidelines.set(r.data.guidelines),r.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(r.data.enableNativeRemoteMcp),!0;case qe.toolConfigDefinitionsResponse:return this.configs.update(a=>this.createConfigsFromHostTools(r.data.hostTools,[]).map(n=>{const c=a.find(h=>h.name===n.name);return c?{...c,displayName:n.displayName,description:n.description,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,isConfigured:n.isConfigured,toolApprovalConfig:n.toolApprovalConfig}:n})),!0}return!1}createConfigsFromHostTools(t,r){return t.map(a=>{const n=this.transformToolDisplay(a),c=r.find(A=>A.name===a.definition.name),h=(c==null?void 0:c.isConfigured)??!n.requiresAuthentication;return{config:(c==null?void 0:c.config)??{},configString:JSON.stringify((c==null?void 0:c.config)??{},null,2),isConfigured:h,name:a.definition.name.toString(),displayName:n.displayName,description:n.description,identifier:a.identifier,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:a.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(t){return["github","linear","notion","jira","confluence","supabase"].includes(t.displayName.toLowerCase())}getDisplayableTools(){return Lr(this.configs,t=>{const r=t.filter(n=>this.isDisplayableTool(n)),a=new Map;for(const n of r)a.set(n.displayName,n);return Array.from(a.values()).sort((n,c)=>{const h={GitHub:1,Linear:2,Notion:3},A=Number.MAX_SAFE_INTEGER,z=h[n.displayName]||A,w=h[c.displayName]||A;return z<A&&w<A||z===A&&w===A?z!==w?z-w:n.displayName.localeCompare(c.displayName):z-w})})}getPretendNativeToolDefs(){return Lr(this.configs,t=>this.getEnableNativeRemoteMcp()?Xr(t):[])}saveConfig(t){this.startPolling()}notifyLoaded(){this._host.postMessage({type:qe.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(t=!0){this._host.postMessage({type:qe.toolConfigGetDefinitions,data:{useCache:t}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"?this._enableNativeRemoteMcp:ms(!1)}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getUserEmail(){return this._userEmail}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(t){this._guidelines.update(r=>r.userGuidelines?{...r,userGuidelines:{...r.userGuidelines,contents:t,enabled:t.length>0}}:r)}updateToolApprovalConfig(t,r){this.configs.update(a=>a.map(n=>n.identifier.toolId===t.toolId&&n.identifier.hostName===t.hostName?{...n,toolApprovalConfig:r}:n))}getSettingsComponentSupported(){return this._settingsComponentSupported}}Ee(Ft,"key","toolConfigModel");class at extends Error{constructor(t){super(t),this.name="MCPServerError",Object.setPrototypeOf(this,at.prototype)}}const wt=Kt({name:_t().optional(),title:_t().optional(),type:qs(["stdio","http","sse"]).optional(),command:_t().optional(),args:ir(wr([_t(),Ur(),Or()])).optional(),env:or(wr([_t(),Ur(),Or(),Gs(),Js()])).optional(),url:_t().optional()}).passthrough();function dt(g){return(g==null?void 0:g.type)==="http"||(g==null?void 0:g.type)==="sse"}function Et(g){return(g==null?void 0:g.type)==="stdio"}function tr(g){return dt(g)?g.url:Et(g)?g.command:""}const Sa=ir(wt),ba=Kt({servers:ir(wt)}),Ma=Kt({mcpServers:ir(wt)}),ka=Kt({servers:or(Ar())}),xa=Kt({mcpServers:or(Ar())}),Aa=or(Ar()),za=wt.refine(g=>{const t=g.command!==void 0,r=g.url!==void 0;if(!t&&!r)return!1;const a=new Set(["name","title","type","command","args","env","url"]);return Object.keys(g).every(n=>a.has(n))},{message:"Single server object must have valid server properties"});function kt(g){try{const t=wt.transform(r=>{let a;if(r.type)a=r.type;else if(r.url)a="http";else{if(!r.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");a="stdio"}if(a==="http"||a==="sse"){if(!r.url)throw new Error(`${a.toUpperCase()} server must have a 'url' property`);return{type:a,name:r.name||r.title||r.url,url:r.url}}{const n=r.command||"",c=r.args?r.args.map(w=>String(w)):[];if(!n)throw new Error("Stdio server must have a 'command' property");const h=c.length>0?`${n} ${c.join(" ")}`:n,A=r.name||r.title||(n?n.split(" ")[0]:""),z=r.env?Object.fromEntries(Object.entries(r.env).filter(([w,i])=>i!=null).map(([w,i])=>[w,String(i)])):void 0;return{type:"stdio",name:A,command:h,arguments:"",useShellInterpolation:!0,env:Object.keys(z||{}).length>0?z:void 0}}}).refine(r=>!!r.name,{message:"Server must have a name",path:["name"]}).refine(r=>r.type==="http"||r.type==="sse"?!!r.url:!!r.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(g);if(!t.success)throw new at(t.error.message);return t.data}catch(t){throw t instanceof Error?new at(`Invalid server configuration: ${t.message}`):new at("Invalid server configuration")}}class Qt{constructor(t){Ee(this,"servers",Xe([]));this.host=t,this.loadServersFromStorage()}handleMessageFromExtension(t){const r=t.data;if(r.type===qe.getStoredMCPServersResponse){const a=r.data;return Array.isArray(a)&&this.servers.set(a),!0}return!1}async importServersFromJSON(t){return this.importFromJSON(t)}loadServersFromStorage(){try{this.host.postMessage({type:qe.getStoredMCPServers})}catch(t){console.error("Failed to load MCP servers:",t),this.servers.set([])}}saveServers(t){try{this.host.postMessage({type:qe.setStoredMCPServers,data:t})}catch(r){throw console.error("Failed to save MCP servers:",r),new at("Failed to save MCP servers")}}getServers(){return this.servers}addServer(t){this.checkExistingServerName(t.name),this.servers.update(r=>{const a=[...r,{...t,id:crypto.randomUUID()}];return this.saveServers(a),a})}addServers(t){for(const r of t)this.checkExistingServerName(r.name);this.servers.update(r=>{const a=[...r,...t.map(n=>({...n,id:crypto.randomUUID()}))];return this.saveServers(a),a})}checkExistingServerName(t,r){const a=fr(this.servers).find(n=>n.name===t);if(a&&(a==null?void 0:a.id)!==r)throw new at(`Server name '${t}' already exists`)}updateServer(t){this.checkExistingServerName(t.name,t.id),this.servers.update(r=>{const a=r.map(n=>n.id===t.id?t:n);return this.saveServers(a),a})}deleteServer(t){this.servers.update(r=>{const a=r.filter(n=>n.id!==t.id);return this.saveServers(a),a}),t.type!=="http"&&t.type!=="sse"||this.host.postMessage({type:qe.deleteOAuthSession,data:t.name})}toggleDisabledServer(t){this.servers.update(r=>{const a=r.map(n=>n.id===t?{...n,disabled:!n.disabled}:n);return this.saveServers(a),a})}static convertServerToJSON(t){if(dt(t))return JSON.stringify({mcpServers:{[t.name]:{url:t.url,type:t.type}}},null,2);{const r=t;return JSON.stringify({mcpServers:{[r.name]:{command:r.command.split(" ")[0],args:r.command.split(" ").slice(1),env:r.env}}},null,2)}}static parseServerValidationMessages(t){const r=new Map,a=new Map;t.forEach(c=>{var A,z;const h=(A=c.tools)==null?void 0:A.filter(w=>!w.enabled).map(w=>w.definition.mcp_tool_name);c.disabled?r.set(c.id,"MCP server has been manually disabled"):c.tools&&c.tools.length===0?r.set(c.id,"No tools are available for this MCP server"):h&&h.length===((z=c.tools)==null?void 0:z.length)?r.set(c.id,"All tools for this MCP server have validation errors: "+h.join(", ")):h&&h.length>0&&a.set(c.id,"MCP server has validation errors in the following tools which have been disabled: "+h.join(", "))});const n=this.parseDuplicateServerIds(t);return{errors:new Map([...r,...n]),warnings:a}}static parseDuplicateServerIds(t){const r=new Map;for(const n of t)r.has(n.name)||r.set(n.name,[]),r.get(n.name).push(n.id);const a=new Map;for(const[,n]of r)if(n.length>1)for(let c=1;c<n.length;c++)a.set(n[c],"MCP server is disabled due to duplicate server names");return a}static convertParsedServerToWebview(t){const{tools:r,...a}=t;return{...a,tools:void 0}}static parseServerConfigFromJSON(t){return function(a){try{const n=JSON.parse(a),c=wr([Sa.transform(h=>h.map(A=>kt(A))),ba.transform(h=>h.servers.map(A=>kt(A))),Ma.transform(h=>h.mcpServers.map(A=>kt(A))),ka.transform(h=>Object.entries(h.servers).map(([A,z])=>{const w=wt.parse(z);return kt({...w,name:w.name||A})})),xa.transform(h=>Object.entries(h.mcpServers).map(([A,z])=>{const w=wt.parse(z);return kt({...w,name:w.name||A})})),za.transform(h=>[kt(h)]),Aa.transform(h=>{if(!Object.values(h).some(A=>{const z=wt.safeParse(A);return z.success&&(z.data.command!==void 0||z.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(h).map(([A,z])=>{const w=wt.parse(z);return kt({...w,name:w.name||A})})})]).safeParse(n);if(c.success)return c.data;throw new at("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(n){throw n instanceof at?n:new at("Failed to parse MCP servers from JSON. Please check the format.")}}(t).map(a=>this.convertParsedServerToWebview(a))}importFromJSON(t){try{const r=Qt.parseServerConfigFromJSON(t),a=fr(this.servers),n=new Set(a.map(c=>c.name));for(const c of r){if(!c.name)throw new at("All servers must have a name.");if(n.has(c.name))throw new at(`A server with the name '${c.name}' already exists.`);n.add(c.name)}return this.servers.update(c=>{const h=[...c,...r.map(A=>({...A,id:crypto.randomUUID()}))];return this.saveServers(h),h}),r.length}catch(r){throw r instanceof at?r:new at("Failed to import MCP servers from JSON. Please check the format.")}}}class Ra{constructor(t){Ee(this,"_terminalSettings",Xe({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=t,this.requestTerminalSettings()}handleMessageFromExtension(t){const r=t.data;return r.type===qe.terminalSettingsResponse&&(this._terminalSettings.set(r.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:qe.getTerminalSettings})}updateSelectedShell(t){this._terminalSettings.update(r=>({...r,selectedShell:t})),this._host.postMessage({type:qe.updateTerminalSettings,data:{selectedShell:t}})}updateStartupScript(t){this._terminalSettings.update(r=>({...r,startupScript:t})),this._host.postMessage({type:qe.updateTerminalSettings,data:{startupScript:t}})}}const Jt=class Jt{constructor(t){Ee(this,"_swarmModeSettings",Xe(er));Ee(this,"_isLoaded",!1);Ee(this,"_pollInterval",null);Ee(this,"_lastKnownSettingsHash","");Ee(this,"dispose",()=>{this.stopPolling()});this._msgBroker=t,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const t=await this._msgBroker.sendToSidecar({type:pr.getSwarmModeSettings});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data)),this._isLoaded=!0}catch(t){console.warn("Failed to load swarm mode settings, using defaults:",t),this._swarmModeSettings.set(er),this._lastKnownSettingsHash=JSON.stringify(er),this._isLoaded=!0}}async updateSettings(t){try{const r=await this._msgBroker.sendToSidecar({type:pr.updateSwarmModeSettings,data:t});r.data&&(this._swarmModeSettings.set(r.data),this._lastKnownSettingsHash=JSON.stringify(r.data))}catch(r){throw console.error("Failed to update swarm mode settings:",r),r}}async setEnabled(t){await this.updateSettings({enabled:t})}async resetToDefaults(){await this.updateSettings(er)}updateEnabled(t){this.setEnabled(t).catch(r=>{console.error("Failed to update enabled setting:",r)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Jt.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const t=await this._msgBroker.sendToSidecar({type:pr.getSwarmModeSettings}),r=JSON.stringify(t.data);this._lastKnownSettingsHash&&r!==this._lastKnownSettingsHash&&t.data&&this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=r}catch(t){console.warn("Failed to check for swarm mode settings updates:",t)}}};Ee(Jt,"key","swarmModeModel"),Ee(Jt,"POLLING_INTERVAL_MS",5e3);let Wt=Jt;var xt=(g=>(g.file="file",g.folder="folder",g))(xt||{});class Mt{constructor(t,r){Ee(this,"subscribe");Ee(this,"set");Ee(this,"update");Ee(this,"handleMessageFromExtension",async t=>{const r=t.data;switch(r.type){case qe.wsContextSourceFoldersChanged:case qe.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case qe.sourceFoldersSyncStatus:this.update(a=>({...a,syncStatus:r.data.status}))}});Ee(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:qe.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Ee(this,"getChildren",async t=>(await this.asyncMsgSender.send({type:qe.wsContextGetChildrenRequest,data:{fileId:{folderRoot:t.folderRoot,relPath:t.relPath}}},1e4)).data.children.map(r=>r.type==="folder"?{...r,children:[],expanded:!1}:{...r}).sort((r,a)=>r.type===a.type?r.name.localeCompare(a.name):r.type==="folder"?-1:1));this.host=t,this.asyncMsgSender=r;const{subscribe:a,set:n,update:c}=Xe({sourceFolders:[],sourceTree:[],syncStatus:yr.done});this.subscribe=a,this.set=n,this.update=c,this.getSourceFolders().then(h=>{this.update(A=>({...A,sourceFolders:h,sourceTree:Mt.sourceFoldersToSourceNodes(h)}))})}async expandNode(t){t.children=await this.getChildren(t.fileId),t.expanded=!0,this.update(r=>r)}collapseNode(t){this.update(r=>(t.children=[],t.expanded=!1,r))}toggleNode(t){t.type==="folder"&&t.inclusionState!==mt.excluded&&(t.expanded?this.collapseNode(t):this.expandNode(t))}addMoreSourceFolders(){this.host.postMessage({type:qe.wsContextAddMoreSourceFolders})}removeSourceFolder(t){this.host.postMessage({type:qe.wsContextRemoveSourceFolder,data:t})}requestRefresh(){this.host.postMessage({type:qe.wsContextUserRequestedRefresh})}async updateSourceFolders(t){let r=fr(this);const a=await this.getRefreshedSourceTree(r.sourceTree,t);this.update(n=>({...n,sourceFolders:t,sourceTree:a}))}async getRefreshedSourceTree(t,r){const a=Mt.sourceFoldersToSourceNodes(r);return this.getRefreshedSourceTreeRecurse(t,a)}async getRefreshedSourceTreeRecurse(t,r){const a=new Map(t.map(n=>[JSON.stringify([n.fileId.folderRoot,n.fileId.relPath]),n]));for(let n of r){const c=Mt.fileIdToString(n.fileId);if(n.type==="folder"){const h=a.get(c);h&&(n.expanded=h.type==="folder"&&h.expanded,n.expanded&&(n.children=await this.getChildren(n.fileId),n.children=await this.getRefreshedSourceTreeRecurse(h.children,n.children)))}}return r}static fileIdToString(t){return JSON.stringify([t.folderRoot,t.relPath])}static sourceFoldersToSourceNodes(t){return t.filter(r=>!r.isNestedFolder&&!r.isPending).sort((r,a)=>r.name.localeCompare(a.name)).map(r=>({name:r.name,fileId:r.fileId,children:[],expanded:!1,type:"folder",inclusionState:r.inclusionState,reason:"",trackedFileCount:r.trackedFileCount}))}}var Ia=v('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),La=v('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');Wr(["keyup","click"]);const _a="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Ea="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Na="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Ta=v('<div class="children-container"></div>'),Pa=v('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function ls(g,t){Be(t,!0);let r=fs(t.data);const a=()=>{t.wsContextModel.toggleNode(r)},n={[mt.included]:_a,[mt.excluded]:Ea,[mt.partial]:Na},c={[mt.included]:"included",[mt.excluded]:"excluded",[mt.partial]:"partially included"};let h=it(()=>r.type===xt.folder&&r.inclusionState!==mt.excluded),A=it(()=>{return(d=r).type===xt.folder&&d.inclusionState!==mt.excluded?d.expanded?"chevron-down":"chevron-right":d.type===xt.folder?"folder":"file";var d}),z=it(()=>r.type===xt.folder&&r.expanded&&r.children&&r.children.length>0?r:null);var w=Pa(),i=o(w);let Y;i.__click=a;var O=it(()=>sr("Enter",a));i.__keyup=function(...d){var E;(E=e(O))==null||E.apply(this,d)};var ae=o(i);as(ae,{get icon(){return e(A)}});var ce=l(ae,2),U=o(ce),x=l(ce,2),I=d=>{se(d,{size:1,class:"file-count",children:(E,j)=>{var p=R();ve(f=>xe(p,f),[()=>r.trackedFileCount.toLocaleString()]),s(E,p)},$$slots:{default:!0}})};H(x,d=>{r.type===xt.folder&&r.inclusionState!==mt.excluded&&typeof r.trackedFileCount=="number"&&d(I)});var V=l(x,2),W=l(i,2),b=d=>{var E=Ta();et(E,21,()=>e(z).children,j=>Mt.fileIdToString(j.fileId),(j,p)=>{const f=it(()=>t.indentLevel+1);ls(j,{get data(){return e(p)},get wsContextModel(){return t.wsContextModel},get indentLevel(){return e(f)}})}),s(d,E)};H(W,d=>{e(z)&&d(b)}),ve(d=>{Y=vt(i,1,"tree-item svelte-sympus",null,Y,d),St(i,"title",r.reason),St(i,"aria-expanded",r.type===xt.folder&&r.expanded),St(i,"aria-level",t.indentLevel),$s(i,`padding-left: ${10*t.indentLevel+20}px;`),xe(U,r.name),St(V,"src",n[r.inclusionState]),St(V,"alt",c[r.inclusionState])},[()=>({"included-folder":e(h)})]),s(g,w),je()}Wr(["click","keyup"]);var Fa=v('<div class="files-container svelte-8hfqhl"></div>');function Ua(g,t){Be(t,!0);const[r,a]=pt();let n=it(()=>We(t.wsContextModel,"$wsContextModel",r).sourceTree);var c=Fa();et(c,21,()=>e(n),h=>Mt.fileIdToString(h.fileId),(h,A)=>{ls(h,{get wsContextModel(){return t.wsContextModel},get data(){return e(A)},indentLevel:0})}),s(g,c),je(),a()}var Oa=lt('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Va(g){var t=Oa();s(g,t)}var Da=v('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Ha=v("<!> <!>",1),qa=v('<div class="settings-card-body"><!></div>'),Ga=v('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function gt(g,t){const r=Cs(t),a=zt(t,["children","$$slots","$$events","$$legacy"]),n=zt(a,["class","icon","title","isClickable"]);Be(t,!1);const c=J(),h=J(),A=J();let z=S(t,"class",8,""),w=S(t,"icon",24,()=>{}),i=S(t,"title",24,()=>{}),Y=S(t,"isClickable",8,!1);we(()=>(e(c),e(h),k(n)),()=>{u(c,n.class),u(h,ys(n,["class"]))}),we(()=>(k(z()),e(c)),()=>{u(A,`settings-card ${z()} ${e(c)||""}`)}),nt();var O=Ga();Ut(O,E=>({role:"button",class:e(A),...e(h),[ws]:E}),[()=>({clickable:Y()})],"svelte-13uht7n");var ae=o(O),ce=o(ae),U=o(ce),x=E=>{var j=Ha(),p=pe(j),f=$=>{var N=Da(),re=o(N);$r(re,w,(T,L)=>{L(T,{})}),s($,N)};H(p,$=>{w()&&$(f)});var D=l(p,2),y=$=>{se($,{color:"neutral",size:1,weight:"light",class:"card-title",children:(N,re)=>{var T=R();ve(()=>xe(T,i())),s(N,T)},$$slots:{default:!0}})};H(D,$=>{i()&&$(y)}),s(E,j)},I=E=>{var j=tt(),p=pe(j);ut(p,t,"header-left",{},null),s(E,j)};H(U,E=>{w()||i()?E(x):E(I,!1)});var V=l(ce,2),W=o(V);ut(W,t,"header-right",{},null);var b=l(ae,2),d=E=>{var j=qa(),p=o(j);ut(p,t,"default",{},null),s(E,j)};H(b,E=>{M(()=>r.default)&&E(d)}),ft("click",O,function(E){ks.call(this,t,E)}),s(g,O),je()}var Ja=v('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),Ba=v('<div slot="header-right"><!></div>');function ja(g,t){Be(t,!1);const[r,a]=pt(),n=()=>We(h,"$wsContextModel",r),c=J();let h=new Mt(Ye,new ta(Ye.postMessage)),A=J(),z=J();we(()=>n(),()=>{u(A,n().sourceFolders.sort((w,i)=>w.isWorkspaceFolder!==i.isWorkspaceFolder?w.isWorkspaceFolder?-1:1:w.fileId.folderRoot.localeCompare(i.fileId.folderRoot)))}),we(()=>n(),()=>{u(z,n().syncStatus)}),we(()=>e(A),()=>{u(c,e(A).reduce((w,i)=>w+(i.trackedFileCount??0),0))}),nt(),rt(),ft("message",xr,function(...w){var i;(i=h.handleMessageFromExtension)==null||i.apply(this,w)}),gt(g,{get icon(){return Va},title:"Context",$$events:{contextmenu:w=>w.preventDefault()},children:(w,i)=>{var Y=Ja(),O=o(Y),ae=o(O);se(ae,{size:1,weight:"medium",class:"context-section-header",children:(V,W)=>{var b=R("SOURCE FOLDERS");s(V,b)},$$slots:{default:!0}}),function(V,W){Be(W,!0);let b=S(W,"folders",19,()=>[]);var d=La(),E=o(d);et(E,17,b,D=>Mt.fileIdToString(D.fileId),(D,y)=>{var $=Ia();let N;var re=o($),T=Q=>{const Ie=it(()=>sr("Enter",()=>W.onRemove(e(y).fileId.folderRoot)));Rt(Q,{title:"Remove source folder from Augment context",onclick:()=>W.onRemove(e(y).fileId.folderRoot),get onkeyup(){return e(Ie)},variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",children:(De,ye)=>{Ve(De,{name:"x",children:(m,C)=>{Ys(m)},$$slots:{default:!0}})},$$slots:{default:!0}})};H(re,Q=>{e(y).isWorkspaceFolder||Q(T)});var L=l(re,2);const _=it(()=>(Q=>Q.isWorkspaceFolder?"root-folder":"folder")(e(y)));as(L,{class:"source-folder-v-adjust",get icon(){return e(_)}});var q=l(L,2),Z=o(q),ge=l(Z),de=o(ge),ie=l(q,2),Re=Q=>{se(Q,{size:1,class:"file-count",children:(Ie,De)=>{var ye=R();ve(m=>xe(ye,m),[()=>e(y).trackedFileCount.toLocaleString()]),s(Ie,ye)},$$slots:{default:!0}})};H(ie,Q=>{e(y).trackedFileCount&&Q(Re)}),ve(Q=>{N=vt($,1,"item svelte-1skknri",null,N,Q),xe(Z,`${e(y).name??""} `),xe(de,e(y).isPending?"(pending)":e(y).fileId.folderRoot)},[()=>({"workspace-folder":e(y).isWorkspaceFolder})]),s(D,$)});var j=l(E,2),p=it(()=>sr("Enter",W.onAddMore));j.__keyup=function(...D){var y;(y=e(p))==null||y.apply(this,D)},j.__click=function(...D){var y;(y=W.onAddMore)==null||y.apply(this,D)};var f=o(j);Ve(f,{name:"plus",children:(D,y)=>{Pt(D,{})},$$slots:{default:!0}}),s(V,d),je()}(l(ae,2),{get folders(){return e(A)},onRemove:V=>h.removeSourceFolder(V),onAddMore:()=>h.addMoreSourceFolders()});var ce=l(O,2),U=o(ce),x=o(U);se(x,{size:1,weight:"medium",class:"context-section-header",children:(V,W)=>{var b=R("FILES");s(V,b)},$$slots:{default:!0}});var I=l(x,2);se(I,{size:1,class:"file-count",children:(V,W)=>{var b=R();ve(d=>xe(b,d),[()=>(e(c),M(()=>e(c).toLocaleString()))],Ne),s(V,b)},$$slots:{default:!0}}),Ua(l(U,2),{get wsContextModel(){return h}}),s(w,Y)},$$slots:{default:!0,"header-right":(w,i)=>{var Y=Ba(),O=o(Y),ae=ce=>{var U=it(()=>sr("Enter",()=>h.requestRefresh()));Rt(ce,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>h.requestRefresh(),keyup(...x){var I;(I=e(U))==null||I.apply(this,x)}},children:(x,I)=>{Ve(x,{name:"refresh-ccw",children:(V,W)=>{ra(V)},$$slots:{default:!0}})},$$slots:{default:!0}})};H(O,ce=>{e(z),k(yr),M(()=>e(z)===yr.done)&&ce(ae)}),s(w,Y)}}}),je(),a()}function Mr(g){return function(t){switch(typeof t){case"object":return t!=null;case"function":return!0;default:return!1}}(g)&&"name"in g}function Hr(g){return Mr(g)&&"component"in g}var Wa=lt('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function qr(g){var t=Wa();s(g,t)}var Za=v('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),Ka=v('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),Qa=v('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),Ya=v('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Gr(g,t){Be(t,!1);let r=S(t,"item",8);rt();var a=Ya(),n=o(a);ut(n,t,"header",{},null);var c=l(n,2),h=o(c),A=z=>{var w=Qa(),i=pe(w);se(i,{size:4,weight:"medium",color:"neutral",children:(U,x)=>{var I=Za(),V=o(I);ve(()=>xe(V,(k(r()),M(()=>{var W;return(W=r())==null?void 0:W.name})))),s(U,I)},$$slots:{default:!0}});var Y=l(i,2),O=U=>{se(U,{color:"secondary",size:1,weight:"light",children:(x,I)=>{var V=Ka(),W=o(V);ve(()=>xe(W,(k(r()),M(()=>{var b;return(b=r())==null?void 0:b.description})))),s(x,V)},$$slots:{default:!0}})};H(Y,U=>{k(r()),M(()=>{var x;return(x=r())==null?void 0:x.description})&&U(O)});var ae=l(Y,2),ce=o(ae);ut(ce,t,"content",{get item(){return r()}},null),s(z,w)};H(h,z=>{r()!=null&&z(A)}),ve(()=>St(a,"id",(k(r()),M(()=>{var z;return(z=r())==null?void 0:z.id})))),s(g,a),je()}function Gt(g,t,r,a,n,c){return n!==void 0?{name:g,description:t,icon:r,id:a,component:n,props:c}:{name:g,description:t,icon:r,id:a}}var Xa=v('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),en=v('<span class="c-navigation__head-icon"><!></span> ',1),tn=v("<button><!></button>"),rn=v('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),sn=v('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),an=v('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),nn=v("<span><!></span>"),on=v('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),ln=v('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),cn=v("<div><!></div>");function dn(g,t){Be(t,!1);let r=S(t,"group",8,"Workspace Settings"),a=S(t,"items",24,()=>[]),n=S(t,"item",28,()=>{}),c=S(t,"mode",8,"tree"),h=S(t,"selectedId",28,()=>{}),A=S(t,"onNavigationChangeItem",8,U=>{}),z=S(t,"showButton",8,!0),w=S(t,"class",8,""),i=J(new Map);we(()=>(k(h()),k(n()),k(a())),()=>{var U;h()?n(a().find(x=>(x==null?void 0:x.id)===h())):h((U=n())==null?void 0:U.id)}),we(()=>(k(a()),k(r())),()=>{u(i,a().reduce((U,x)=>{if(!x)return U;const I=x.group??r(),V=U.get(I)??[];return V.push(x),U.set(I,V),U},new Map))}),we(()=>(k(n()),k(a())),()=>{n()||n(a()[0])}),we(()=>(k(A()),k(h())),()=>{A()(h())}),nt(),rt();var Y=cn(),O=o(Y),ae=U=>{oa(U,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return z()},minimized:!1,$$slots:{left:(x,I)=>{var V=sn(),W=o(V);aa(W,h,b=>{var d=tt(),E=pe(d);et(E,1,()=>e(i),bt,(j,p)=>{var f=it(()=>_r(e(p),2));let D=()=>e(f)[0];var y=rn(),$=o(y);ut($,t,"group",{get label(){return D()},get mode(){return c()}},re=>{var T=Xa(),L=o(T);qr(L);var _=l(L,2);se(_,{size:2,color:"primary",children:(q,Z)=>{var ge=R();ve(()=>xe(ge,D())),s(q,ge)},$$slots:{default:!0}}),s(re,T)});var N=l($,2);et(N,5,()=>e(f)[1],bt,(re,T)=>{var L=tn();let _;var q=o(L);se(q,{size:2,weight:"regular",color:"primary",children:(Z,ge)=>{var de=en(),ie=pe(de),Re=o(ie);$r(Re,()=>e(T).icon,(Ie,De)=>{De(Ie,{})});var Q=l(ie);ve(()=>xe(Q,` ${e(T),M(()=>e(T).name)??""}`)),s(Z,de)},$$slots:{default:!0}}),ve(Z=>_=vt(L,1,"c-navigation__item svelte-n5ccbo",null,_,Z),[()=>({"is-active":e(T).id===h()})],Ne),ft("click",L,()=>{return Z=e(T),n(Z),void h(Z==null?void 0:Z.id);var Z}),s(re,L)}),s(j,y)}),s(b,d)}),s(x,V)},right:(x,I)=>{Gr(x,{get item(){return n()},slot:"right",$$slots:{header:(V,W)=>{var b=tt(),d=pe(b);ut(d,t,"header",{get item(){return n()},get selectedId(){return h()}},null),s(V,b)},content:(V,W)=>{var b=tt(),d=pe(b);ut(d,t,"content",{get item(){return n()},get isSelected(){return k(n()),k(h()),M(()=>{var E;return((E=n())==null?void 0:E.id)===h()})}},E=>{var j=tt(),p=pe(j),f=D=>{var y=tt(),$=pe(y);$r($,()=>n().component,(N,re)=>{re(N,Zr(()=>n().props))}),s(D,y)};H(p,D=>{k(Hr),k(n()),k(c()),k(h()),M(()=>{return Hr(n())&&(y=n(),$=c(),N=h(),$!=="tree"||(y==null?void 0:y.id)===N);var y,$,N})&&D(f)}),s(E,j)}),s(V,b)}}})}}})},ce=U=>{var x=ln(),I=o(x);ut(I,t,"header",{get item(){return n()}},null);var V=l(I,2);et(V,1,()=>e(i),bt,(W,b)=>{var d=it(()=>_r(e(b),2));let E=()=>e(d)[0];var j=on(),p=pe(j),f=o(p);ut(f,t,"group",{get label(){return E()},get mode(){return c()}},y=>{se(y,{color:"secondary",size:2,weight:"medium",children:($,N)=>{var re=an(),T=pe(re);qr(o(T));var L=l(T,2),_=o(L);ve(()=>xe(_,E())),s($,re)},$$slots:{default:!0}})});var D=l(p,2);et(D,1,()=>e(d)[1],bt,(y,$)=>{var N=nn();Gr(o(N),{get item(){return e($)},$$slots:{content:(re,T)=>{var L=tt(),_=pe(L);ut(_,t,"content",{get item(){return e($)}},null),s(re,L)}}}),xs(N,(re,T)=>function(L,_){let q;function Z({scrollTo:ge,delay:de,options:ie}){clearTimeout(q),ge&&(q=setTimeout(()=>{L.scrollIntoView(ie)},de))}return Z(_),{update:Z,destroy(){clearTimeout(q)}}}(re,T),()=>({scrollTo:c()==="flat"&&e($).id===h(),delay:300,options:{behavior:"smooth"}})),s(y,N)}),s(W,j)}),s(U,x)};H(O,U=>{c()==="tree"?U(ae):U(ce,!1)}),ve(()=>vt(Y,1,`c-navigation c-navigation--mode__${c()??""} ${w()??""}`,"svelte-n5ccbo")),s(g,Y),je()}var un=lt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function vn(g){var t=un();s(g,t)}var hn=lt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function gn(g){var t=hn();s(g,t)}var pn=lt("<svg><!></svg>");function Jr(g,t){const r=zt(t,["children","$$slots","$$events","$$legacy"]);var a=pn();Ut(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...r}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),s(g,a)}var mn=v('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),fn=v("<span>Connect</span>"),$n=v('<div class="connect-button-content svelte-js5lik"><!></div>'),Cn=v('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),wn=v('<div slot="header-right"><!></div>'),yn=v("<div> </div>"),Sn=v('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),bn=v('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Mn=v('<div class="category-content"><!></div>'),kn=v('<div class="category"><div class="category-heading"><!></div> <!></div>');function cs(g,t){let r=S(t,"title",8),a=S(t,"loading",8,!1);var n=kn(),c=o(n),h=o(c);se(h,{size:1,color:"secondary",weight:"regular",children:(i,Y)=>{var O=R();ve(()=>xe(O,r())),s(i,O)},$$slots:{default:!0}});var A=l(c,2),z=i=>{var Y=bn(),O=o(Y);nr(O,{size:1});var ae=l(O,2);se(ae,{size:1,color:"secondary",children:(ce,U)=>{var x=R("Loading...");s(ce,x)},$$slots:{default:!0}}),s(i,Y)},w=i=>{var Y=Mn(),O=o(Y);ut(O,t,"default",{},null),s(i,Y)};H(A,i=>{a()?i(z):i(w,!1)}),s(g,n)}const ds="mcpServerModel";function kr(){const g=Tt(ds);if(!g)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return g}var xn=v('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),An=v("<span>Connect</span>"),zn=v('<div class="connect-button-content svelte-e3a21z"><!></div>'),Rn=v('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),In=v('<div slot="header-right"><!></div>'),Ln=v("<div> </div>"),_n=v('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),En=v('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Nn=v("<div><!></div>");function Tn(g,t){Be(t,!0);const[r,a]=pt(),n=()=>We(w,"$pretendNativeToolDefs",r);let c=S(t,"tools",19,()=>[]),h=S(t,"onToolApprovalConfigChange",3,()=>{});const A=Tt(Ft.key),z=kr(),w=A.getPretendNativeToolDefs(),i=z.getServers();let Y=it(()=>A.getEnableNativeRemoteMcp()?Xr(We(i,"$allServers",r)):[]);var O=Nn(),ae=o(O);const ce=it(()=>c().length===0);cs(ae,{get title(){return t.title},get loading(){return e(ce)},children:(U,x)=>{var I=En(),V=o(I);et(V,17,c,b=>b.name,(b,d)=>{(function(E,j){Be(j,!1);let p=S(j,"config",8),f=S(j,"onAuthenticate",8),D=S(j,"onRevokeAccess",8);const y=()=>{};let $=J(!1),N=J(null),re=J(!1),T=J();function L(){if(e($))u($,!1),e(N)&&(clearTimeout(e(N)),u(N,null));else{u($,!0);const de=p().authUrl||"";f()({authUrl:de,toolName:p().displayName.toLowerCase()}),u(N,setTimeout(()=>{u($,!1),u(N,null)},6e4))}}we(()=>k(p()),()=>{u(T,p().name.toLowerCase().replace(/[^a-z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""))}),we(()=>(k(p()),e($),e(N)),()=>{p().isConfigured&&e($)&&(u($,!1),e(N)&&(clearTimeout(e(N)),u(N,null)))}),nt(),rt();var _=Sn(),q=o(_);gt(q,{get icon(){return k(p()),M(()=>p().icon)},get title(){return k(p()),M(()=>p().displayName)},$$slots:{"header-right":(de,ie)=>{var Re=wn(),Q=o(Re),Ie=ye=>{const m=Ne(()=>e($)?"neutral":"accent");Je(ye,{variant:"ghost-block",get color(){return e(m)},size:1,$$events:{click:L},children:(C,X)=>{var B=$n(),oe=o(B),G=Me=>{var P=mn(),K=pe(P),F=o(K);nr(F,{size:1,useCurrentColor:!0}),s(Me,P)},$e=Me=>{var P=fn();s(Me,P)};H(oe,Me=>{e($)?Me(G):Me($e,!1)}),s(C,B)},$$slots:{default:!0}})},De=(ye,m)=>{var C=X=>{var B=Cn(),oe=o(B),G=o(oe),$e=o(G);let Me;var P=o($e);const K=Ne(()=>(k(Nt),M(()=>[Nt.Hover])));Ct(P,{get triggerOn(){return e(K)},content:"Revoke Access",children:(ee,ne)=>{Rt(ee,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>D()(p())},children:(he,me)=>{Ve(he,{name:"unplug",children:(fe,Se)=>{Jr(fe,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}});var F=l(G,2);br.Root(F,{color:"success",size:1,variant:"soft",children:(ee,ne)=>{var he=R("Connected");s(ee,he)},$$slots:{default:!0}}),ve(ee=>Me=vt($e,1,"icon-button-wrapper svelte-js5lik",null,Me,ee),[()=>({active:e(re)})],Ne),s(X,B)};H(ye,X=>{k(p()),M(()=>p().isConfigured)&&X(C)},m)};H(Q,ye=>{k(p()),M(()=>!p().isConfigured&&p().authUrl)?ye(Ie):ye(De,!1)}),s(de,Re)}}});var Z=l(q,2),ge=de=>{var ie=yn(),Re=o(ie);ve(()=>{vt(ie,1,`status-message ${k(p()),M(()=>p().statusType)??""}`,"svelte-js5lik"),xe(Re,(k(p()),M(()=>p().statusMessage)))}),s(de,ie)};H(Z,de=>{k(p()),M(()=>p().showStatus)&&de(ge)}),ve(()=>St(_,"id",e(T))),ft("mouseenter",_,()=>u(re,!0)),ft("mouseleave",_,()=>u(re,!1)),s(E,_),os(j,"onToolApprovalConfigChange",y),je({onToolApprovalConfigChange:y})})(b,{get config(){return e(d)},get onAuthenticate(){return t.onAuthenticate},get onRevokeAccess(){return t.onRevokeAccess},onToolApprovalConfigChange:h()})});var W=l(V,2);et(W,1,n,b=>b.name,(b,d)=>{const E=it(()=>e(Y).find(j=>j.name===e(d).name));(function(j,p){Be(p,!1);let f=S(p,"config",12),D=S(p,"mcpTool",8);const y=kr(),$=ts();async function N(){if(e(T))return _&&(clearTimeout(_),_=null),void u(T,!1);$.startRemoteMCPAuth(f().name),u(T,!0);const ie=new Promise(Re=>{_=setTimeout(()=>{Re(),_=null},6e4)});await Promise.race([ie]),u(T,!1)}async function re(){await $.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${f().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(D()&&y.deleteServer(D()),u(T,!1))}let T=J(!1),L=J(!1),_=null;we(()=>(k(f()),Fr),()=>{f(Fr(f()))}),we(()=>k(D()),()=>{f(f().isConfigured=!!D(),!0)}),nt(),rt();var q=_n(),Z=o(q);gt(Z,{get icon(){return k(f()),M(()=>f().icon)},get title(){return k(f()),M(()=>f().displayName)},$$slots:{"header-right":(ie,Re)=>{var Q=In(),Ie=o(Q),De=m=>{const C=Ne(()=>e(T)?"neutral":"accent");Je(m,{variant:"ghost-block",get color(){return e(C)},size:1,$$events:{click:N},children:(X,B)=>{var oe=zn(),G=o(oe),$e=P=>{var K=xn(),F=pe(K),ee=o(F);nr(ee,{size:1,useCurrentColor:!0}),s(P,K)},Me=P=>{var K=An();s(P,K)};H(G,P=>{e(T)?P($e):P(Me,!1)}),s(X,oe)},$$slots:{default:!0}})},ye=(m,C)=>{var X=B=>{var oe=Rn(),G=o(oe);let $e;var Me=o(G);const P=Ne(()=>(k(Nt),M(()=>[Nt.Hover])));Ct(Me,{get triggerOn(){return e(P)},content:"Revoke Access",children:(F,ee)=>{Rt(F,{color:"neutral",variant:"ghost",size:1,$$events:{click:re},children:(ne,he)=>{Ve(ne,{name:"unplug",children:(me,fe)=>{Jr(me,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}});var K=l(G,2);br.Root(K,{color:"success",size:1,variant:"soft",children:(F,ee)=>{var ne=R("Connected");s(F,ne)},$$slots:{default:!0}}),ve(F=>$e=vt(G,1,"disconnect-button svelte-e3a21z",null,$e,F),[()=>({active:e(L)})],Ne),s(B,oe)};H(m,B=>{k(f()),M(()=>f().isConfigured)&&B(X)},C)};H(Ie,m=>{k(f()),M(()=>!f().isConfigured)?m(De):m(ye,!1)}),s(ie,Q)}}});var ge=l(Z,2),de=ie=>{var Re=Ln(),Q=o(Re);ve(()=>{vt(Re,1,`status-message ${k(f()),M(()=>f().statusType)??""}`,"svelte-e3a21z"),xe(Q,(k(f()),M(()=>f().statusMessage)))}),s(ie,Re)};H(ge,ie=>{k(f()),M(()=>f().showStatus)&&ie(de)}),ft("mouseenter",q,()=>u(L,!0)),ft("mouseleave",q,()=>u(L,!1)),s(j,q),je()})(b,{get mcpTool(){return e(E)},get config(){return e(d)}})}),s(U,I)},$$slots:{default:!0}}),s(g,O),je(),a()}var Pn=v('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Fn=v('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Un(g,t){Be(t,!1);let r=S(t,"handleEnterEditMode",8),a=S(t,"envVarEntries",28,()=>[]);rt();var n=Fn(),c=pe(n);se(c,{size:1,weight:"medium",children:(O,ae)=>{var ce=R("Environment Variables");s(O,ce)},$$slots:{default:!0}});var h=l(c,2),A=o(h),z=o(A),w=O=>{var ae=tt(),ce=pe(ae);et(ce,1,a,U=>U.id,(U,x,I)=>{var V=Pn(),W=o(V),b=o(W);At(b,{size:1,placeholder:"Name",class:"full-width",get value(){return e(x).key},set value(f){e(x).key=f,Er(()=>a())},$$events:{focus(...f){var D;(D=r())==null||D.apply(this,f)},change:()=>function(f,D){const y=a().findIndex($=>$.id===f);y!==-1&&(a(a()[y].key=D,!0),a(a()))}(e(x).id,e(x).key)},$$legacy:!0});var d=l(W),E=o(d);At(E,{size:1,placeholder:"Value",class:"full-width",get value(){return e(x).value},set value(f){e(x).value=f,Er(()=>a())},$$events:{focus(...f){var D;(D=r())==null||D.apply(this,f)},change:()=>function(f,D){const y=a().findIndex($=>$.id===f);y!==-1&&(a(a()[y].value=D,!0),a(a()))}(e(x).id,e(x).value)},$$legacy:!0});var j=l(d),p=o(j);Ct(p,{content:"Remove",children:(f,D)=>{Je(f,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...y){var $;($=r())==null||$.apply(this,y)},click:()=>{return y=e(x).id,r()(),void a(a().filter($=>$.id!==y));var y}},$$slots:{iconLeft:(y,$)=>{Ve(y,{slot:"iconLeft",name:"trash-2",children:(N,re)=>{rs(N,{})},$$slots:{default:!0}})}}})},$$slots:{default:!0}}),s(U,V)}),s(O,ae)};H(z,O=>{k(a()),M(()=>a().length>0)&&O(w)});var i=l(h,2),Y=o(i);Je(Y,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){r()(),a([...a(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(O,ae)=>{var ce=R("Variable");s(O,ce)},$$slots:{default:!0,iconLeft:(O,ae)=>{Ve(O,{slot:"iconLeft",name:"plus",children:(ce,U)=>{Pt(ce,{})},$$slots:{default:!0}})}}}),s(g,n),je()}var On=v("<div></div>"),Vn=v(" <!>",1),Dn=v('<div class="server-name svelte-1koxb3z"><!></div>'),Hn=v('<div slot="header-left" class="l-header svelte-1koxb3z"><!> <!> <!> <div class="command-text svelte-1koxb3z"><!></div></div>'),qn=v('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Gn=v('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Jn=v('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Bn=v("<!> <!> <!>",1),jn=v("<!> <!>",1),Wn=v('<div class="server-actions svelte-1koxb3z" slot="header-right"><!> <div class="status-controls svelte-1koxb3z"><!> <!></div></div>'),Zn=v('<div class="c-tool-item svelte-1koxb3z"><div class="c-tool-info svelte-1koxb3z"><div class="tool-status svelte-1koxb3z"><div></div> <!></div> <div class="c-tool-description svelte-1koxb3z"><!></div></div></div>'),Kn=v('<div slot="footer"></div>'),Qn=v('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>',1),Yn=v('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!> <div class="connection-type-buttons svelte-1koxb3z"><!> <!></div></div></div>'),Xn=v('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),eo=v('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),to=v('<!> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <!>',1),ro=v('<form><div class="server-edit-form svelte-1koxb3z"><div class="server-header svelte-1koxb3z"><div class="server-title svelte-1koxb3z"><div class="server-icon svelte-1koxb3z"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-1koxb3z"><div><!></div> <div class="form-actions svelte-1koxb3z"><!> <!></div></div></div></form>');function Br(g,t){var ye;Be(t,!1);const r=J(),a=J(),n=J(),c=J(),h=J(),A=J(),z=J(),w=J();let i=S(t,"server",8,null),Y=S(t,"onDelete",8),O=S(t,"onAdd",8),ae=S(t,"onSave",8),ce=S(t,"onEdit",8),U=S(t,"onToggleDisableServer",8),x=S(t,"onJSONImport",8),I=S(t,"onCancel",8),V=S(t,"onAuthenticate",24,()=>{}),W=S(t,"disabledText",24,()=>{}),b=S(t,"warningText",24,()=>{}),d=S(t,"mode",12,"view"),E=S(t,"mcpServerError",12,""),j=J(0),p=J(((ye=i())==null?void 0:ye.name)??""),f=J(dt(i())?"":Et(i())?i().command:""),D=J(dt(i())?i().url:""),y=Et(i())?i().env??{}:{},$=J(""),N=J(dt(i())?i().type:"http"),re=J([]);L();let T=J(!0);function L(){u(re,Object.entries(y).map(([m,C])=>({id:crypto.randomUUID(),key:m,value:C})))}let _=J(()=>{});function q(){i()&&d()==="view"&&(d("edit"),ce()(i()),e(_)())}let Z=S(t,"busy",12,!1);function ge({key:m,value:C}){return m.trim()&&C.trim()}async function de(){E(""),Z(!0);const m=e(re).filter(ge);y=Object.fromEntries(m.map(({key:C,value:X})=>[C.trim(),X.trim()])),L();try{if(d()==="add"){const C={type:"stdio",name:e(p).trim(),command:e(f).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(y).length>0?y:void 0};await O()(C)}else if(d()==="addRemote"){const C={type:e(N),name:e(p).trim(),url:e(D).trim()};await O()(C)}else if(d()==="addJson"){try{JSON.parse(e($))}catch(C){const X=C instanceof Error?C.message:String(C);throw new at(`Invalid JSON format: ${X}`)}await x()(e($))}else if(d()==="edit"&&i()){if(dt(i())){const C={...i(),type:e(N),name:e(p).trim(),url:e(D).trim()};await ae()(C)}else if(Et(i())){const C={...i(),name:e(p).trim(),command:e(f).trim(),arguments:"",env:Object.keys(y).length>0?y:void 0};await ae()(C)}}}catch(C){E(C instanceof at?C.message:"Failed to save server"),console.warn(C)}finally{Z(!1)}}function ie(){var m,C;Z(!1),E(""),(m=I())==null||m(),u($,""),u(p,((C=i())==null?void 0:C.name)??""),u(f,dt(i())?"":Et(i())?i().command:""),u(D,dt(i())?i().url:""),y=Et(i())&&i().env?{...i().env}:{},u(N,dt(i())?i().type:"http"),L()}we(()=>k(i()),()=>{var m;u(r,((m=i())==null?void 0:m.tools)??[])}),we(()=>k(i()),()=>{u(a,dt(i()))}),we(()=>e(j),()=>{u(n,Date.now()-e(j)<3e3)}),we(()=>(k(i()),e(a),e(r),e(n)),()=>{var m;u(c,!((m=i())!=null&&m.disabled)&&e(a)&&i().authRequired===!0&&e(r).length===0&&!e(n))}),we(()=>(e(p),e(f)),()=>{e(p)&&e(f)&&E("")}),we(()=>(k(d()),e(p),e(f),e(D)),()=>{u(h,!((d()!=="add"||e(p).trim()&&e(f).trim())&&(d()!=="addRemote"||e(p).trim()&&e(D).trim())))}),we(()=>(k(d()),e($)),()=>{u(A,d()==="addJson"&&!e($).trim())}),we(()=>(e(h),k(d()),e(A)),()=>{u(z,e(h)||d()==="view"||e(A))}),we(()=>k(d()),()=>{u(w,(()=>{switch(d()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),nt(),rt();var Re=tt();ve(()=>{console.log({server:M(()=>Vr(i())),serverTools:M(()=>Vr(e(r)))})});var Q=pe(Re),Ie=m=>{ns(m,{get collapsed(){return e(T)},set collapsed(C){u(T,C)},$$slots:{header:(C,X)=>{gt(C,{slot:"header",$$slots:{"header-left":(B,oe)=>{var G=Hn(),$e=o(G),Me=me=>{Rt(me,{size:1,variant:"ghost",$$events:{click:()=>u(T,!e(T))},children:(fe,Se)=>{var ke=tt(),Pe=pe(ke),Ce=te=>{Ve(te,{name:"chevron-right",children:(ue,be)=>{Bs(ue,{})},$$slots:{default:!0}})},le=te=>{Ve(te,{name:"chevron-down",children:(ue,be)=>{Rr(ue,{})},$$slots:{default:!0}})};H(Pe,te=>{e(T)?te(Ce):te(le,!1)}),s(fe,ke)},$$slots:{default:!0}})};H($e,me=>{e(r),M(()=>e(r).length>0)&&me(Me)});var P=l($e,2);const K=Ne(()=>W()||(e(c)?"Authentication required":b()));Ct(P,{get content(){return e(K)},children:(me,fe)=>{var Se=On();let ke;ve(Pe=>ke=vt(Se,1,"c-dot svelte-1koxb3z",null,ke,Pe),[()=>({"c-green":!W()&&!e(c),"c-warning":e(c)||!W()&&!!b(),"c-red":!!W()&&!e(c),"c-disabled":i().disabled})],Ne),s(me,Se)},$$slots:{default:!0}});var F=l(P,2);Ct(F,{get content(){return k(i()),M(()=>i().name)},side:"top",align:"start",children:(me,fe)=>{var Se=Dn(),ke=o(Se);se(ke,{size:1,weight:"medium",children:(Pe,Ce)=>{var le=Vn(),te=pe(le),ue=l(te),be=Fe=>{var Ae=R();ve(()=>xe(Ae,`(${e(r),M(()=>e(r).length)??""}) tools`)),s(Fe,Ae)};H(ue,Fe=>{e(r),M(()=>e(r).length>0)&&Fe(be)}),ve(()=>xe(te,`${k(i()),M(()=>i().name)??""} `)),s(Pe,le)},$$slots:{default:!0}}),s(me,Se)},$$slots:{default:!0}});var ee=l(F,2),ne=o(ee);const he=Ne(()=>(k(tr),k(i()),M(()=>tr(i()))));Ct(ne,{get content(){return e(he)},side:"top",align:"start",children:(me,fe)=>{se(me,{color:"secondary",size:1,weight:"regular",children:(Se,ke)=>{var Pe=R();ve(Ce=>xe(Pe,Ce),[()=>(k(tr),k(i()),M(()=>tr(i())))],Ne),s(Se,Pe)},$$slots:{default:!0}})},$$slots:{default:!0}}),s(B,G)},"header-right":(B,oe)=>{var G=Wn(),$e=o(G),Me=ne=>{Je(ne,{size:1,variant:"surface",color:"warning",$$events:{click:()=>{var he;(he=V())==null||he(i())}},children:(he,me)=>{var fe=R("Authenticate");s(he,fe)},$$slots:{default:!0}})};H($e,ne=>{e(c)&&ne(Me)});var P=l($e,2),K=o(P),F=ne=>{const he=Ne(()=>(k(i()),M(()=>!i().disabled)));rr(ne,{size:1,get checked(){return e(he)},onchange:()=>{i()&&(u(j,Date.now()),U()(i().id)),e(_)()}})};H(K,ne=>{k(Pr),M(Pr)&&ne(F)});var ee=l(K,2);Ze.Root(ee,{get requestClose(){return e(_)},set requestClose(ne){u(_,ne)},children:(ne,he)=>{var me=jn(),fe=pe(me);Ze.Trigger(fe,{children:(ke,Pe)=>{Rt(ke,{size:1,variant:"ghost-block",color:"neutral",children:(Ce,le)=>{Ve(Ce,{name:"ellipsis",children:(te,ue)=>{la(te,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}});var Se=l(fe,2);Ze.Content(Se,{side:"bottom",align:"end",children:(ke,Pe)=>{var Ce=Bn(),le=pe(Ce);Ze.Item(le,{onSelect:q,children:(be,Fe)=>{var Ae=qn(),ze=o(Ae);Ve(ze,{name:"square-pen",children:(Te,Qe)=>{ca(Te,{})},$$slots:{default:!0}});var _e=l(ze,2);se(_e,{size:1,weight:"medium",children:(Te,Qe)=>{var Ke=R("Edit");s(Te,Ke)},$$slots:{default:!0}}),s(be,Ae)},$$slots:{default:!0}});var te=l(le,2);Ze.Item(te,{onSelect:()=>{(function(){if(i()){const be=Qt.convertServerToJSON(i());navigator.clipboard.writeText(be)}})(),e(_)()},children:(be,Fe)=>{var Ae=Gn(),ze=o(Ae);Ve(ze,{name:"clipboard",children:(Te,Qe)=>{da(Te,{})},$$slots:{default:!0}});var _e=l(ze,2);se(_e,{size:1,weight:"medium",children:(Te,Qe)=>{var Ke=R("Copy JSON");s(Te,Ke)},$$slots:{default:!0}}),s(be,Ae)},$$slots:{default:!0}});var ue=l(te,2);Ze.Item(ue,{color:"error",onSelect:()=>{Y()(i()),e(_)()},children:(be,Fe)=>{var Ae=Jn(),ze=o(Ae);Ve(ze,{name:"trash-2",children:(Te,Qe)=>{rs(Te,{})},$$slots:{default:!0}});var _e=l(ze,2);se(_e,{size:1,weight:"medium",children:(Te,Qe)=>{var Ke=R("Delete");s(Te,Ke)},$$slots:{default:!0}}),s(be,Ae)},$$slots:{default:!0}}),s(ke,Ce)},$$slots:{default:!0}}),s(ne,me)},$$slots:{default:!0},$$legacy:!0}),s(B,G)}}})},footer:(C,X)=>{var B=Kn();et(B,5,()=>e(r),bt,(oe,G)=>{var $e=Zn(),Me=o($e),P=o(Me),K=o(P);let F;var ee=l(K,2);se(ee,{size:1,weight:"medium",children:(me,fe)=>{var Se=R();ve(()=>xe(Se,(e(G),M(()=>e(G).definition.mcp_tool_name||e(G).definition.name)))),s(me,Se)},$$slots:{default:!0}});var ne=l(P,2),he=o(ne);Ct(he,{get content(){return e(G),M(()=>e(G).definition.description)},align:"start",children:(me,fe)=>{var Se=tt(),ke=pe(Se),Pe=Ce=>{se(Ce,{size:1,color:"secondary",children:(le,te)=>{var ue=R();ve(()=>xe(ue,(e(G),M(()=>e(G).definition.description)))),s(le,ue)},$$slots:{default:!0}})};H(ke,Ce=>{e(G),M(()=>e(G).definition.description)&&Ce(Pe)}),s(me,Se)},$$slots:{default:!0}}),ve(me=>F=vt(K,1,"tool-status-dot svelte-1koxb3z",null,F,me),[()=>({enabled:e(G).enabled,disabled:!e(G).enabled})],Ne),s(oe,$e)}),s(C,B)}},$$legacy:!0})},De=m=>{var C=ro(),X=o(C),B=o(X),oe=o(B),G=o(oe),$e=o(G);Ve($e,{name:"cpu",children:(le,te)=>{Ps(le)},$$slots:{default:!0}});var Me=l(G,2);se(Me,{color:"secondary",size:1,weight:"medium",children:(le,te)=>{var ue=R();ve(()=>xe(ue,e(w))),s(le,ue)},$$slots:{default:!0}});var P=l(B,2),K=le=>{var te=Qn(),ue=pe(te),be=o(ue),Fe=o(be);se(Fe,{size:1,weight:"medium",children:(Te,Qe)=>{var Ke=R("Code Snippet");s(Te,Ke)},$$slots:{default:!0}});var Ae=l(ue,2),ze=o(Ae),_e=o(ze);zr(_e,{size:1,placeholder:"Paste JSON here...",get value(){return e($)},set value(Te){u($,Te)},$$legacy:!0}),s(le,te)},F=(le,te)=>{var ue=be=>{var Fe=to(),Ae=pe(Fe),ze=Ue=>{var Le=Yn(),Ge=o(Le),ct=o(Ge);se(ct,{size:1,weight:"medium",children:(Dt,Ir)=>{var Ht=R("Connection Type");s(Dt,Ht)},$$slots:{default:!0}});var ot=l(ct,2),It=o(ot);const Ot=Ne(()=>e(N)==="http"?"solid":"ghost"),lr=Ne(()=>e(N)==="http"?"accent":"neutral");Je(It,{size:1,get variant(){return e(Ot)},get color(){return e(lr)},type:"button",$$events:{click:()=>u(N,"http")},children:(Dt,Ir)=>{var Ht=R("HTTP");s(Dt,Ht)},$$slots:{default:!0}});var Vt=l(It,2);const vs=Ne(()=>e(N)==="sse"?"solid":"ghost"),hs=Ne(()=>e(N)==="sse"?"accent":"neutral");Je(Vt,{size:1,get variant(){return e(vs)},get color(){return e(hs)},type:"button",$$events:{click:()=>u(N,"sse")},children:(Dt,Ir)=>{var Ht=R("SSE");s(Dt,Ht)},$$slots:{default:!0}}),s(Ue,Le)};H(Ae,Ue=>{k(d()),k(i()),M(()=>{var Le,Ge;return d()==="addRemote"||d()==="edit"&&(((Le=i())==null?void 0:Le.type)==="http"||((Ge=i())==null?void 0:Ge.type)==="sse")})&&Ue(ze)});var _e=l(Ae,2),Te=o(_e),Qe=o(Te);At(Qe,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return e(p)},set value(Ue){u(p,Ue)},$$events:{focus:q},$$slots:{label:(Ue,Le)=>{se(Ue,{slot:"label",size:1,weight:"medium",children:(Ge,ct)=>{var ot=R("Name");s(Ge,ot)},$$slots:{default:!0}})}},$$legacy:!0});var Ke=l(_e,2),ht=Ue=>{var Le=Xn(),Ge=o(Le),ct=o(Ge);At(ct,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return e(D)},set value(ot){u(D,ot)},$$events:{focus:q},$$slots:{label:(ot,It)=>{se(ot,{slot:"label",size:1,weight:"medium",children:(Ot,lr)=>{var Vt=R("URL");s(Ot,Vt)},$$slots:{default:!0}})}},$$legacy:!0}),s(Ue,Le)},Oe=Ue=>{var Le=eo(),Ge=o(Le),ct=o(Ge);At(ct,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return e(f)},set value(ot){u(f,ot)},$$events:{focus:q},$$slots:{label:(ot,It)=>{se(ot,{slot:"label",size:1,weight:"medium",children:(Ot,lr)=>{var Vt=R("Command");s(Ot,Vt)},$$slots:{default:!0}})}},$$legacy:!0}),s(Ue,Le)};H(Ke,Ue=>{k(d()),k(i()),M(()=>{var Le,Ge;return d()==="addRemote"||((Le=i())==null?void 0:Le.type)==="http"||((Ge=i())==null?void 0:Ge.type)==="sse"})?Ue(ht):Ue(Oe,!1)}),s(be,Fe)};H(le,be=>{d()!=="add"&&d()!=="addRemote"&&d()!=="edit"||be(ue)},te)};H(P,le=>{d()==="addJson"?le(K):le(F,!1)});var ee=l(P,2),ne=le=>{Un(le,{handleEnterEditMode:q,get envVarEntries(){return e(re)},set envVarEntries(te){u(re,te)},$$legacy:!0})};H(ee,le=>{k(d()),k(dt),k(i()),M(()=>(d()==="add"||d()==="edit")&&!dt(i()))&&le(ne)});var he=l(ee,2),me=o(he);let fe;var Se=o(me);jt(Se,{variant:"soft",color:"error",size:1,icon:te=>{Ve(te,{name:"circle-x",children:(ue,be)=>{Fs(ue,{})},$$slots:{default:!0}})},children:(te,ue)=>{var be=R();ve(()=>xe(be,E())),s(te,be)},$$slots:{icon:!0,default:!0}});var ke=l(me,2),Pe=o(ke);Je(Pe,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:ie},children:(le,te)=>{var ue=R("Cancel");s(le,ue)},$$slots:{default:!0}});var Ce=l(Pe,2);Je(Ce,{size:1,variant:"solid",color:"accent",get loading(){return Z()},type:"submit",get disabled(){return e(z)},children:(le,te)=>{var ue=tt(),be=pe(ue),Fe=ze=>{var _e=R("Import");s(ze,_e)},Ae=(ze,_e)=>{var Te=Ke=>{var ht=R("Add");s(Ke,ht)},Qe=(Ke,ht)=>{var Oe=Le=>{var Ge=R("Add");s(Le,Ge)},Ue=(Le,Ge)=>{var ct=ot=>{var It=R("Save");s(ot,It)};H(Le,ot=>{d()==="edit"&&ot(ct)},Ge)};H(Ke,Le=>{d()==="addRemote"?Le(Oe):Le(Ue,!1)},ht)};H(ze,Ke=>{d()==="add"?Ke(Te):Ke(Qe,!1)},_e)};H(be,ze=>{d()==="addJson"?ze(Fe):ze(Ae,!1)}),s(le,ue)},$$slots:{default:!0}}),ve(le=>{vt(C,1,"c-mcp-server-card "+(d()==="add"||d()==="addJson"||d()==="addRemote"?"add-server-section":"server-item"),"svelte-1koxb3z"),fe=vt(me,1,"error-container svelte-1koxb3z",null,fe,le)},[()=>({"is-error":!!E()})],Ne),ft("submit",C,ia(de)),s(m,C)};return H(Q,m=>{d()==="view"&&i()?m(Ie):m(De,!1)}),s(g,Re),os(t,"setLocalEnvVarFormState",L),je({setLocalEnvVarFormState:L})}var so=v('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),ao=v('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),no=v('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),oo=v('<div class="installed-indicator svelte-8tbe79"><!></div>'),io=v('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),lo=v('<div class="mcp-service-item"><!></div>'),co=v('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),uo=v('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),vo=v('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),ho=v('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function go(g,t){Be(t,!1);let r=S(t,"onMCPServerAdd",24,()=>{}),a=S(t,"servers",24,()=>[]);const n=[{label:hr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:hr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:hr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],c="easyMCPInstall.collapsed";let h=J(!1),A=J(!1),z=J(null),w=J({}),i=J({});function Y(U){var W;if(!U.userInput)return;for(let b=0;b<U.userInput.length;b++){const d=U.userInput[b];let E;if(E=d.type==="environmentVariable"&&d.envVarName?d.envVarName:d.correspondingArg?d.correspondingArg:`input_${b}`,!((W=e(w)[E])==null?void 0:W.trim())){const p=e(i)[E];return void(p&&p.focus())}}let x=[U.command],I={};U.args&&x.push(...U.args);for(let b=0;b<U.userInput.length;b++){const d=U.userInput[b];let E;E=d.type==="environmentVariable"&&d.envVarName?d.envVarName:d.correspondingArg?d.correspondingArg:`input_${b}`;const j=e(w)[E].trim(),p=`"${j}"`;if(d.type==="environmentVariable"&&d.envVarName)I[d.envVarName]=j;else if(d.correspondingArg){const f=x.indexOf(d.correspondingArg);f!==-1?x.splice(f+1,0,p):x.push(d.correspondingArg,p)}else x.push(p)}const V={type:"stdio",name:U.label,command:x.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(I).length>0?I:void 0};r()&&r()(V),u(z,null),u(w,{})}function O(){u(z,null),u(w,{})}we(()=>{},()=>{const U=localStorage.getItem(c);if(U!==null)try{u(h,JSON.parse(U))}catch{localStorage.removeItem(c)}u(A,!0)}),we(()=>(e(A),e(h)),()=>{typeof window<"u"&&e(A)&&localStorage.setItem(c,JSON.stringify(e(h)))}),nt(),rt();var ae=ho(),ce=o(ae);ns(ce,{get collapsed(){return e(h)},set collapsed(U){u(h,U)},children:(U,x)=>{var I=co(),V=o(I);et(V,5,()=>n,bt,(W,b)=>{var d=lo();gt(o(d),{$$slots:{"header-left":(E,j)=>{var p=no(),f=o(p),D=o(f);se(D,{size:1,weight:"medium",children:(T,L)=>{var _=R();ve(()=>xe(_,(e(b),M(()=>e(b).label)))),s(T,_)},$$slots:{default:!0}});var y=l(f,2),$=T=>{se(T,{size:1,color:"secondary",children:(L,_)=>{var q=R();ve(()=>xe(q,(e(b),M(()=>e(b).description)))),s(L,q)},$$slots:{default:!0}})};H(y,T=>{e(b),M(()=>e(b).description)&&T($)});var N=l(y,2),re=T=>{var L=ao(),_=o(L);et(_,1,()=>(e(b),M(()=>e(b).userInput)),bt,(de,ie,Re)=>{var Q=so();const Ie=Ne(()=>(e(ie),M(()=>e(ie).type==="environmentVariable"&&e(ie).envVarName?e(ie).envVarName:e(ie).correspondingArg||`input_${Re}`)));var De=o(Q);se(De,{size:1,weight:"medium",color:"neutral",children:(B,oe)=>{var G=R();ve(()=>xe(G,(e(ie),M(()=>e(ie).label)))),s(B,G)},$$slots:{default:!0}});var ye=l(De,2),m=B=>{se(B,{size:1,color:"secondary",children:(oe,G)=>{var $e=R();ve(()=>xe($e,(e(ie),M(()=>e(ie).description)))),s(oe,$e)},$$slots:{default:!0}})};H(ye,B=>{e(ie),M(()=>e(ie).description)&&B(m)});var C=l(ye,2);const X=Ne(()=>(e(ie),M(()=>e(ie).placeholder||"")));At(C,{get placeholder(){return e(X)},size:1,variant:"surface",get value(){return e(w)[e(Ie)]},set value(B){cr(w,e(w)[e(Ie)]=B)},get textInput(){return e(i)[e(Ie)]},set textInput(B){cr(i,e(i)[e(Ie)]=B)},$$events:{keydown:B=>{B.key==="Enter"?Y(e(b)):B.key==="Escape"&&O()}},$$legacy:!0}),s(de,Q)});var q=l(_,2),Z=o(q);Je(Z,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>Y(e(b))},children:(de,ie)=>{var Re=R("Install");s(de,Re)},$$slots:{default:!0}});var ge=l(Z,2);Je(ge,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:O},children:(de,ie)=>{var Re=R("Cancel");s(de,Re)},$$slots:{default:!0}}),s(T,L)};H(N,T=>{e(z),e(b),M(()=>e(z)===e(b).label&&e(b).userInput)&&T(re)}),s(E,p)},"header-right":(E,j)=>{var p=io(),f=o(p),D=$=>{var N=oo(),re=o(N);br.Root(re,{color:"success",size:1,variant:"soft",children:(T,L)=>{var _=R("Installed");s(T,_)},$$slots:{default:!0}}),s($,N)},y=($,N)=>{var re=T=>{Rt(T,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(L){if(a().some(q=>q.name===L.label))return;if(L.userInput&&L.userInput.length>0)return u(w,{}),L.userInput.forEach((q,Z)=>{let ge;ge=q.type==="environmentVariable"&&q.envVarName?q.envVarName:q.correspondingArg?q.correspondingArg:`input_${Z}`,cr(w,e(w)[ge]=q.defaultValue||"")}),void u(z,L.label);const _={type:"stdio",name:L.label,command:L.command,arguments:"",useShellInterpolation:!0};r()&&r()(_)}(e(b))},children:(L,_)=>{Ve(L,{name:"plus",children:(q,Z)=>{Pt(q,{})},$$slots:{default:!0}})},$$slots:{default:!0}})};H($,T=>{e(z),e(b),M(()=>e(z)!==e(b).label)&&T(re)},N)};H(f,$=>{k(a()),e(b),M(()=>a().some(N=>N.name===e(b).label))?$(D):$(y,!1)}),s(E,p)}}}),s(W,d)}),s(U,I)},$$slots:{default:!0,header:(U,x)=>{var I=vo();gt(o(I),{$$slots:{"header-left":(V,W)=>{var b=uo(),d=o(b);na(d,{});var E=l(d,2);se(E,{color:"neutral",size:1,weight:"light",class:"card-title",children:(j,p)=>{var f=R("Easy MCP Installation");s(j,f)},$$slots:{default:!0}}),s(V,b)}}}),s(U,I)}},$$legacy:!0}),s(g,ae),je()}const po={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},mo={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},fo=As(),$o=new class{constructor(g){Ee(this,"strings");let t={[vr.vscode]:{},[vr.jetbrains]:mo,[vr.web]:{}};this.strings={...po,...t[g]}}get(g){return this.strings[g]}}(fo.clientType);var Co=v('<div class="section-heading-text">MCP</div>'),wo=v(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),yo=v('<div class="section-heading-text">Terminal</div>'),So=v("<!> <!>",1),bo=v("<!> <!>",1),Mo=v('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function ko(g,t){Be(t,!1);const r=J();let a=S(t,"supportedShells",24,()=>[]),n=S(t,"selectedShell",24,()=>{}),c=S(t,"startupScript",28,()=>{}),h=S(t,"onShellSelect",8),A=S(t,"onStartupScriptChange",8),z=J();we(()=>k(n()),()=>{var I;u(r,n()?(I=n(),a().find(V=>V.friendlyName===I)):void 0)}),nt(),rt();var w=Mo(),i=o(w);se(i,{size:1,weight:"regular",color:"secondary",children:(I,V)=>{var W=yo();s(I,W)},$$slots:{default:!0}});var Y=l(i,2),O=o(Y);se(O,{size:1,children:(I,V)=>{var W=R("Shell:");s(I,W)},$$slots:{default:!0}});var ae=l(O,2);Ze.Root(ae,{get requestClose(){return e(z)},set requestClose(I){u(z,I)},children:(I,V)=>{var W=bo(),b=pe(W);Ze.Trigger(b,{children:(E,j)=>{const p=Ne(()=>(k(a()),M(()=>a().length===0)));Je(E,{size:1,variant:"outline",color:"neutral",get disabled(){return e(p)},children:(f,D)=>{var y=So(),$=pe(y),N=L=>{var _=R();ve(()=>xe(_,`${e(r),M(()=>e(r).friendlyName)??""}
            (${e(r),M(()=>e(r).supportString)??""})`)),s(L,_)},re=(L,_)=>{var q=ge=>{var de=R("No shells available");s(ge,de)},Z=ge=>{var de=R("Select a shell");s(ge,de)};H(L,ge=>{k(a()),M(()=>a().length===0)?ge(q):ge(Z,!1)},_)};H($,L=>{e(r),k(a()),M(()=>e(r)&&a().length>0)?L(N):L(re,!1)});var T=l($,2);Ve(T,{name:"chevron-down",children:(L,_)=>{Os(L)},$$slots:{default:!0}}),s(f,y)},$$slots:{default:!0}})},$$slots:{default:!0}});var d=l(b,2);Ze.Content(d,{side:"bottom",align:"start",children:(E,j)=>{var p=tt(),f=pe(p),D=$=>{var N=tt(),re=pe(N);et(re,1,a,T=>T.friendlyName,(T,L)=>{const _=Ne(()=>(k(n()),e(L),M(()=>n()===e(L).friendlyName)));Ze.Item(T,{onSelect:()=>{h()(e(L).friendlyName),e(z)()},get highlight(){return e(_)},children:(q,Z)=>{var ge=R();ve(()=>xe(ge,`${e(L),M(()=>e(L).friendlyName)??""}
              (${e(L),M(()=>e(L).supportString)??""})`)),s(q,ge)},$$slots:{default:!0}})}),s($,N)},y=$=>{Ze.Label($,{children:(N,re)=>{var T=R("No shells available");s(N,T)},$$slots:{default:!0}})};H(f,$=>{k(a()),M(()=>a().length>0)?$(D):$(y,!1)}),s(E,p)},$$slots:{default:!0}}),s(I,W)},$$slots:{default:!0},$$legacy:!0});var ce=l(Y,2),U=o(ce);se(U,{size:1,children:(I,V)=>{var W=R("Start-up script: Code to run wherever a new terminal is opened");s(I,W)},$$slots:{default:!0}});var x=l(U,2);zr(x,{onchange:function(I){const V=I.target;A()(V.value)},placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return c()},set value(I){c(I)},$$legacy:!0}),s(g,w),je()}var xo=v('<div class="section-heading-text">Sound Settings</div>'),Ao=v('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),zo=v('<div slot="header-right"><!></div>'),Ro=v('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Io=v('<div slot="header-right"><!></div>'),Lo=v('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),_o=v('<div slot="header-right"><!></div>'),Eo=v("<!> <!>",1),No=v('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),To=v('<div class="section-heading-text">Agent Settings</div>'),Po=v('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Fo=v('<div slot="header-right"><!></div>'),Uo=v('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),Oo=v('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function Vo(g,t){let r=S(t,"tools",19,()=>[]),a=S(t,"isMCPEnabled",3,!0),n=S(t,"isMCPImportEnabled",3,!0),c=S(t,"isTerminalEnabled",3,!0),h=S(t,"isSoundCategoryEnabled",3,!1),A=S(t,"isAgentCategoryEnabled",3,!1),z=S(t,"isSwarmModeFeatureFlagEnabled",3,!1),w=S(t,"hasEverUsedRemoteAgent",3,!1),i=S(t,"onToolApprovalConfigChange",3,()=>{}),Y=S(t,"onCancel",19,()=>{}),O=S(t,"supportedShells",19,()=>[]),ae=S(t,"selectedShell",19,()=>{}),ce=S(t,"startupScript",19,()=>{}),U=S(t,"onShellSelect",3,()=>{}),x=S(t,"onStartupScriptChange",3,()=>{});var I=Oo(),V=o(I);Tn(V,{title:"Services",get tools(){return r()},get onAuthenticate(){return t.onAuthenticate},get onRevokeAccess(){return t.onRevokeAccess},onToolApprovalConfigChange:i()});var W=l(V,2),b=y=>{(function($,N){Be(N,!1);const[re,T]=pt(),L=()=>We(Me,"$enableNativeRemoteMcp",re),_=()=>We(P,"$allServers",re),q=J(),Z=J();let ge=S(N,"onMCPServerAdd",8),de=S(N,"onMCPServerSave",8),ie=S(N,"onMCPServerDelete",8),Re=S(N,"onMCPServerToggleDisable",8),Q=S(N,"onCancel",24,()=>{}),Ie=S(N,"onMCPServerJSONImport",8),De=S(N,"isMCPImportEnabled",8,!0);const ye=ts();function m(Oe){ye.startRemoteMCPAuth(Oe.name)}let C=J(null),X=J(null);function B(){var Oe;u(C,null),u(X,null),(Oe=Q())==null||Oe()}let oe=J([]);const G=Tt(Ft.key),$e=kr(),Me=G.getEnableNativeRemoteMcp(),P=$e.getServers();function K(Oe){u(C,Oe.id)}function F(Oe){return async function(...Ue){const Le=await Oe(...Ue);return u(X,null),u(C,null),Le}}const ee=F(ge()),ne=F(de()),he=F(Ie()),me=F(ie()),fe=F(Re()),Se=$o.get("mcpDocsURL");we(()=>(e(X),e(C)),()=>{u(q,e(X)==="add"||e(X)==="addJson"||e(X)==="addRemote"||e(C)!==null)}),we(()=>(L(),_()),()=>{L()?u(oe,Us(_())):u(oe,_())}),we(()=>e(oe),()=>{u(Z,Qt.parseServerValidationMessages(e(oe)))}),nt(),rt();var ke=wo(),Pe=pe(ke),Ce=o(Pe),le=o(Ce);se(le,{size:1,weight:"regular",color:"secondary",children:(Oe,Ue)=>{var Le=Co();s(Oe,Le)},$$slots:{default:!0}});var te=l(Ce,2),ue=l(o(te)),be=l(te,2);go(be,{get onMCPServerAdd(){return ee},get servers(){return e(oe)}});var Fe=l(be,2);et(Fe,1,()=>e(oe),Oe=>Oe.id,(Oe,Ue)=>{const Le=Ne(()=>(e(C),e(Ue),M(()=>e(C)===e(Ue).id?"edit":"view"))),Ge=Ne(()=>(e(Z),e(Ue),M(()=>e(Z).errors.get(e(Ue).id)))),ct=Ne(()=>(e(Z),e(Ue),M(()=>e(Z).warnings.get(e(Ue).id))));Br(Oe,{get mode(){return e(Le)},get server(){return e(Ue)},get onAdd(){return ee},get onSave(){return ne},get onDelete(){return me},get onToggleDisableServer(){return fe},onEdit:K,onCancel:B,get onJSONImport(){return he},onAuthenticate:m,get disabledText(){return e(Ge)},get warningText(){return e(ct)}})});var Ae=l(Pe,2),ze=Oe=>{Br(Oe,{get mode(){return e(X)},get onAdd(){return ee},get onSave(){return ne},get onDelete(){return me},get onToggleDisableServer(){return fe},onEdit:K,onCancel:B,get onJSONImport(){return he},onAuthenticate:m})};H(Ae,Oe=>{e(X)!=="add"&&e(X)!=="addJson"&&e(X)!=="addRemote"||Oe(ze)});var _e=l(Ae,2),Te=o(_e);Je(Te,{get disabled(){return e(q)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{u(X,"add")}},children:(Oe,Ue)=>{var Le=R("Add MCP");s(Oe,Le)},$$slots:{default:!0,iconLeft:(Oe,Ue)=>{Ve(Oe,{slot:"iconLeft",name:"plus",children:(Le,Ge)=>{Pt(Le,{})},$$slots:{default:!0}})}}});var Qe=l(Te,2);Je(Qe,{get disabled(){return e(q)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{u(X,"addRemote")}},children:(Oe,Ue)=>{var Le=R("Add remote MCP");s(Oe,Le)},$$slots:{default:!0,iconLeft:(Oe,Ue)=>{Ve(Oe,{slot:"iconLeft",name:"plus",children:(Le,Ge)=>{Pt(Le,{})},$$slots:{default:!0}})}}});var Ke=l(Qe,2),ht=Oe=>{Je(Oe,{get disabled(){return e(q)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{u(X,"addJson")}},children:(Ue,Le)=>{var Ge=R("Import from JSON");s(Ue,Ge)},$$slots:{default:!0,iconLeft:(Ue,Le)=>{Ve(Ue,{slot:"iconLeft",name:"download",children:(Ge,ct)=>{ss(Ge,{})},$$slots:{default:!0}})}}})};H(Ke,Oe=>{De()&&Oe(ht)}),ve(()=>St(ue,"href",Se)),s($,ke),je(),T()})(y,{get onMCPServerAdd(){return t.onMCPServerAdd},get onMCPServerSave(){return t.onMCPServerSave},get onMCPServerDelete(){return t.onMCPServerDelete},get onMCPServerToggleDisable(){return t.onMCPServerToggleDisable},get onMCPServerJSONImport(){return t.onMCPServerJSONImport},get onCancel(){return Y()},get isMCPImportEnabled(){return n()}})};H(W,y=>{a()&&y(b)});var d=l(W,2),E=y=>{ko(y,{get supportedShells(){return O()},get selectedShell(){return ae()},get startupScript(){return ce()},onShellSelect:U(),onStartupScriptChange:x()})};H(d,y=>{c()&&y(E)});var j=l(d,2),p=y=>{(function($,N){Be(N,!1);const[re,T]=pt(),L=()=>We(e(_),"$currentSettings",re),_=J(),q=J(),Z=Tt(Sr.key);async function ge(){return await Z.playAgentComplete({playOnlyWhenWindowUnfocused:!1}),"success"}we(()=>{},()=>{Bt(u(_,Z.getCurrentSettings),"$currentSettings",re)}),we(()=>L(),()=>{u(q,L().enabled)}),nt(),rt();var de=No(),ie=pe(de);se(ie,{size:1,weight:"regular",color:"secondary",children:(ye,m)=>{var C=xo();s(ye,C)},$$slots:{default:!0}});var Re=l(ie,2),Q=o(Re);gt(Q,{$$slots:{"header-left":(ye,m)=>{var C=Ao(),X=o(C),B=o(X);se(B,{size:2,weight:"medium",children:($e,Me)=>{var P=R("Enable Sound Effects");s($e,P)},$$slots:{default:!0}});var oe=l(X,2),G=o(oe);se(G,{size:1,weight:"medium",children:($e,Me)=>{var P=R("Play a sound when an agent completes a task");s($e,P)},$$slots:{default:!0}}),s(ye,C)},"header-right":(ye,m)=>{var C=zo(),X=o(C);rr(X,{size:1,get checked(){return e(q)},ariaLabel:"Enable sound effects",onchange:()=>Z.updateEnabled(!e(q))}),s(ye,C)}}});var Ie=l(Q,2),De=ye=>{var m=Eo(),C=pe(m);gt(C,{$$slots:{"header-left":(X,B)=>{var oe=Ro(),G=o(oe),$e=o(G);se($e,{size:2,weight:"medium",children:(K,F)=>{var ee=R("Only play when app window is unfocused");s(K,ee)},$$slots:{default:!0}});var Me=l(G,2),P=o(Me);se(P,{size:1,weight:"medium",children:(K,F)=>{var ee=R("Play the agent completion sound only when the host window is not focused");s(K,ee)},$$slots:{default:!0}}),s(X,oe)},"header-right":(X,B)=>{var oe=Io(),G=o(oe);const $e=Ne(()=>(L(),M(()=>L().playOnlyWhenWindowUnfocused??!1)));rr(G,{size:1,get checked(){return e($e)},ariaLabel:"Only play when app window is unfocused",onchange:()=>Z.updateSettings({playOnlyWhenWindowUnfocused:!L().playOnlyWhenWindowUnfocused})}),s(X,oe)}}}),gt(l(C,2),{$$slots:{"header-left":(X,B)=>{var oe=Lo(),G=o(oe),$e=o(G);se($e,{size:2,weight:"medium",children:(K,F)=>{var ee=R("Test Sound");s(K,ee)},$$slots:{default:!0}});var Me=l(G,2),P=o(Me);se(P,{size:1,weight:"medium",children:(K,F)=>{var ee=R("Play a sample of the agent completion sound");s(K,ee)},$$slots:{default:!0}}),s(X,oe)},"header-right":(X,B)=>{var oe=_o(),G=o(oe);const $e=Ne(()=>e(q)?"":"Enable sound effects to test"),Me=Ne(()=>(k(Nt),M(()=>[Nt.Hover])));Ct(G,{get content(){return e($e)},get triggerOn(){return e(Me)},children:(P,K)=>{const F=Ne(()=>!e(q));ua(P,{size:1,defaultColor:"neutral",get enabled(){return e(q)},stickyColor:!1,get disabled(){return e(F)},onClick:ge,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(ee,ne)=>{var he=R("Play");s(ee,he)},$$slots:{default:!0}})},$$slots:{default:!0}}),s(X,oe)}}}),s(ye,m)};H(Ie,ye=>{e(q)&&ye(De)}),s($,de),je(),T()})(y,{})};H(j,y=>{h()&&y(p)});var f=l(j,2),D=y=>{(function($,N){Be(N,!1);const[re,T]=pt(),L=()=>We(e(_),"$currentSettings",re),_=J(),q=J();let Z=S(N,"isSwarmModeEnabled",8,!1),ge=S(N,"hasEverUsedRemoteAgent",8,!1);const de=Tt(Wt.key);we(()=>{},()=>{Bt(u(_,de.getCurrentSettings),"$currentSettings",re)}),we(()=>L(),()=>{u(q,L().enabled)}),nt(),rt();var ie=tt(),Re=pe(ie),Q=Ie=>{var De=Uo(),ye=pe(De);se(ye,{size:1,weight:"regular",color:"secondary",children:(C,X)=>{var B=To();s(C,B)},$$slots:{default:!0}});var m=l(ye,2);gt(o(m),{$$slots:{"header-left":(C,X)=>{var B=Po(),oe=o(B),G=o(oe);se(G,{size:2,weight:"medium",children:(F,ee)=>{var ne=R("Enable Swarm Mode");s(F,ne)},$$slots:{default:!0}});var $e=l(oe,2),Me=o($e);se(Me,{size:1,weight:"medium",children:(F,ee)=>{var ne=R("Allow agents to coordinate and work together on complex tasks");s(F,ne)},$$slots:{default:!0}});var P=l($e,2),K=o(P);se(K,{size:1,weight:"regular",color:"secondary",children:(F,ee)=>{var ne=R(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);s(F,ne)},$$slots:{default:!0}}),s(C,B)},"header-right":(C,X)=>{var B=Fo(),oe=o(B);rr(oe,{size:1,get checked(){return e(q)},onchange:()=>de.updateEnabled(!e(q))}),s(C,B)}}}),s(Ie,De)};H(Re,Ie=>{Z()&&ge()&&Ie(Q)}),s($,ie),je(),T()})(y,{get isSwarmModeEnabled(){return z()},get hasEverUsedRemoteAgent(){return w()}})};H(f,y=>{A()&&y(D)}),s(g,I)}var Do=lt("<svg><!></svg>");function Ho(g,t){const r=zt(t,["children","$$slots","$$events","$$legacy"]);var a=Do();Ut(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...r}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),s(g,a)}var qo=lt("<svg><!></svg>");function Go(g,t){const r=zt(t,["children","$$slots","$$events","$$legacy"]);var a=qo();Ut(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...r}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),s(g,a)}var Jo=lt("<svg><!></svg>");function jr(g,t){const r=zt(t,["children","$$slots","$$events","$$legacy"]);var a=Jo();Ut(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...r}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',!0),s(g,a)}var Bo=v('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1),jo=v('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function us(g,t){Be(t,!0);const[r,a]=pt();let n=S(t,"userGuidelines",15,""),c=S(t,"userGuidelinesLengthLimit",19,()=>{}),h=S(t,"updateUserGuideline",3,()=>!1);const A=Xe(void 0);function z(){const i=n().trim();if(We(A,"$originalValue",r)!==i){if(!h()(i))throw c()&&i.length>c()?`The user guideline must be less than ${c()} character long`:"An error occurred updating the user";Tr(A,i)}}Kr(()=>{Tr(A,n().trim())}),Cr(()=>{z()});var w=jo();(function(i,Y){Be(Y,!0);let O,ae=S(Y,"variant",3,"surface"),ce=S(Y,"size",3,2),U=S(Y,"color",19,()=>{}),x=S(Y,"resize",3,"none"),I=S(Y,"textInput",15),V=S(Y,"value",15,""),W=S(Y,"selectedText",15,""),b=S(Y,"selectionStart",15,0),d=S(Y,"selectionEnd",15,0),E=Ss(Y,["$$slots","$$events","$$legacy","variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","header","children","title"]),j=Nr(!1),p=Nr(void 0);const f=async()=>{try{Y.saveFunction(),u(j,!0),clearTimeout(O),O=setTimeout(()=>{u(j,!1)},1500)}catch(Q){u(p,Q instanceof Error?Q.message:String(Q),!0)}};function D(){I()&&(b(I().selectionStart),d(I().selectionEnd),b()!==d()?W(V().substring(b(),d())):W(""))}Cr(()=>{f()});var y=Bo(),$=pe(y),N=o($),re=o(N);dr(re,()=>Y.header??ur);var T=l(N,2);dr(T,()=>Y.children??ur);var L=l(T,2),_=o(L);dr(_,()=>Y.title??ur);var q=l(_,2);zr(q,Zr({get variant(){return ae()},get size(){return ce()},get color(){return U()},get resize(){return x()},placeholder:"Enter markdown content...",onselect:D,onmouseup:D,onkeyup:()=>{D()},onblur:f,onkeydown:Q=>{(Q.key==="Escape"||(Q.metaKey||Q.ctrlKey)&&Q.key==="s")&&(Q.preventDefault(),f())}},()=>E,{get textInput(){return I()},set textInput(Q){I(Q)},get value(){return V()},set value(Q){V(Q)}}));var Z=l($,2),ge=o(Z),de=Q=>{se(Q,{size:1,weight:"light",color:"error",children:(Ie,De)=>{var ye=R();ve(()=>xe(ye,e(p))),s(Ie,ye)},$$slots:{default:!0}})};H(ge,Q=>{e(p)&&Q(de)});var ie=l(ge,2),Re=Q=>{se(Q,{size:1,weight:"light",color:"success",children:(Ie,De)=>{var ye=R("Saved");s(Ie,ye)},$$slots:{default:!0}})};H(ie,Q=>{e(j)&&Q(Re)}),s(i,y),je()})(o(w),{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",rows:6,saveFunction:z,get value(){return n()},set value(i){n(i)}}),s(g,w),je(),a()}var Wo=v("<!> <!> <!>",1),Zo=v("<!> <!>",1),Ko=v('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),Qo=v('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),Yo=v("<!> <!>",1),Xo=v("<!> <!>",1),ei=v("<!> <!>",1),ti=v("<!> <!> <!> <!>",1),ri=v('<div class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),si=v("<!> <!>",1);function ai(g,t){Be(t,!1);const[r,a]=pt(),n=()=>We(e(ae),"$focusedIndex",r),c=J(),h=Qr();let A=S(t,"show",8,!1),z=S(t,"options",24,()=>[]),w=S(t,"isLoading",8,!1),i=S(t,"errorMessage",8,""),Y=S(t,"successMessage",8,""),O=J(e(c)),ae=J(void 0),ce=J(()=>{});function U(){e(O)&&!w()&&h("select",e(O))}function x(){w()||(h("cancel"),u(O,e(c)))}we(()=>k(z()),()=>{u(c,z().length>0?z()[0]:null)}),we(()=>(k(A()),e(c)),()=>{A()&&u(O,e(c))}),nt(),rt(),ft("keydown",xr,function(I){A()&&!w()&&(I.key==="Escape"?(I.preventDefault(),x()):I.key==="Enter"&&e(O)&&(I.preventDefault(),U()))}),is(g,{get show(){return A()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return w()},get preventEscapeClose(){return w()},$$events:{cancel:x},body:W=>{var b=ri(),d=o(b),E=p=>{var f=Ko();s(p,f)},j=p=>{var f=ti(),D=pe(f);se(D,{size:2,color:"secondary",children:(_,q)=>{var Z=R("Select existing rules to auto import to .augment/rules");s(_,Z)},$$slots:{default:!0}});var y=l(D,2);const $=Ne(()=>(k(z()),M(()=>z().length===0?[]:void 0)));Ze.Root(y,{get triggerOn(){return e($)},get requestClose(){return e(ce)},set requestClose(_){u(ce,_)},get focusedIndex(){return e(ae)},set focusedIndex(_){Bt(u(ae,_),"$focusedIndex",r)},children:(_,q)=>{var Z=ei(),ge=pe(Z);Ze.Trigger(ge,{children:(ie,Re)=>{var Q=Qo(),Ie=o(Q),De=l(Ie,2);Ve(De,{name:"chevron-down",children:(ye,m)=>{Rr(ye,{})},$$slots:{default:!0}}),ve(()=>bs(Ie,(e(O),M(()=>e(O)?e(O).label:"Existing rules")))),s(ie,Q)},$$slots:{default:!0}});var de=l(ge,2);Ze.Content(de,{align:"start",side:"bottom",children:(ie,Re)=>{var Q=Xo(),Ie=pe(Q);et(Ie,1,z,bt,(m,C)=>{const X=Ne(()=>(e(O),e(C),M(()=>{var B;return((B=e(O))==null?void 0:B.label)===e(C).label})));Ze.Item(m,{onSelect:()=>function(B){u(O,B),e(ce)()}(e(C)),get highlight(){return e(X)},children:(B,oe)=>{var G=R();ve(()=>xe(G,(e(C),M(()=>e(C).label)))),s(B,G)},$$slots:{default:!0}})});var De=l(Ie,2),ye=m=>{var C=Yo(),X=pe(C);Ze.Separator(X,{});var B=l(X,2);Ze.Label(B,{children:(oe,G)=>{var $e=R();ve(()=>xe($e,(n(),k(z()),e(O),M(()=>{var Me;return n()!==void 0?z()[n()].description:(Me=e(O))==null?void 0:Me.description})))),s(oe,$e)},$$slots:{default:!0}}),s(m,C)};H(De,m=>{(n()!==void 0||e(O))&&m(ye)}),s(ie,Q)},$$slots:{default:!0}}),s(_,Z)},$$slots:{default:!0},$$legacy:!0});var N=l(y,2),re=_=>{jt(_,{variant:"soft",color:"error",size:1,icon:Z=>{Ve(Z,{name:"circle-alert",children:(ge,de)=>{ar(ge,{})},$$slots:{default:!0}})},children:(Z,ge)=>{var de=R();ve(()=>xe(de,i())),s(Z,de)},$$slots:{icon:!0,default:!0}})};H(N,_=>{i()&&_(re)});var T=l(N,2),L=_=>{jt(_,{variant:"soft",color:"success",size:1,icon:Z=>{Ve(Z,{name:"circle-check",children:(ge,de)=>{Vs(ge,{})},$$slots:{default:!0}})},children:(Z,ge)=>{var de=R();ve(()=>xe(de,Y())),s(Z,de)},$$slots:{icon:!0,default:!0}})};H(T,_=>{Y()&&_(L)}),s(p,f)};H(d,p=>{k(z()),M(()=>z().length===0)?p(E):p(j,!1)}),s(W,b)},footer:W=>{var b=si(),d=pe(b);Je(d,{variant:"solid",color:"neutral",get disabled(){return w()},$$events:{click:x},children:(p,f)=>{var D=R("Cancel");s(p,D)},$$slots:{default:!0}});var E=l(d,2),j=p=>{const f=Ne(()=>!e(O)||w());Je(p,{color:"accent",variant:"solid",get disabled(){return e(f)},get loading(){return w()},$$events:{click:U},children:(D,y)=>{var $=R();ve(()=>xe($,w()?"Importing...":"Import ")),s(D,$)},$$slots:{default:!0}})};H(E,p=>{k(z()),M(()=>z().length>0)&&p(j)}),s(W,b)},$$slots:{body:!0,footer:!0}}),je(),a()}var ni=v('<div class="loading-container"><!> <!></div>'),oi=v('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),ii=v('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),li=v('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),ci=v('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),di=v('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),ui=v("<!> <!>",1),vi=v("<!> <!>",1),hi=v("<!> <!>",1),gi=v(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function pi(g,t){Be(t,!1);const[r,a]=pt(),n=()=>We(V,"$rulesFiles",r),c=()=>We(e(A),"$isRulesLoading",r),h=()=>We(e(y),"$importFocusedIndex",r),A=J(),z=J(),w=J(),i=J();let Y=S(t,"userGuidelines",8,""),O=S(t,"userGuidelinesLengthLimit",24,()=>{}),ae=S(t,"workspaceGuidelinesLengthLimit",24,()=>{}),ce=S(t,"workspaceGuidelinesContent",8,""),U=S(t,"updateUserGuideline",8,()=>!1),x=S(t,"rulesModel",8),I=S(t,"rulesController",8);const V=x().getCachedRules(),W=I().getShowCreateRuleDialog(),b=I().getCreateRuleError();let d=J(!1),E=J([]),j=J(!1),p=J(""),f=J("");const D=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let y=J(void 0),$=J(()=>{});async function N(P){try{P.id==="select_file_or_directory"?await I().selectFileToImport():P.id==="auto_import"&&await async function(){try{u(p,""),u(f,"");const K=await I().getAutoImportOptions();u(E,K.data.options),u(d,!0)}catch(K){console.error("Failed to get auto-import options:",K),u(p,"Failed to detect existing rules in workspace.")}}()}catch(K){console.error("Failed to handle import select:",K)}e($)&&e($)()}we(()=>k(x()),()=>{Bt(u(A,x().getLoading()),"$isRulesLoading",r)}),we(()=>k(ae()),()=>{u(z,ae())}),we(()=>(e(w),e(i),n(),k(ce()),e(z)),()=>{var P;P=Xs({rules:n(),workspaceGuidelinesContent:ce(),rulesAndGuidelinesLimit:e(z)}),u(w,P.isOverLimit),u(i,P.warningMessage)}),nt(),rt();var re=gi(),T=pe(re),L=o(T),_=o(L);se(_,{class:"c-section-header",size:3,color:"primary",children:(P,K)=>{var F=R("Rules");s(P,F)},$$slots:{default:!0}});var q=l(_,2),Z=l(o(q)),ge=o(Z);se(ge,{size:1,weight:"regular",children:(P,K)=>{var F=R("Learn more");s(P,F)},$$slots:{default:!0}});var de=l(q,2),ie=P=>{jt(P,{variant:"soft",color:"warning",size:1,icon:F=>{Ve(F,{name:"circle-alert",children:(ee,ne)=>{ar(ee,{})},$$slots:{default:!0}})},children:(F,ee)=>{var ne=R();ve(()=>xe(ne,e(i))),s(F,ne)},$$slots:{icon:!0,default:!0}})};H(de,P=>{e(w)&&P(ie)});var Re=l(de,2),Q=o(Re),Ie=P=>{var K=ni(),F=o(K);nr(F,{size:1});var ee=l(F,2);se(ee,{size:1,color:"secondary",children:(ne,he)=>{var me=R("Loading rules...");s(ne,me)},$$slots:{default:!0}}),s(P,K)},De=(P,K)=>{var F=ne=>{var he=oi(),me=o(he);se(me,{size:1,color:"neutral",children:(fe,Se)=>{var ke=R("No rules files found");s(fe,ke)},$$slots:{default:!0}}),s(ne,he)},ee=ne=>{var he=tt(),me=pe(he);et(me,1,n,fe=>fe.path,(fe,Se)=>{gt(fe,{isClickable:!0,$$events:{click:()=>I().openRule(e(Se).path)},$$slots:{"header-left":(ke,Pe)=>{var Ce=ii(),le=o(Ce),te=o(le),ue=ze=>{Ct(ze,{content:"No description found",children:(_e,Te)=>{Ve(_e,{name:"circle-alert",children:(Qe,Ke)=>{ar(Qe,{})},$$slots:{default:!0}})},$$slots:{default:!0}})},be=ze=>{Ve(ze,{name:"file",children:(_e,Te)=>{Hs(_e,{})},$$slots:{default:!0}})};H(te,ze=>{e(Se),k(Dr),M(()=>e(Se).type===Dr.AGENT_REQUESTED&&!e(Se).description)?ze(ue):ze(be,!1)});var Fe=l(le,2),Ae=o(Fe);se(Ae,{size:1,color:"neutral",children:(ze,_e)=>{var Te=R();ve(()=>xe(Te,(e(Se),M(()=>e(Se).path)))),s(ze,Te)},$$slots:{default:!0}}),s(ke,Ce)},"header-right":(ke,Pe)=>{var Ce=li(),le=o(Ce),te=o(le),ue=o(te);ha(ue,{get rule(){return e(Se)},onSave:async(Ae,ze)=>{await x().updateRuleContent({type:Ae,path:e(Se).path,content:e(Se).content,description:ze})}});var be=l(te,2);Je(be,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ae=>{Ae.stopPropagation(),I().openRule(e(Se).path)}},$$slots:{iconRight:(Ae,ze)=>{Ve(Ae,{slot:"iconRight",name:"external-link",children:(_e,Te)=>{Ds(_e,{})},$$slots:{default:!0}})}}});var Fe=l(be,2);Je(Fe,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ae=>{Ae.stopPropagation(),I().deleteRule(e(Se).path)}},$$slots:{iconRight:(Ae,ze)=>{Ve(Ae,{slot:"iconRight",name:"trash-2",children:(_e,Te)=>{sa(_e,{})},$$slots:{default:!0}})}}}),s(ke,Ce)}}})}),s(ne,he)};H(P,ne=>{n(),M(()=>n().length===0)?ne(F):ne(ee,!1)},K)};H(Q,P=>{c(),n(),M(()=>c()&&n().length===0)?P(Ie):P(De,!1)});var ye=l(Re,2),m=o(ye);Je(m,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>I().createRule()},children:(P,K)=>{var F=ci(),ee=o(F);Ve(ee,{slot:"iconLeft",name:"plus",children:(ne,he)=>{Pt(ne,{})},$$slots:{default:!0}}),s(P,F)},$$slots:{default:!0}});var C=l(m,2);Ze.Root(C,{get requestClose(){return e($)},set requestClose(P){u($,P)},get focusedIndex(){return e(y)},set focusedIndex(P){Bt(u(y,P),"$importFocusedIndex",r)},children:(P,K)=>{var F=hi(),ee=pe(F);Ze.Trigger(ee,{children:(he,me)=>{Je(he,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(fe,Se)=>{var ke=di(),Pe=o(ke);Ve(Pe,{slot:"iconLeft",name:"download",children:(le,te)=>{ss(le,{})},$$slots:{default:!0}});var Ce=l(Pe,2);Ve(Ce,{slot:"iconRight",name:"chevron-down",children:(le,te)=>{Rr(le,{})},$$slots:{default:!0}}),s(fe,ke)},$$slots:{default:!0}})},$$slots:{default:!0}});var ne=l(ee,2);Ze.Content(ne,{align:"start",side:"bottom",children:(he,me)=>{var fe=vi(),Se=pe(fe);et(Se,1,()=>D,Ce=>Ce.id,(Ce,le)=>{Ze.Item(Ce,{onSelect:()=>N(e(le)),children:(te,ue)=>{var be=R();ve(()=>xe(be,(e(le),M(()=>e(le).label)))),s(te,be)},$$slots:{default:!0}})});var ke=l(Se,2),Pe=Ce=>{var le=ui(),te=pe(le);Ze.Separator(te,{});var ue=l(te,2);Ze.Label(ue,{children:(be,Fe)=>{var Ae=R();ve(()=>xe(Ae,(h(),M(()=>h()!==void 0?D[h()].description:D[0])))),s(be,Ae)},$$slots:{default:!0}}),s(Ce,le)};H(ke,Ce=>{h()!==void 0&&Ce(Pe)}),s(he,fe)},$$slots:{default:!0}}),s(P,F)},$$slots:{default:!0},$$legacy:!0});var X=l(L,2),B=o(X);se(B,{class:"c-section-header",size:3,color:"primary",children:(P,K)=>{var F=R("User Guidelines");s(P,F)},$$slots:{default:!0}});var oe=l(B,2),G=l(o(oe)),$e=o(G);se($e,{size:1,weight:"regular",children:(P,K)=>{var F=R("Learn more");s(P,F)},$$slots:{default:!0}}),us(l(oe,2),{get userGuidelines(){return Y()},get userGuidelinesLengthLimit(){return O()},updateUserGuideline:U()});var Me=l(T,2);(function(P,K){Be(K,!1);const F=Qr();let ee=S(K,"show",8,!1),ne=S(K,"errorMessage",8,""),he=J(""),me=J(void 0),fe=J(!1);function Se(){e(he).trim()&&!e(fe)&&(u(fe,!0),F("create",e(he).trim()))}function ke(){e(fe)||(F("cancel"),u(he,""))}function Pe(Ce){e(fe)||(Ce.key==="Enter"?(Ce.preventDefault(),Se()):Ce.key==="Escape"&&(Ce.preventDefault(),ke()))}we(()=>(k(ee()),e(me)),()=>{ee()&&e(me)&&setTimeout(()=>{var Ce;return(Ce=e(me))==null?void 0:Ce.focus()},100)}),we(()=>(k(ee()),k(ne())),()=>{ee()&&!ne()||u(fe,!1)}),we(()=>(k(ee()),k(ne())),()=>{ee()||ne()||u(he,"")}),nt(),rt(),is(P,{get show(){return ee()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return e(fe)},get preventEscapeClose(){return e(fe)},$$events:{cancel:ke,keydown:function(te){e(fe)||te.detail.key==="Enter"&&(te.detail.preventDefault(),Se())}},body:te=>{var ue=Wo(),be=pe(ue);se(be,{size:2,color:"secondary",children:(_e,Te)=>{var Qe=R("Enter a name for the new rule file (e.g., architecture.md):");s(_e,Qe)},$$slots:{default:!0}});var Fe=l(be,2);At(Fe,{placeholder:"rule-name.md",get disabled(){return e(fe)},get value(){return e(he)},set value(_e){u(he,_e)},get textInput(){return e(me)},set textInput(_e){u(me,_e)},$$events:{keydown:Pe},$$legacy:!0});var Ae=l(Fe,2),ze=_e=>{jt(_e,{variant:"soft",color:"error",size:1,icon:Qe=>{Ve(Qe,{name:"circle-alert",children:(Ke,ht)=>{ar(Ke,{})},$$slots:{default:!0}})},children:(Qe,Ke)=>{var ht=R();ve(()=>xe(ht,ne())),s(Qe,ht)},$$slots:{icon:!0,default:!0}})};H(Ae,_e=>{ne()&&_e(ze)}),s(te,ue)},footer:te=>{var ue=Zo(),be=pe(ue);Je(be,{variant:"solid",color:"neutral",get disabled(){return e(fe)},$$events:{click:ke},children:(ze,_e)=>{var Te=R("Cancel");s(ze,Te)},$$slots:{default:!0}});var Fe=l(be,2);const Ae=Ne(()=>(e(he),e(fe),M(()=>!e(he).trim()||e(fe))));Je(Fe,{variant:"solid",color:"accent",get disabled(){return e(Ae)},get loading(){return e(fe)},$$events:{click:Se},children:(ze,_e)=>{var Te=R();ve(()=>xe(Te,e(fe)?"Creating...":"Create")),s(ze,Te)},$$slots:{default:!0}}),s(te,ue)},$$slots:{body:!0,footer:!0}}),je()})(Me,{get show(){return We(W,"$showCreateRuleDialog",r)},get errorMessage(){return We(b,"$createRuleError",r)},$$events:{create:function(P){I().handleCreateRuleWithName(P.detail)},cancel:function(){I().hideCreateRuleDialog()}}}),ai(l(Me,2),{get show(){return e(d)},get options(){return e(E)},get isLoading(){return e(j)},get errorMessage(){return e(p)},get successMessage(){return e(f)},$$events:{select:async function(P){const K=P.detail;try{u(j,!0),u(p,"");const F=await I().processAutoImportSelection(K);let ee=`Successfully imported ${F.importedRulesCount} rule${F.importedRulesCount!==1?"s":""} from ${K.label}`;F.duplicatesCount>0&&(ee+=`, ${F.duplicatesCount} duplicate${F.duplicatesCount!==1?"s":""} skipped`),F.totalAttempted>F.importedRulesCount+F.duplicatesCount&&(ee+=`, ${F.totalAttempted-F.importedRulesCount-F.duplicatesCount} failed`),u(f,ee),setTimeout(()=>{u(d,!1),u(f,"")},500)}catch(F){console.error("Failed to process auto-import selection:",F),u(p,"Failed to import rules. Please try again.")}finally{u(j,!1)}},cancel:function(){u(d,!1),u(p,""),u(f,"")}}}),s(g,re),je(),a()}var mi=lt("<svg><!></svg>"),fi=v('<div class="account-email svelte-wku0j5"><!> <!></div>'),$i=v("<!> Sign Out",1),Ci=v("<!> <!>",1);function wi(g,t){Be(t,!1);const[r,a]=pt(),n=()=>We(z,"$userEmailStore",r),c=J();let h=S(t,"onSignOut",8),A=J(!1);const z=Tt(Ft.key).getUserEmail();function w(){h()(),u(A,!0)}we(()=>n(),()=>{u(c,n())}),nt(),rt(),cs(g,{title:"",loading:!1,children:(i,Y)=>{var O=Ci(),ae=pe(O),ce=x=>{var I=fi(),V=o(I);se(V,{size:1,color:"secondary",children:(b,d)=>{var E=R("Signed in as");s(b,E)},$$slots:{default:!0}});var W=l(V,2);se(W,{size:1,weight:"medium",children:(b,d)=>{var E=R();ve(()=>xe(E,e(c))),s(b,E)},$$slots:{default:!0}}),s(x,I)};H(ae,x=>{e(c)&&x(ce)});var U=l(ae,2);Je(U,{get loading(){return e(A)},variant:"soft","data-testid":"sign-out-button",$$events:{click:w},children:(x,I)=>{var V=$i(),W=pe(V);Ve(W,{name:"log-out",children:(b,d)=>{(function(E,j){const p=zt(j,["children","$$slots","$$events","$$legacy"]);var f=mi();Ut(f,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...p}));var D=o(f);Zt(D,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),s(E,f)})(b,{})},$$slots:{default:!0}}),s(x,V)},$$slots:{default:!0}}),s(i,O)},$$slots:{default:!0}}),je(),a()}class yi{constructor(t,r,a){Ee(this,"_showCreateRuleDialog",Xe(!1));Ee(this,"_createRuleError",Xe(""));Ee(this,"_extensionClient");this._host=t,this._msgBroker=r,this._rulesModel=a;const n=new Yr;this._extensionClient=new es(t,r,n)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(t){if(t&&t.trim()){this._createRuleError.set("");try{const r=await this._rulesModel.createRule(t.trim());r&&r.path&&await this.openRule(r.path),this._extensionClient.reportAgentSessionEvent({eventName:mr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Xt.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const a=`Failed to create rule "${t.trim()}"`;this._createRuleError.set(a)}}else this.hideCreateRuleDialog()}async openRule(t){try{const r=await this._rulesModel.getWorkspaceRoot();js.includes(t)?this._extensionClient.openFile({repoRoot:r,pathName:t}):this._extensionClient.openFile({repoRoot:r,pathName:`${Ws}/${Zs}/${t}`})}catch(r){console.error("Failed to open rule:",r)}}async deleteRule(t){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(t)}catch(r){console.error("Failed to delete rule:",r)}}async selectFileToImport(){try{const t=await this._msgBroker.send({type:qe.triggerImportDialogRequest},1e5);if(t.data.selectedPaths&&t.data.selectedPaths.length>0){const r=await this._rulesModel.processSelectedPaths(t.data.selectedPaths);this._showImportNotification(r),this._reportSelectedImportMetrics(r)}}catch(t){console.error("Failed to import files:",t)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(t){const r=await this._rulesModel.processAutoImportSelection(t);return this._showImportNotification(r),this._reportAutoImportMetrics(r),r}_showImportNotification(t){let r;t.importedRulesCount===0?r=t.source?`No new rules imported from ${t.source}`:"No new rules imported":(r=`Successfully imported ${t.importedRulesCount} rule${t.importedRulesCount!==1?"s":""}`,t.duplicatesCount&&t.duplicatesCount>0&&(r+=` and skipped ${t.duplicatesCount} duplicate${t.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:r,type:t.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(t){const r=t.directoryOrFile==="directory"?Xt.selectedDirectory:(t.directoryOrFile,Xt.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:mr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:r,numFiles:t.importedRulesCount,source:""}}})}_reportAutoImportMetrics(t){this._extensionClient.reportAgentSessionEvent({eventName:mr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Xt.auto,numFiles:t.importedRulesCount,source:t.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var Si=v('<span slot="content"><!></span>');function bi(g,t){Be(t,!1);const[r,a]=pt(),n=()=>We(ge,"$guidelines",r),c=()=>We(j,"$settingsComponentSupported",r),h=()=>We(p,"$enableAgentMode",r),A=()=>We(y,"$terminalSettingsStore",r),z=J(),w=J(),i=J(),Y=J(),O=J(),ae=new Ft(Ye),ce=new Qt(Ye),U=new Ra(Ye),x=new zs(Ye),I=new Yr,V=new es(Ye,x,I),W=new Sr(x),b=new Wt(x),d=new Ks(x),E=new yi(Ye,x,d);x.registerConsumer(d),Yt(Sr.key,W),Yt(Wt.key,b),Yt(Ft.key,ae),ea(V),function(m){Yt(ds,m)}(ce);const j=ae.getSettingsComponentSupported(),p=ae.getEnableAgentMode(),f=ae.getEnableAgentSwarmMode(),D=ae.getHasEverUsedRemoteAgent();x.registerConsumer(ae),x.registerConsumer(ce),x.registerConsumer(U);const y=U.getTerminalSettings();let $=J();const N=[];let re=!0;function T(m,C){const X=window.setTimeout(()=>{re&&m()},C);return N.push(X),X}const L={handleMessageFromExtension(m){return m.data&&m.data.type===qe.navigateToSettingsSection?(m.data.data&&typeof m.data.data=="string"&&q(m.data.data),!0):!1}};function _(){const m=window.location.hash.substring(1);m&&q(m)}function q(m){u($,void 0);const C=document.getElementById(m);if(C){const B=C.closest("[data-section]"),oe=B==null?void 0:B.getAttribute("data-section");u($,oe??"tools")}else u($,"tools");function X(B=0,oe=20){const G=document.getElementById(m);G?(G.scrollIntoView({behavior:"smooth",block:"center"}),T(()=>{G.classList.remove("settings-section-highlight"),G.offsetHeight,G.classList.add("settings-section-highlight"),T(()=>{G.classList.remove("settings-section-highlight")},1050)},500)):B<oe&&T(()=>X(B+1,oe),200)}T(()=>X(),100)}x.registerConsumer(L),Kr(()=>{T(_,100);const m=()=>{_()};return window.addEventListener("hashchange",m),()=>{re=!1,N.forEach(clearTimeout),window.removeEventListener("hashchange",m)}});const Z=ae.getDisplayableTools(),ge=ae.getGuidelines();function de(m){const C=m.trim();return!(e(w)&&C.length>e(w))&&(ae.updateLocalUserGuidelines(C),Ye.postMessage({type:qe.updateUserGuidelines,data:m}),!0)}function ie(m){Ye.postMessage({type:qe.toolConfigStartOAuth,data:m}),ae.startPolling()}async function Re(m){await V.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${m.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&Ye.postMessage({type:qe.toolConfigRevokeAccess,data:{toolId:m.identifier}})}function Q(m){U.updateSelectedShell(m)}function Ie(m){U.updateStartupScript(m)}function De(m,C){Ye.postMessage({type:qe.toolApprovalConfigSetRequest,data:{toolId:m,approvalConfig:C}})}function ye(){Ye.postMessage({type:qe.signOut})}Cr(()=>{ae.dispose(),W.dispose(),b.dispose()}),ae.notifyLoaded(),Ye.postMessage({type:qe.getOrientationStatus}),Ye.postMessage({type:qe.settingsPanelLoaded}),we(()=>n(),()=>{var m;u(z,(m=n().userGuidelines)==null?void 0:m.contents)}),we(()=>n(),()=>{var m;u(w,(m=n().userGuidelines)==null?void 0:m.lengthLimit)}),we(()=>n(),()=>{var m,C;u(i,(C=(m=n().workspaceGuidelines)==null?void 0:m[0])==null?void 0:C.lengthLimit)}),we(()=>n(),()=>{var m,C;u(Y,((C=(m=n().workspaceGuidelines)==null?void 0:m[0])==null?void 0:C.contents)||"")}),we(()=>(c(),jr),()=>{u(O,[c().remoteTools?Gt("Tools","",vn,"section-tools"):void 0,c().userGuidelines&&!c().rules?Gt("User Guidelines","Guidelines for Augment Chat to follow.",Ho,"guidelines"):void 0,c().rules?Gt("Rules and User Guidelines","",Go,"guidelines"):void 0,c().workspaceContext?Gt("Context","",gn,"context"):void 0,Gt("Account","Manage your Augment account settings.",jr,"account")].filter(Boolean))}),we(()=>(e(O),e($)),()=>{var m;e(O).length>1&&!e($)&&u($,(m=e(O)[0])==null?void 0:m.id)}),nt(),rt(),ft("message",xr,function(...m){var C;(C=x.onMessageFromExtension)==null||C.apply(this,m)}),va.Root(g,{children:(m,C)=>{dn(m,{get items(){return e(O)},mode:"tree",class:"c-settings-navigation",get selectedId(){return e($)},$$slots:{content:(X,B)=>{var oe=Si();const G=Ne(()=>B.item);var $e=o(oe),Me=K=>{},P=(K,F)=>{var ee=he=>{ja(he,{})},ne=(he,me)=>{var fe=ke=>{var Pe=tt(),Ce=pe(Pe),le=ue=>{pi(ue,{get userGuidelines(){return e(z)},get userGuidelinesLengthLimit(){return e(w)},get workspaceGuidelinesLengthLimit(){return e(i)},get workspaceGuidelinesContent(){return e(Y)},updateUserGuideline:de,get rulesModel(){return d},get rulesController(){return E}})},te=ue=>{us(ue,{get userGuidelines(){return e(z)},get userGuidelinesLengthLimit(){return e(w)},updateUserGuideline:de})};H(Ce,ue=>{c(),M(()=>c().rules)?ue(le):ue(te,!1)}),s(ke,Pe)},Se=(ke,Pe)=>{var Ce=te=>{wi(te,{onSignOut:ye})},le=te=>{const ue=Ne(()=>(h(),c(),M(()=>h()&&c().mcpServerList))),be=Ne(()=>(h(),c(),M(()=>h()&&c().mcpServerImport)));Vo(te,{get tools(){return We(Z,"$displayableTools",r)},onAuthenticate:ie,onRevokeAccess:Re,onToolApprovalConfigChange:De,onMCPServerAdd:Fe=>ce.addServer(Fe),onMCPServerSave:Fe=>ce.updateServer(Fe),onMCPServerDelete:Fe=>ce.deleteServer(Fe),onMCPServerToggleDisable:Fe=>ce.toggleDisabledServer(Fe),onMCPServerJSONImport:Fe=>ce.importServersFromJSON(Fe),get isMCPEnabled(){return e(ue)},get isMCPImportEnabled(){return e(be)},get supportedShells(){return A(),M(()=>A().supportedShells)},get selectedShell(){return A(),M(()=>A().selectedShell)},get startupScript(){return A(),M(()=>A().startupScript)},onShellSelect:Q,onStartupScriptChange:Ie,get isTerminalEnabled(){return c(),M(()=>c().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return h()},get isSwarmModeFeatureFlagEnabled(){return We(f,"$enableAgentSwarmMode",r)},get hasEverUsedRemoteAgent(){return We(D,"$hasEverUsedRemoteAgent",r)}})};H(ke,te=>{k(e(G)),M(()=>{var ue;return((ue=e(G))==null?void 0:ue.id)==="account"})?te(Ce):te(le,!1)},Pe)};H(he,ke=>{k(e(G)),M(()=>{var Pe;return((Pe=e(G))==null?void 0:Pe.id)==="guidelines"})?ke(fe):ke(Se,!1)},me)};H(K,he=>{k(e(G)),M(()=>{var me;return((me=e(G))==null?void 0:me.id)==="context"})?he(ee):he(ne,!1)},F)};H($e,K=>{k(Mr),k(e(G)),M(()=>!Mr(e(G)))?K(Me):K(P,!1)}),s(X,oe)}}})},$$slots:{default:!0}}),je(),a()}(async function(){Ye&&Ye.initialize&&await Ye.initialize(),Ms(bi,{target:document.getElementById("app")})})();
