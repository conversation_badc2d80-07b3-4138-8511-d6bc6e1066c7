import{N as T,x as t,Z as V,y as n,z as d,B as f,u as p,a as X,r as Z,P as $,_ as j,Q as q,R as D,$ as E,b as o,X as F,S as G,V as J,C as K,a0 as L,a1 as O}from"./GuardedIcon-BFT2yJIo.js";import{B as U,b as W}from"./BaseTextInput-BaUpeUef.js";import"./IconButtonAugment-CR0fVrwD.js";var Y=n('<label class="c-textarea-label svelte-hohluk"> </label>'),B=n('<div class="c-textarea-label-container svelte-hohluk"><!> <!></div>'),ee=n("<textarea></textarea>"),te=n('<div class="c-textarea svelte-hohluk"><!> <!></div>');function le(m,e){T(e,!0);let c=t(e,"label",19,()=>{}),k=t(e,"variant",3,"surface"),y=t(e,"size",3,2),_=t(e,"color",19,()=>{}),l=t(e,"resize",3,"none"),i=t(e,"textInput",15),R=t(e,"type",3,"default"),w=t(e,"value",15),A=t(e,"id",19,()=>{}),C=t(e,"autoresize",3,!0),I=t(e,"focus",3,!1),S=t(e,"class",3,""),H=t(e,"outline",3,!0),M=Z(e,["$$slots","$$events","$$legacy","label","variant","size","color","resize","textInput","type","value","id","autoresize","topRightAction","focus","class","outline"]),z=D(()=>A()||`text-field-${Math.random().toString(36).substring(2,11)}`);V(()=>{I()&&i()&&i().focus()});var g=te(),b=p(g),N=s=>{var u=B(),a=p(u),r=v=>{var x=Y(),Q=p(x);K(()=>{L(x,"for",$(z)),O(Q,c())}),o(v,x)};d(a,v=>{c()&&v(r)});var h=f(a,2);G(h,()=>e.topRightAction??J),o(s,u)};d(b,s=>{(c()||e.topRightAction)&&s(N)});var P=f(b,2);U(P,{get type(){return R()},get variant(){return k()},get size(){return y()},get color(){return _()},get outline(){return H()},children:(s,u)=>{var a=ee();X(a,(r,h)=>({id:$(z),spellCheck:"false",class:`c-textarea__input c-base-text-input__input ${S()}`,...M,[q]:r,[j]:h}),[()=>({"c-textarea--resize-none":l()==="none","c-textarea--resize-both":l()==="both","c-textarea--resize-horizontal":l()==="horizontal","c-textarea--resize-vertical":l()==="vertical","c-textarea--autoresize":C()}),()=>({"--text-area-min-height-rows":e.rows?`${e.rows}lh`:"1lh","--text-area-max-height":e.maxHeight??"auto"})],"svelte-hohluk"),E(a,r=>i(r),()=>i()),W(a,w),o(s,a)},$$slots:{default:!0}}),o(m,g),F()}export{le as T};
