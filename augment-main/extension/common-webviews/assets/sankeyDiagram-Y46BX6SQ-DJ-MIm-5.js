import{_,p as ft,o as yt,s as pt,g as gt,b as dt,a as mt,c as tt,v as _t,d as R,aa as xt,x as kt,k as vt}from"./AugmentMessage-cgapx9im.js";import{o as bt}from"./ordinal-_rw2EY4v.js";import"./GuardedIcon-BFT2yJIo.js";import"./index-BzB60MCy.js";import"./preload-helper-Dv6uf1Os.js";import"./await-D3vig32v.js";import"./chat-model-context-39sqbIF3.js";import"./IconButtonAugment-CR0fVrwD.js";import"./partner-mcp-utils-DYgwDbFd.js";import"./index-DON3DCoY.js";import"./async-messaging-Bp70swAv.js";import"./chat-types-BDRYChZT.js";import"./file-paths-BcSg4gks.js";import"./isObjectLike-D6mfjXx_.js";import"./CardAugment-DVbbQqkH.js";import"./focusTrapStack-CDv9v5kQ.js";import"./TextFieldAugment-DfCJMerV.js";import"./BaseTextInput-BaUpeUef.js";import"./trash-ByjpApta.js";import"./index-C_brRns6.js";import"./expand-CPL6rEzo.js";import"./toggleHighContrast-C7wSWUJK.js";import"./index-CWns8XM2.js";import"./ButtonAugment-CWDjQYWT.js";import"./MaterialIcon-D9dP7dAZ.js";import"./lodash-DkRojHDE.js";import"./Filespan-9fd1tyrF.js";import"./OpenFileButton-CVOPVJP1.js";import"./index-B528snJk.js";import"./remote-agents-client-IyYiaWrE.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-RUDtUaBz.js";import"./SuccessfulButton-Btt1yYx9.js";import"./CopyButton-DSjqZJL1.js";import"./copy-oYDqgVJ5.js";import"./LanguageIcon-CJlkgv5n.js";import"./CollapseButtonAugment-Cnr_pz_w.js";import"./ellipsis-DBzJOEA0.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-b0Ugp2il.js";import"./message-broker-BygIEqPd.js";import"./file-type-utils-Zb3vtfL9.js";import"./TextAreaAugment-DIwZ8JEM.js";import"./clock-B1EQOiug.js";import"./augment-logo-CNPb11gr.js";import"./index-BuQDLh4_.js";import"./branch-BXbTQdeh.js";import"./index-JMsuML6t.js";import"./CalloutAugment-C9yL-4XM.js";import"./init-g68aIKmP.js";const wt=function(t){for(var e=t.length/6|0,r=new Array(e),s=0;s<e;)r[s]="#"+t.slice(6*s,6*++s);return r}("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function et(t,e){let r;if(e===void 0)for(const s of t)s!=null&&(r<s||r===void 0&&s>=s)&&(r=s);else{let s=-1;for(let o of t)(o=e(o,++s,t))!=null&&(r<o||r===void 0&&o>=o)&&(r=o)}return r}function lt(t,e){let r;if(e===void 0)for(const s of t)s!=null&&(r>s||r===void 0&&s>=s)&&(r=s);else{let s=-1;for(let o of t)(o=e(o,++s,t))!=null&&(r>o||r===void 0&&o>=o)&&(r=o)}return r}function Y(t,e){let r=0;if(e===void 0)for(let s of t)(s=+s)&&(r+=s);else{let s=-1;for(let o of t)(o=+e(o,++s,t))&&(r+=o)}return r}function Et(t){return t.target.depth}function ht(t,e){return t.sourceLinks.length?t.depth:e-1}function W(t){return function(){return t}}function it(t,e){return G(t.source,e.source)||t.index-e.index}function rt(t,e){return G(t.target,e.target)||t.index-e.index}function G(t,e){return t.y0-e.y0}function B(t){return t.value}function Lt(t){return t.index}function St(t){return t.nodes}function At(t){return t.links}function st(t,e){const r=t.get(e);if(!r)throw new Error("missing: "+e);return r}function ot({nodes:t}){for(const e of t){let r=e.y0,s=r;for(const o of e.sourceLinks)o.y0=r+o.width/2,r+=o.width;for(const o of e.targetLinks)o.y1=s+o.width/2,s+=o.width}}function Mt(){let t,e,r,s=0,o=0,y=1,k=1,i=24,f=8,c=Lt,d=ht,x=St,g=At,A=6;function v(){const n={nodes:x.apply(null,arguments),links:g.apply(null,arguments)};return function({nodes:l,links:h}){for(const[u,a]of l.entries())a.index=u,a.sourceLinks=[],a.targetLinks=[];const p=new Map(l.map((u,a)=>[c(u,a,l),u]));for(const[u,a]of h.entries()){a.index=u;let{source:b,target:m}=a;typeof b!="object"&&(b=a.source=st(p,b)),typeof m!="object"&&(m=a.target=st(p,m)),b.sourceLinks.push(a),m.targetLinks.push(a)}if(r!=null)for(const{sourceLinks:u,targetLinks:a}of l)u.sort(r),a.sort(r)}(n),function({nodes:l}){for(const h of l)h.value=h.fixedValue===void 0?Math.max(Y(h.sourceLinks,B),Y(h.targetLinks,B)):h.fixedValue}(n),function({nodes:l}){const h=l.length;let p=new Set(l),u=new Set,a=0;for(;p.size;){for(const b of p){b.depth=a;for(const{target:m}of b.sourceLinks)u.add(m)}if(++a>h)throw new Error("circular link");p=u,u=new Set}}(n),function({nodes:l}){const h=l.length;let p=new Set(l),u=new Set,a=0;for(;p.size;){for(const b of p){b.height=a;for(const{source:m}of b.targetLinks)u.add(m)}if(++a>h)throw new Error("circular link");p=u,u=new Set}}(n),function(l){const h=function({nodes:p}){const u=et(p,m=>m.depth)+1,a=(y-s-i)/(u-1),b=new Array(u);for(const m of p){const E=Math.max(0,Math.min(u-1,Math.floor(d.call(null,m,u))));m.layer=E,m.x0=s+E*a,m.x1=m.x0+i,b[E]?b[E].push(m):b[E]=[m]}if(e)for(const m of b)m.sort(e);return b}(l);t=Math.min(f,(k-o)/(et(h,p=>p.length)-1)),function(p){const u=lt(p,a=>(k-o-(a.length-1)*t)/Y(a,B));for(const a of p){let b=o;for(const m of a){m.y0=b,m.y1=b+m.value*u,b=m.y1+t;for(const E of m.sourceLinks)E.width=E.value*u}b=(k-b+t)/(a.length+1);for(let m=0;m<a.length;++m){const E=a[m];E.y0+=b*(m+1),E.y1+=b*(m+1)}C(a)}}(h);for(let p=0;p<A;++p){const u=Math.pow(.99,p),a=Math.max(1-u,(p+1)/A);I(h,u,a),T(h,u,a)}}(n),ot(n),n}function T(n,l,h){for(let p=1,u=n.length;p<u;++p){const a=n[p];for(const b of a){let m=0,E=0;for(const{source:S,value:U}of b.targetLinks){let nt=U*(b.layer-S.layer);m+=$(S,b)*nt,E+=nt}if(!(E>0))continue;let D=(m/E-b.y0)*l;b.y0+=D,b.y1+=D,P(b)}e===void 0&&a.sort(G),M(a,h)}}function I(n,l,h){for(let p=n.length-2;p>=0;--p){const u=n[p];for(const a of u){let b=0,m=0;for(const{target:D,value:S}of a.sourceLinks){let U=S*(D.layer-a.layer);b+=N(a,D)*U,m+=U}if(!(m>0))continue;let E=(b/m-a.y0)*l;a.y0+=E,a.y1+=E,P(a)}e===void 0&&u.sort(G),M(u,h)}}function M(n,l){const h=n.length>>1,p=n[h];L(n,p.y0-t,h-1,l),w(n,p.y1+t,h+1,l),L(n,k,n.length-1,l),w(n,o,0,l)}function w(n,l,h,p){for(;h<n.length;++h){const u=n[h],a=(l-u.y0)*p;a>1e-6&&(u.y0+=a,u.y1+=a),l=u.y1+t}}function L(n,l,h,p){for(;h>=0;--h){const u=n[h],a=(u.y1-l)*p;a>1e-6&&(u.y0-=a,u.y1-=a),l=u.y0-t}}function P({sourceLinks:n,targetLinks:l}){if(r===void 0){for(const{source:{sourceLinks:h}}of l)h.sort(rt);for(const{target:{targetLinks:h}}of n)h.sort(it)}}function C(n){if(r===void 0)for(const{sourceLinks:l,targetLinks:h}of n)l.sort(rt),h.sort(it)}function $(n,l){let h=n.y0-(n.sourceLinks.length-1)*t/2;for(const{target:p,width:u}of n.sourceLinks){if(p===l)break;h+=u+t}for(const{source:p,width:u}of l.targetLinks){if(p===n)break;h-=u}return h}function N(n,l){let h=l.y0-(l.targetLinks.length-1)*t/2;for(const{source:p,width:u}of l.targetLinks){if(p===n)break;h+=u+t}for(const{target:p,width:u}of n.sourceLinks){if(p===l)break;h-=u}return h}return v.update=function(n){return ot(n),n},v.nodeId=function(n){return arguments.length?(c=typeof n=="function"?n:W(n),v):c},v.nodeAlign=function(n){return arguments.length?(d=typeof n=="function"?n:W(n),v):d},v.nodeSort=function(n){return arguments.length?(e=n,v):e},v.nodeWidth=function(n){return arguments.length?(i=+n,v):i},v.nodePadding=function(n){return arguments.length?(f=t=+n,v):f},v.nodes=function(n){return arguments.length?(x=typeof n=="function"?n:W(n),v):x},v.links=function(n){return arguments.length?(g=typeof n=="function"?n:W(n),v):g},v.linkSort=function(n){return arguments.length?(r=n,v):r},v.size=function(n){return arguments.length?(s=o=0,y=+n[0],k=+n[1],v):[y-s,k-o]},v.extent=function(n){return arguments.length?(s=+n[0][0],y=+n[1][0],o=+n[0][1],k=+n[1][1],v):[[s,o],[y,k]]},v.iterations=function(n){return arguments.length?(A=+n,v):A},v}var K=Math.PI,Z=2*K,z=1e-6,It=Z-z;function H(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function ut(){return new H}function at(t){return function(){return t}}function Tt(t){return t[0]}function Pt(t){return t[1]}H.prototype=ut.prototype={constructor:H,moveTo:function(t,e){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,e){this._+="L"+(this._x1=+t)+","+(this._y1=+e)},quadraticCurveTo:function(t,e,r,s){this._+="Q"+ +t+","+ +e+","+(this._x1=+r)+","+(this._y1=+s)},bezierCurveTo:function(t,e,r,s,o,y){this._+="C"+ +t+","+ +e+","+ +r+","+ +s+","+(this._x1=+o)+","+(this._y1=+y)},arcTo:function(t,e,r,s,o){t=+t,e=+e,r=+r,s=+s,o=+o;var y=this._x1,k=this._y1,i=r-t,f=s-e,c=y-t,d=k-e,x=c*c+d*d;if(o<0)throw new Error("negative radius: "+o);if(this._x1===null)this._+="M"+(this._x1=t)+","+(this._y1=e);else if(x>z)if(Math.abs(d*i-f*c)>z&&o){var g=r-y,A=s-k,v=i*i+f*f,T=g*g+A*A,I=Math.sqrt(v),M=Math.sqrt(x),w=o*Math.tan((K-Math.acos((v+x-T)/(2*I*M)))/2),L=w/M,P=w/I;Math.abs(L-1)>z&&(this._+="L"+(t+L*c)+","+(e+L*d)),this._+="A"+o+","+o+",0,0,"+ +(d*g>c*A)+","+(this._x1=t+P*i)+","+(this._y1=e+P*f)}else this._+="L"+(this._x1=t)+","+(this._y1=e)},arc:function(t,e,r,s,o,y){t=+t,e=+e,y=!!y;var k=(r=+r)*Math.cos(s),i=r*Math.sin(s),f=t+k,c=e+i,d=1^y,x=y?s-o:o-s;if(r<0)throw new Error("negative radius: "+r);this._x1===null?this._+="M"+f+","+c:(Math.abs(this._x1-f)>z||Math.abs(this._y1-c)>z)&&(this._+="L"+f+","+c),r&&(x<0&&(x=x%Z+Z),x>It?this._+="A"+r+","+r+",0,1,"+d+","+(t-k)+","+(e-i)+"A"+r+","+r+",0,1,"+d+","+(this._x1=f)+","+(this._y1=c):x>z&&(this._+="A"+r+","+r+",0,"+ +(x>=K)+","+d+","+(this._x1=t+r*Math.cos(o))+","+(this._y1=e+r*Math.sin(o))))},rect:function(t,e,r,s){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)+"h"+ +r+"v"+ +s+"h"+-r+"Z"},toString:function(){return this._}};var Nt=Array.prototype.slice;function Ot(t){return t.source}function Ct(t){return t.target}function Dt(t,e,r,s,o){t.moveTo(e,r),t.bezierCurveTo(e=(e+s)/2,r,e,o,s,o)}function $t(){return function(t){var e=Ot,r=Ct,s=Tt,o=Pt,y=null;function k(){var i,f=Nt.call(arguments),c=e.apply(this,f),d=r.apply(this,f);if(y||(y=i=ut()),t(y,+s.apply(this,(f[0]=c,f)),+o.apply(this,f),+s.apply(this,(f[0]=d,f)),+o.apply(this,f)),i)return y=null,i+""||null}return k.source=function(i){return arguments.length?(e=i,k):e},k.target=function(i){return arguments.length?(r=i,k):r},k.x=function(i){return arguments.length?(s=typeof i=="function"?i:at(+i),k):s},k.y=function(i){return arguments.length?(o=typeof i=="function"?i:at(+i),k):o},k.context=function(i){return arguments.length?(y=i??null,k):y},k}(Dt)}function zt(t){return[t.source.x1,t.y0]}function jt(t){return[t.target.x0,t.y1]}var J=function(){var t=_(function(i,f,c,d){for(c=c||{},d=i.length;d--;c[i[d]]=f);return c},"o"),e=[1,9],r=[1,10],s=[1,5,10,12],o={trace:_(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:_(function(i,f,c,d,x,g,A){var v=g.length-1;switch(x){case 7:const T=d.findOrCreateNode(g[v-4].trim().replaceAll('""','"')),I=d.findOrCreateNode(g[v-2].trim().replaceAll('""','"')),M=parseFloat(g[v].trim());d.addLink(T,I,M);break;case 8:case 9:case 11:this.$=g[v];break;case 10:this.$=g[v-1]}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:e,20:r},{1:[2,6],7:11,10:[1,12]},t(r,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(s,[2,8]),t(s,[2,9]),{19:[1,16]},t(s,[2,11]),{1:[2,1]},{1:[2,5]},t(r,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:e,20:r},{15:18,16:7,17:8,18:e,20:r},{18:[1,19]},t(r,[2,3]),{12:[1,20]},t(s,[2,10]),{15:21,16:7,17:8,18:e,20:r},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:_(function(i,f){if(!f.recoverable){var c=new Error(i);throw c.hash=f,c}this.trace(i)},"parseError"),parse:_(function(i){var f=this,c=[0],d=[],x=[null],g=[],A=this.table,v="",T=0,I=0,M=g.slice.call(arguments,1),w=Object.create(this.lexer),L={yy:{}};for(var P in this.yy)Object.prototype.hasOwnProperty.call(this.yy,P)&&(L.yy[P]=this.yy[P]);w.setInput(i,L.yy),L.yy.lexer=w,L.yy.parser=this,w.yylloc===void 0&&(w.yylloc={});var C=w.yylloc;g.push(C);var $=w.options&&w.options.ranges;function N(){var S;return typeof(S=d.pop()||w.lex()||1)!="number"&&(S instanceof Array&&(S=(d=S).pop()),S=f.symbols_[S]||S),S}typeof L.yy.parseError=="function"?this.parseError=L.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,_(function(S){c.length=c.length-2*S,x.length=x.length-S,g.length=g.length-S},"popStack"),_(N,"lex");for(var n,l,h,p,u,a,b,m,E={};;){if(l=c[c.length-1],this.defaultActions[l]?h=this.defaultActions[l]:(n==null&&(n=N()),h=A[l]&&A[l][n]),h===void 0||!h.length||!h[0]){var D="";for(u in m=[],A[l])this.terminals_[u]&&u>2&&m.push("'"+this.terminals_[u]+"'");D=w.showPosition?"Parse error on line "+(T+1)+`:
`+w.showPosition()+`
Expecting `+m.join(", ")+", got '"+(this.terminals_[n]||n)+"'":"Parse error on line "+(T+1)+": Unexpected "+(n==1?"end of input":"'"+(this.terminals_[n]||n)+"'"),this.parseError(D,{text:w.match,token:this.terminals_[n]||n,line:w.yylineno,loc:C,expected:m})}if(h[0]instanceof Array&&h.length>1)throw new Error("Parse Error: multiple actions possible at state: "+l+", token: "+n);switch(h[0]){case 1:c.push(n),x.push(w.yytext),g.push(w.yylloc),c.push(h[1]),n=null,I=w.yyleng,v=w.yytext,T=w.yylineno,C=w.yylloc;break;case 2:if(a=this.productions_[h[1]][1],E.$=x[x.length-a],E._$={first_line:g[g.length-(a||1)].first_line,last_line:g[g.length-1].last_line,first_column:g[g.length-(a||1)].first_column,last_column:g[g.length-1].last_column},$&&(E._$.range=[g[g.length-(a||1)].range[0],g[g.length-1].range[1]]),(p=this.performAction.apply(E,[v,I,T,L.yy,h[1],x,g].concat(M)))!==void 0)return p;a&&(c=c.slice(0,-1*a*2),x=x.slice(0,-1*a),g=g.slice(0,-1*a)),c.push(this.productions_[h[1]][0]),x.push(E.$),g.push(E._$),b=A[c[c.length-2]][c[c.length-1]],c.push(b);break;case 3:return!0}}return!0},"parse")},y=function(){return{EOF:1,parseError:_(function(i,f){if(!this.yy.parser)throw new Error(i);this.yy.parser.parseError(i,f)},"parseError"),setInput:_(function(i,f){return this.yy=f||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:_(function(){var i=this._input[0];return this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i,i.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:_(function(i){var f=i.length,c=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-f),this.offset-=f;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var x=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===d.length?this.yylloc.first_column:0)+d[d.length-c.length].length-c[0].length:this.yylloc.first_column-f},this.options.ranges&&(this.yylloc.range=[x[0],x[0]+this.yyleng-f]),this.yyleng=this.yytext.length,this},"unput"),more:_(function(){return this._more=!0,this},"more"),reject:_(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:_(function(i){this.unput(this.match.slice(i))},"less"),pastInput:_(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:_(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:_(function(){var i=this.pastInput(),f=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+f+"^"},"showPosition"),test_match:_(function(i,f){var c,d,x;if(this.options.backtrack_lexer&&(x={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(x.yylloc.range=this.yylloc.range.slice(0))),(d=i[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=d.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:d?d[d.length-1].length-d[d.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],c=this.performAction.call(this,this.yy,this,f,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var g in x)this[g]=x[g];return!1}return!1},"test_match"),next:_(function(){if(this.done)return this.EOF;var i,f,c,d;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var x=this._currentRules(),g=0;g<x.length;g++)if((c=this._input.match(this.rules[x[g]]))&&(!f||c[0].length>f[0].length)){if(f=c,d=g,this.options.backtrack_lexer){if((i=this.test_match(c,x[g]))!==!1)return i;if(this._backtrack){f=!1;continue}return!1}if(!this.options.flex)break}return f?(i=this.test_match(f,x[d]))!==!1&&i:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:_(function(){var i=this.next();return i||this.lex()},"lex"),begin:_(function(i){this.conditionStack.push(i)},"begin"),popState:_(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:_(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:_(function(i){return(i=this.conditionStack.length-1-Math.abs(i||0))>=0?this.conditionStack[i]:"INITIAL"},"topState"),pushState:_(function(i){this.begin(i)},"pushState"),stateStackSize:_(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:_(function(i,f,c,d){switch(c){case 0:return this.pushState("csv"),4;case 1:return 10;case 2:return 5;case 3:return 12;case 4:return this.pushState("escaped_text"),18;case 5:return 20;case 6:return this.popState("escaped_text"),18;case 7:return 19}},"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:!1},escaped_text:{rules:[6,7],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:!0}}}}();function k(){this.yy={}}return o.lexer=y,_(k,"Parser"),k.prototype=o,o.Parser=k,new k}();J.parser=J;var V=J,q=[],Q=[],X=new Map,Ft=_(()=>{q=[],Q=[],X=new Map,kt()},"clear"),j,Ut=(j=class{constructor(e,r,s=0){this.source=e,this.target=r,this.value=s}},_(j,"SankeyLink"),j),Wt=_((t,e,r)=>{q.push(new Ut(t,e,r))},"addLink"),F,Gt=(F=class{constructor(e){this.ID=e}},_(F,"SankeyNode"),F),Vt=_(t=>{t=vt.sanitizeText(t,tt());let e=X.get(t);return e===void 0&&(e=new Gt(t),X.set(t,e),Q.push(e)),e},"findOrCreateNode"),Xt=_(()=>Q,"getNodes"),qt=_(()=>q,"getLinks"),Qt=_(()=>({nodes:Q.map(t=>({id:t.ID})),links:q.map(t=>({source:t.source.ID,target:t.target.ID,value:t.value}))}),"getGraph"),Rt={nodesMap:X,getConfig:_(()=>tt().sankey,"getConfig"),getNodes:Xt,getLinks:qt,getGraph:Qt,addLink:Wt,findOrCreateNode:Vt,getAccTitle:mt,setAccTitle:dt,getAccDescription:gt,setAccDescription:pt,getDiagramTitle:yt,setDiagramTitle:ft,clear:Ft},O,ct=(O=class{static next(e){return new O(e+ ++O.count)}constructor(e){this.id=e,this.href=`#${e}`}toString(){return"url("+this.href+")"}},_(O,"Uid"),O.count=0,O),Yt={left:function(t){return t.depth},right:function(t,e){return e-1-t.height},center:function(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?lt(t.sourceLinks,Et)-1:0},justify:ht},Bt=_(function(t,e,r,s){const{securityLevel:o,sankey:y}=tt(),k=_t.sankey;let i;o==="sandbox"&&(i=R("#i"+e));const f=R(o==="sandbox"?i.nodes()[0].contentDocument.body:"body"),c=o==="sandbox"?f.select(`[id="${e}"]`):R(`[id="${e}"]`),d=(y==null?void 0:y.width)??k.width,x=(y==null?void 0:y.height)??k.width,g=(y==null?void 0:y.useMaxWidth)??k.useMaxWidth,A=(y==null?void 0:y.nodeAlignment)??k.nodeAlignment,v=(y==null?void 0:y.prefix)??k.prefix,T=(y==null?void 0:y.suffix)??k.suffix,I=(y==null?void 0:y.showValues)??k.showValues,M=s.db.getGraph(),w=Yt[A];Mt().nodeId(n=>n.id).nodeWidth(10).nodePadding(10+(I?15:0)).nodeAlign(w).extent([[0,0],[d,x]])(M);const L=bt(wt);c.append("g").attr("class","nodes").selectAll(".node").data(M.nodes).join("g").attr("class","node").attr("id",n=>(n.uid=ct.next("node-")).id).attr("transform",function(n){return"translate("+n.x0+","+n.y0+")"}).attr("x",n=>n.x0).attr("y",n=>n.y0).append("rect").attr("height",n=>n.y1-n.y0).attr("width",n=>n.x1-n.x0).attr("fill",n=>L(n.id));const P=_(({id:n,value:l})=>I?`${n}
${v}${Math.round(100*l)/100}${T}`:n,"getText");c.append("g").attr("class","node-labels").attr("font-family","sans-serif").attr("font-size",14).selectAll("text").data(M.nodes).join("text").attr("x",n=>n.x0<d/2?n.x1+6:n.x0-6).attr("y",n=>(n.y1+n.y0)/2).attr("dy",(I?"0":"0.35")+"em").attr("text-anchor",n=>n.x0<d/2?"start":"end").text(P);const C=c.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(M.links).join("g").attr("class","link").style("mix-blend-mode","multiply"),$=(y==null?void 0:y.linkColor)??"gradient";if($==="gradient"){const n=C.append("linearGradient").attr("id",l=>(l.uid=ct.next("linearGradient-")).id).attr("gradientUnits","userSpaceOnUse").attr("x1",l=>l.source.x1).attr("x2",l=>l.target.x0);n.append("stop").attr("offset","0%").attr("stop-color",l=>L(l.source.id)),n.append("stop").attr("offset","100%").attr("stop-color",l=>L(l.target.id))}let N;switch($){case"gradient":N=_(n=>n.uid,"coloring");break;case"source":N=_(n=>L(n.source.id),"coloring");break;case"target":N=_(n=>L(n.target.id),"coloring");break;default:N=$}C.append("path").attr("d",$t().source(zt).target(jt)).attr("stroke",N).attr("stroke-width",n=>Math.max(1,n.width)),xt(void 0,c,0,g)},"draw"),Kt={draw:Bt},Zt=_(t=>t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,`
`).trim(),"prepareTextForParsing"),Ht=V.parse.bind(V);V.parse=t=>Ht(Zt(t));var Kn={parser:V,db:Rt,renderer:Kt};export{Kn as diagram};
