<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Smart Diff View</title>
    <script nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <script type="module" crossorigin src="./assets/diff-view-DDV8YYt9.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/GuardedIcon-BFT2yJIo.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/initialize-DgduSj_U.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-K1OaxmPU.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-Bp70swAv.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CR0fVrwD.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-DVbbQqkH.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-BygIEqPd.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-BDRYChZT.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/file-type-utils-Zb3vtfL9.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-D6mfjXx_.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CDv9v5kQ.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-BaUpeUef.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/TextFieldAugment-DfCJMerV.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/index-DON3DCoY.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-39sqbIF3.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/index-C_brRns6.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-b0Ugp2il.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/monaco-render-utils-DfwV7QLY.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CWDjQYWT.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-C9yL-4XM.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-uRBrTmWU.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/index-CWns8XM2.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-9fd1tyrF.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-D9dP7dAZ.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/TextCombo-CTaxEruE.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-Bn06n7i7.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-CNPb11gr.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-TUnsm-bW.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/GuardedIcon-DzY30p2i.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-tJfihrx1.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BLuo5Hnm.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BDZh6BVQ.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/TextFieldAugment-Cx_jUV0g.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/index-EyL516td.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-Fl6YbyF2.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-D1H89BMr.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/TextCombo-DYKrB3yI.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings--oeZmuWk.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/diff-view-COwC345D.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
