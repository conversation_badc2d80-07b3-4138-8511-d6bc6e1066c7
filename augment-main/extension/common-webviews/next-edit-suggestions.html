<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Next Edit Suggestions</title>
    <script nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <script type="module" crossorigin src="./assets/next-edit-suggestions-Cdo-88TW.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/GuardedIcon-BFT2yJIo.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/initialize-DgduSj_U.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-K1OaxmPU.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/await-D3vig32v.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BzB60MCy.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-CNPb11gr.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="modulepreload" crossorigin href="./assets/index-CWns8XM2.js" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/GuardedIcon-DzY30p2i.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
    <link rel="stylesheet" crossorigin href="./assets/next-edit-suggestions-Df4-uiQ1.css" nonce="nonce-n1ZiLa2xGVrC5O2iWIzcFA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
