# Migration Guide: Integrating Enhanced Features

This guide helps you integrate the Wingman-inspired enhancements into your existing VS Code extension.

## 🔄 Step-by-Step Migration

### 1. **Install New Dependencies**

```bash
npm install diff@^5.1.0 node-fetch@^3.3.2
```

The enhanced system requires these additional packages for diff generation and HTTP requests.

### 2. **Update Extension Entry Point**

Replace your current `extension.ts` with the enhanced version:

```typescript
// Option 1: Complete replacement
// Rename current extension.ts to extension-legacy.ts
// Rename enhanced-extension.ts to extension.ts

// Option 2: Gradual integration
// Keep both systems running side by side
// Users can choose which interface to use
```

### 3. **Configure New Views**

The enhanced system adds a new activity bar container. Update your `package.json`:

```json
{
  "viewsContainers": {
    "activitybar": [
      {
        "id": "enhanced-ai",
        "title": "Enhanced AI Assistant", 
        "icon": "$(robot)"
      }
    ]
  }
}
```

### 4. **Provider Migration**

Your existing provider configuration will work, but you can enhance it:

```typescript
// Before (your current system)
const aiProvider = new AIProviderFactory().create(providerType);

// After (enhanced system)
const aiProvider = createAIProvider(providerType);
const validation = await validateProvider(aiProvider);
```

### 5. **Tool System Integration**

Replace your current agent system with the enhanced version:

```typescript
// Before
const agentSystem = new ProfessionalAgentSystem(/*...*/);

// After  
const agentSystem = new EnhancedAgentSystem(workspace, aiProvider, outputChannel);
```

## 🛠️ Integration Options

### **Option A: Complete Migration**

**Pros:**
- Get all enhanced features immediately
- Clean, modern architecture
- Better user experience

**Cons:**
- Requires testing of all functionality
- Users need to adapt to new interface

**Steps:**
1. Backup current `src/` directory
2. Replace with enhanced files
3. Update `package.json` dependencies and commands
4. Test thoroughly
5. Deploy

### **Option B: Side-by-Side Integration**

**Pros:**
- Zero risk to existing functionality
- Users can choose their preferred interface
- Gradual migration path

**Cons:**
- Maintains two codebases temporarily
- Larger extension size

**Steps:**
1. Keep existing files unchanged
2. Add enhanced files with different names
3. Register both sets of commands
4. Let users choose via settings
5. Deprecate old system over time

### **Option C: Feature-by-Feature Migration**

**Pros:**
- Minimal risk
- Incremental improvement
- Easy to test each feature

**Cons:**
- Takes longer to get full benefits
- More complex integration

**Steps:**
1. Start with enhanced AI providers
2. Add enhanced tools one by one
3. Migrate to LangGraph architecture
4. Update UI last

## 🔧 Configuration Migration

### **Existing Settings (Preserved)**
```json
{
  "ollama-assistant.serverUrl": "http://localhost:11434",
  "ollama-assistant.defaultModel": "codellama",
  "ollama-assistant.chatModel": "llama2", 
  "ollama-assistant.aiProvider": "ollama",
  "ollama-assistant.geminiApiKey": "your-key"
}
```

### **New Settings (Optional)**
```json
{
  "enhanced-ai.autoApproveFileOps": false,
  "enhanced-ai.autoApproveSafeCommands": true,
  "enhanced-ai.maxConversationHistory": 50,
  "enhanced-ai.enableStreaming": true
}
```

## 🧪 Testing Checklist

### **Core Functionality**
- [ ] Provider switching (Ollama ↔ Gemini)
- [ ] Basic chat functionality
- [ ] Code completion
- [ ] File operations
- [ ] Command execution

### **Enhanced Features**
- [ ] Real-time streaming
- [ ] Approval workflows
- [ ] Diff generation
- [ ] Tool validation
- [ ] Error handling
- [ ] State persistence

### **UI/UX**
- [ ] Chat interface loads correctly
- [ ] Provider badge updates
- [ ] Approval buttons work
- [ ] Streaming indicators show
- [ ] Error messages display properly

## 🚨 Common Issues and Solutions

### **Issue: LangChain Dependencies**
```bash
# Error: Cannot resolve @langchain/langgraph
npm install @langchain/langgraph@^0.2.72
```

### **Issue: TypeScript Errors**
```bash
# Add to tsconfig.json
{
  "compilerOptions": {
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  }
}
```

### **Issue: Webview Not Loading**
```typescript
// Ensure media files are included in package
"files": [
  "out/**/*",
  "media/**/*"
]
```

### **Issue: Provider Validation Fails**
```typescript
// Check provider configuration
const config = vscode.workspace.getConfiguration('ollama-assistant');
console.log('Current config:', config);
```

## 📦 Deployment Strategy

### **Development Phase**
1. Set up enhanced system in development
2. Test with both providers
3. Validate all tool operations
4. Check UI responsiveness

### **Beta Release**
1. Deploy as beta version
2. Enable enhanced features via setting
3. Collect user feedback
4. Fix any issues

### **Production Release**
1. Make enhanced system default
2. Keep legacy system as fallback
3. Provide migration documentation
4. Monitor for issues

### **Deprecation**
1. Mark legacy commands as deprecated
2. Show migration prompts to users
3. Remove legacy code in future version

## 🎯 Recommended Approach

For your project, I recommend **Option B: Side-by-Side Integration**:

1. **Week 1**: Add enhanced files alongside existing ones
2. **Week 2**: Test enhanced system thoroughly
3. **Week 3**: Deploy with both systems available
4. **Week 4**: Collect feedback and iterate
5. **Month 2**: Make enhanced system default
6. **Month 3**: Deprecate legacy system

This approach minimizes risk while maximizing the benefits of the enhanced architecture.

## 🔗 Key Files to Focus On

### **High Priority**
- `src/services/enhanced-ai-provider.ts` - Better provider architecture
- `src/core/tools/enhanced-tools.ts` - Professional tool system
- `src/providers/enhanced-chat-provider.ts` - Modern chat interface

### **Medium Priority**  
- `src/core/enhanced-agent-system.ts` - LangGraph integration
- `media/enhanced-chat.css` - Modern UI styles
- `media/enhanced-chat.js` - Interactive functionality

### **Low Priority**
- `ENHANCED_FEATURES.md` - Documentation
- `MIGRATION_GUIDE.md` - This guide

## 🎉 Expected Benefits

After migration, you'll have:

- **Professional-grade architecture** matching Wingman AI
- **Better error handling** and validation
- **Modern UI** with real-time streaming
- **Enhanced safety** with approval workflows
- **Extensible design** for future features
- **Multi-provider support** with seamless switching
- **Production-ready code** with comprehensive testing

The enhanced system transforms your extension from a simple chat interface into a sophisticated AI coding assistant that rivals commercial solutions.