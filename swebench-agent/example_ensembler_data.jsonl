{"id": "problem-1", "instruction": "Add a function to calculate the factorial of a number in the math_utils.py file.", "diffs": ["@@ -10,3 +10,10 @@ def square(x):\n     \"\"\"\n     return x * x\n \n+def factorial(n):\n+    \"\"\"\n+    Calculate the factorial of a non-negative integer n.\n+    \"\"\"\n+    if n < 0:\n+        raise ValueError(\"Factorial is not defined for negative numbers\")\n+    return 1 if n == 0 else n * factorial(n - 1)\n", "@@ -10,3 +10,12 @@ def square(x):\n     \"\"\"\n     return x * x\n \n+def factorial(n):\n+    \"\"\"\n+    Calculate the factorial of a non-negative integer n.\n+    \"\"\"\n+    if n < 0:\n+        raise ValueError(\"Factorial is not defined for negative numbers\")\n+    result = 1\n+    for i in range(1, n + 1):\n+        result *= i\n+    return result\n", "@@ -10,3 +10,9 @@ def square(x):\n     \"\"\"\n     return x * x\n \n+def factorial(n):\n+    \"\"\"\n+    Calculate the factorial of a non-negative integer n.\n+    \"\"\"\n+    import math\n+    return math.factorial(n)\n"], "eval_outcomes": [{"is_success": true}, {"is_success": false}, {"is_success": false}]}
{"id": "problem-2", "instruction": "Fix the bug in the sort_list function that causes it to fail when the list contains None values.", "diffs": ["@@ -25,7 +25,10 @@ def sort_list(items, reverse=False):\n     \"\"\"\n     Sort a list of items in ascending or descending order.\n     \"\"\"\n-    return sorted(items, reverse=reverse)\n+    # Filter out None values before sorting\n+    filtered_items = [item for item in items if item is not None]\n+    \n+    return sorted(filtered_items, reverse=reverse)\n \n def merge_lists(list1, list2):\n     \"\"\"\n", "@@ -25,7 +25,13 @@ def sort_list(items, reverse=False):\n     \"\"\"\n     Sort a list of items in ascending or descending order.\n     \"\"\"\n-    return sorted(items, reverse=reverse)\n+    # Handle None values by using a custom key function\n+    def sort_key(x):\n+        if x is None:\n+            return float('-inf')  # Place None values at the beginning\n+        return x\n+    \n+    return sorted(items, key=sort_key, reverse=reverse)\n \n def merge_lists(list1, list2):\n     \"\"\"\n", "@@ -25,7 +25,11 @@ def sort_list(items, reverse=False):\n     \"\"\"\n     Sort a list of items in ascending or descending order.\n     \"\"\"\n-    return sorted(items, reverse=reverse)\n+    try:\n+        return sorted(items, reverse=reverse)\n+    except TypeError:\n+        # If sorting fails due to None values, filter them out and try again\n+        return sorted([item for item in items if item is not None], reverse=reverse)\n \n def merge_lists(list1, list2):\n     \"\"\"\n"], "eval_outcomes": [{"is_success": true}, {"is_success": false}, {"is_success": false}]}
{"id": "problem-3", "instruction": "Implement a function to check if a string is a valid email address.", "diffs": ["@@ -45,3 +45,12 @@ def is_palindrome(text):\n     cleaned_text = ''.join(c.lower() for c in text if c.isalnum())\n     return cleaned_text == cleaned_text[::-1]\n \n+def is_valid_email(email):\n+    \"\"\"\n+    Check if a string is a valid email address.\n+    \"\"\"\n+    import re\n+    \n+    # Simple regex pattern for email validation\n+    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'\n+    return bool(re.match(pattern, email))\n", "@@ -45,3 +45,19 @@ def is_palindrome(text):\n     cleaned_text = ''.join(c.lower() for c in text if c.isalnum())\n     return cleaned_text == cleaned_text[::-1]\n \n+def is_valid_email(email):\n+    \"\"\"\n+    Check if a string is a valid email address.\n+    \"\"\"\n+    if not isinstance(email, str):\n+        return False\n+    \n+    # Check for @ symbol and proper domain structure\n+    if '@' not in email:\n+        return False\n+    \n+    username, domain = email.split('@', 1)\n+    \n+    # Basic validation rules\n+    return len(username) > 0 and '.' in domain and len(domain.split('.')[-1]) >= 2\n", "@@ -45,3 +45,15 @@ def is_palindrome(text):\n     cleaned_text = ''.join(c.lower() for c in text if c.isalnum())\n     return cleaned_text == cleaned_text[::-1]\n \n+def is_valid_email(email):\n+    \"\"\"\n+    Check if a string is a valid email address.\n+    \"\"\"\n+    try:\n+        # Use the email validation from the email package\n+        from email.utils import parseaddr\n+        from email.validator import validate_email\n+        validate_email(email)\n+        return True\n+    except:\n+        return False\n"], "eval_outcomes": [{"is_success": true}, {"is_success": false}, {"is_success": false}]}
