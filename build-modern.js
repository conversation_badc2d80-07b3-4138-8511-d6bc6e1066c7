const esbuild = require('esbuild');
const path = require('path');

const buildOptions = {
    entryPoints: ['src/extension.ts'],
    bundle: true,
    outfile: 'out/extension.js',
    external: ['vscode'],
    format: 'cjs',
    platform: 'node',
    target: 'node16',
    sourcemap: process.argv.includes('--watch'),
    minify: !process.argv.includes('--watch'),
    define: {
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production')
    },
    plugins: [
        {
            name: 'typescript-resolver',
            setup(build) {
                build.onResolve({ filter: /\.ts$/ }, (args) => {
                    if (args.path.startsWith('./') || args.path.startsWith('../')) {
                        return {
                            path: path.resolve(args.resolveDir, args.path),
                            namespace: 'file'
                        };
                    }
                });
            }
        }
    ]
};

async function build() {
    try {
        console.log('🔨 Building Modern AI Assistant...');
        
        if (process.argv.includes('--watch')) {
            console.log('👀 Watching for changes...');
            const ctx = await esbuild.context(buildOptions);
            await ctx.watch();
        } else {
            await esbuild.build(buildOptions);
            console.log('✅ Build completed successfully!');
        }
    } catch (error) {
        console.error('❌ Build failed:', error);
        process.exit(1);
    }
}

build();